.blog-admin-container {
  padding: 24px;
  background-color: #f9fafb;
  border-radius: 8px;
  width: 100%;
}

.blog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.blog-title {
  font-size: 24px;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.add-blog-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: #8057ff;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 10px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.add-blog-button:hover {
  background-color: #7046e0;
}

.filters-container {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 16px;
}

.search-container {
  position: relative;
  width: 100%;
  max-width: 400px;
  display: flex;
  align-items: center;
  margin-right: 10px;
}

.search-input-wrapper {
  position: relative;
  width: 100%;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 14px;
  top: 50%;
  transform: translateY(-50%);
  color: #6b7280;
  font-size: 14px;
  z-index: 1;
  pointer-events: none;
}

.search-input {
  width: 100%;
  padding: 10px 10px 10px 40px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  font-size: 14px;
  text-indent: 5px;
  box-sizing: border-box;
  min-width: 200px;
}

.filter-select {
  padding: 10px 16px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  font-size: 14px;
  background-color: white;
}

.table-container {
  overflow-x: auto;
}

.blogs-table {
  width: 100%;
  border-collapse: collapse;
  background-color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  overflow: hidden;
}

.blogs-table th {
  padding: 12px 16px;
  text-align: left;
  font-size: 14px;
  font-weight: 600;
  color: #4b5563;
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.blogs-table td {
  padding: 12px 16px;
  font-size: 14px;
  color: #1f2937;
  border-bottom: 1px solid #e5e7eb;
}

.blog-title-row {
  font-weight: 500;
  margin-bottom: 4px;
}

.blog-description {
  color: #6b7280;
  font-size: 13px;
}

.featured-badge {
  display: inline-flex;
  align-items: center;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  background-color: #fef3c7;
  color: #b45309;
  margin-right: 6px;
  margin-bottom: 4px;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 10px;
  border-radius: 9999px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.active {
  background-color: #dcfce7;
  color: #166534;
}

.status-badge.inactive {
  background-color: #fee2e2;
  color: #991b1b;
}

.views-container {
  display: flex;
  align-items: center;
}

.views-icon {
  margin-right: 6px;
  color: #6b7280;
}

.action-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 4px;
  border: none;
  background-color: transparent;
  color: #6b7280;
  cursor: pointer;
  margin: 0 4px;
}

.edit-button {
  color: #2563eb;

}

.edit-button:hover {
  background-color: #bfdbfe;
}

.delete-button {
  color: #dc2626;
}

.delete-button:hover {
  background-color: #fecaca;
}

.toggle-button {
  transition: background-color 0.2s;
}

.toggle-button.active {
  color: #991b1b;
  background-color: #fee2e2;
}

.toggle-button.active:hover {
  background-color: #fecaca;
}

.toggle-button.inactive {
  color: #166534;
  background-color: #dcfce7;
}

.toggle-button.inactive:hover {
  background-color: #bbf7d0;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #64748b;
  text-align: center;
  gap: 16px;
}

.loading-container svg {
  color: #3b82f6;
}

.error-message {
  background-color: #fef2f2;
  color: #b91c1c;
  border: 1px solid #fee2e2;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.error-message svg {
  font-size: 18px;
}

.no-blogs-message {
  text-align: center;
  padding: 32px;
  color: #64748b;
  font-size: 14px;
}

.action-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.add-blog-button:disabled {
  background-color: #a0aec0;
  cursor: not-allowed;
}

.category-badge {
  display: inline-block;
  padding: 4px 8px;
  background-color: #f3f4f6;
  color: #4b5563;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Style the blog header actions container */
.blog-header-actions {
  display: flex;
  gap: 10px;
}

/* Style the refresh button */
.refresh-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background-color: #f8f9fa;
  color: #495057;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.refresh-button:hover {
  background-color: #e9ecef;
}

.refresh-button:active {
  background-color: #dae0e5;
}

.refresh-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Add a spin animation for the refresh icon when loading */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.refresh-button svg {
  font-size: 14px;
}

.loading .refresh-button svg {
  animation: spin 1s linear infinite;
}