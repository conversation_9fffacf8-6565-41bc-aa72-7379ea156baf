/* Complete Updated JobBlog.css */

/* Separator bar for page */
.page-separator {
  position: fixed;
  top: 80px;
  left: 0;
  width: 100%;
  height: 50px;
  background-color: #FFF4E9;
  z-index: 1100;
}

:root {
    --primary-color: #7457d9;
    --secondary-color: #f25f80;
    --text-color: #333;
    --light-text: #777;
    --bg-color: #fff;
    --light-bg: #f8f8f8;
    --border-color: #eee;
    --box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    --radius: 8px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
  }
  
  
  body {
    color: var(--text-color);
    line-height: 1.6;
    background-color: var(--bg-color);
  }
  
  img {
    max-width: 100%;
    height: auto;
    display: block;
  }
  
  button {
    cursor: pointer;
    border: none;
    background: none;
    font-family: inherit;
  }
  
  a {
    text-decoration: none;
    color: inherit;
  }
  
  ul {
    list-style: none;
  }
  

.job-blog-container {
  max-width: 1400px;
  margin: 120px auto;
  padding: 0 20px;
}

/* Layout */
.job-blog {
  max-width: 1200px;
  margin: 0 auto;
  padding: 150px var(--spacing-md) 0 var(--spacing-md); /* Adjusted top padding for navbar + separator */
}

.ad-banner-top {
  display: flex;
  gap: var(--spacing-md);
  margin: var(--spacing-lg) 0;
}
  
  .ad-container {
    flex: 1;
    background-color: var(--primary-color);
    color: white;
    text-align: center;
    padding: var(--spacing-lg);
    border-radius: var(--radius);
    font-size: 24px;
    opacity: 0.7;
  }
  
  .breadcrumb {
    padding: var(--spacing-md) var(--spacing-lg);
    background: linear-gradient(90deg, #fff7f2 0%, #fef3ec 100%);
    color: var(--light-text);
    margin-bottom: var(--spacing-lg);
    border-radius: 12px;
    border-left: 4px solid var(--primary-color);
    font-size: 14px;
    font-weight: 500;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }
  
  .blog-content {
    margin-bottom: var(--spacing-xl);
  }

  .blog-hero {
  background: linear-gradient(135deg, #f9f2ff 0%, #ffefef 100%);
  padding: 80px 20px;
  text-align: center;
  border-radius: 20px;
  margin-bottom: 40px;
  position: relative;
  overflow: hidden;
  border: 1px solid #e2e8f0;
}

.blog-hero::before {
   content: "";
  position: absolute;
  top: -50px;
  right: -50px;
  width: 200px;
  height: 200px;
  background: rgba(134, 87, 243, 0.1);
  border-radius: 50%;
  z-index: 0;
}

.blog-hero::after {
  content: "";
  position: absolute;
  bottom: -100px;
  left: -100px;
  width: 300px;
  height: 300px;
  background: rgba(255, 107, 107, 0.1);
  border-radius: 50%;
  z-index: 0;
}

.hero-content {
  position: relative;
  z-index: 1;
  max-width: 800px;
  margin: 0 auto;
}

.hero-content h1 {
  font-size: 2.5rem;
  color: #000;
  font-weight: 700;
  margin-bottom: 15px;
}

.hero-content p {
  font-size: 1.1rem;
  color: #555;
  opacity: 0.9;
  margin-bottom: 30px;
}
  
.blog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
}

.blog-header h1 {
  font-size: 24px;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.blog-header h2 {
  font-size: 20px;
  font-weight: 400;
  color: var(--light-text);
  margin-bottom: 0;
  line-height: 1.4;
}

.blog-content-wrapper {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.blog-layout {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 0px;
}

/* Articles Main Section */
.articles-main {
  background-color: var(--bg-white);
  border-radius: var(--radius-lg);
  padding: 30px;
  box-shadow: var(--shadow-sm);
}

/* Category Tabs */
.category-tabs {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 30px;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 20px;
}

.tab-btn {
padding: 10px 20px;
border-radius: 20px;
font-size: 0.9rem;
font-weight: 500;
  background: #f5f7ff;
  color: #555;
transition: var(--transition);
}

.tab-btn:hover {
background: #e6e9f7;
}

.tab-btn.active {
background-color: var(--primary-color);
color: white;
border-color: var(--primary-color);
}

/* Articles Grid */
.articles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 30px;
}

.meta-info {
  display: flex;
  align-items: center;
  font-size: 0.8rem;
  color: var(--text-light);
  bottom: 0;
}

.dot-separator {
  margin: 0 8px;
}

.card-title {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 12px;
  color: var(--text-dark);
  line-height: 1.4;
}

.card-excerpt {
  font-size: 0.95rem;
  color: var(--text-medium);
  margin-bottom: 20px;
  line-height: 1.5;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.read-more-btn {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 0;
  font-size: 0.9rem;
  font-weight: 500;
  border-radius: 20px;
  /* background-color: var(--primary-color); */
  color: #000;
  transition: var(--transition);
}

.read-more-btn .arrow-icon {
  font-size: 0.8rem;
  transition: transform 0.3s ease;
}

.blog-card:hover .read-more-btn .arrow-icon {
  transform: translateX(3px);
}

.bookmark-icon {
  color: var(--text-light);
  transition: var(--transition);
}

.bookmark-icon:hover {
  color: var(--secondary-color);
}

/* No Results */
.no-results {
  text-align: center;
  padding: 60px 20px;
}

.no-results h3 {
  font-size: 1.5rem;
  margin-bottom: 10px;
  color: var(--text-dark);
}

.no-results p {
  color: var(--text-medium);
  font-size: 1rem;
}

/* Pagination */
.pagination-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  margin-top: 40px;
}
.pagination-btn {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: var(--bg-white);
  color: var(--text-medium);
  border: 1px solid var(--border-color);
  transition: var(--transition);
}

.pagination-btn:hover:not(.disabled) {
  background-color: rgba(108, 92, 231, 0.1);
  color: var(--primary-color);
}

.pagination-btn.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.pagination-btn.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Sidebar */
.blog-sidebar {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.sidebar-widget {
  background-color: var(--bg-white);
  border-radius: var(--radius-md);
  padding: 25px;
  box-shadow: var(--shadow-sm);
}

.widget-title {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 20px;
  color: var(--text-dark);
  position: relative;
  padding-bottom: 10px;
}

.widget-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 40px;
  height: 3px;
  background-color: var(--primary-color);
}

/* Latest Articles */
.latest-articles {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.latest-item {
  display: flex;
  gap: 15px;
  align-items: center;
  transition: var(--transition);
  padding: 10px;
  border-radius: var(--radius-sm);
    cursor: pointer;
}

.latest-item:hover {
  background-color: rgba(108, 92, 231, 0.05);
}

.latest-image {
  width: 80px;
  height: 80px;
  min-width: 80px;
  border-radius: var(--radius-sm);
  overflow: hidden;
}

.latest-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
} 

.latest-content h4 {
  font-size: 0.95rem;
  font-weight: 500;
  margin-bottom: 5px;
  color: var(--text-dark);
  line-height: 1.4;
}

.latest-date {
  font-size: 0.8rem;
  color: var(--text-light);
}

/* Sidebar Promo */
.sidebar-promo {
  border-radius: var(--radius-md);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  transition: var(--transition);
}

.sidebar-promo:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-md);
}

.sidebar-promo img {
  width: 100%;
  height: auto;
  display: block;
}

/* Loading and Error States */
.loading-overlay {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  gap: 20px;
}

.loading-overlay .spinner {
  font-size: 2rem;
  color: var(--primary-color);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-notification {
color: rgb(231, 76, 60);
    background-color: rgb(253, 242, 242);
    border: 1px solid rgb(245, 198, 203);
    border-radius: 8px;
    padding: 12px 16px;
    margin: 16px auto;
    text-align: center;
    font-size: 14px;
    max-width: 400px;
    box-shadow: rgba(231, 76, 60, 0.1) 0px 2px 4px;

}

/* Responsive Design */
@media (max-width: 992px) {
  .blog-layout {
    grid-template-columns: 1fr;
  }
  
  .blog-sidebar {
    grid-row: 1;
    margin-bottom: 40px;
  }
  
  .hero-content h1 {
    font-size: 2.2rem;
  }
}

@media (max-width: 768px) {
  .blog-hero {
    padding: 80px 20px;
  }
  
  .hero-content h1 {
    font-size: 1.8rem;
  }
  
  .hero-content .subtitle {
    font-size: 1rem;
  }
  
  .articles-main {
    padding: 20px;
  }
  
  .sidebar-widget {
    padding: 20px;
  }
}

@media (max-width: 576px) {
  .category-tabs {
    justify-content: center;
  }
  
  .articles-grid {
    grid-template-columns: 1fr;
  }
  
  .pagination-controls {
    flex-wrap: wrap;
  }
}

/* Animation Enhancements */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.blog-card {
  animation: fadeIn 0.5s ease-out forwards;
  cursor: pointer;
}

/* Delay animations for staggered effect */
.articles-grid .blog-card:nth-child(1) { animation-delay: 0.1s; }
.articles-grid .blog-card:nth-child(2) { animation-delay: 0.2s; }
.articles-grid .blog-card:nth-child(3) { animation-delay: 0.3s; }
.articles-grid .blog-card:nth-child(4) { animation-delay: 0.4s; }
.articles-grid .blog-card:nth-child(5) { animation-delay: 0.5s; }
.articles-grid .blog-card:nth-child(6) { animation-delay: 0.6s; }


.category-badge {
  position: absolute;
  top: 15px;
  right: 15px;
  background-color: var(--primary-color);
  color: white;
  padding: 5px 12px;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  z-index: 1;
}

.card-content {
  padding: 10px;
  display: flex;
  flex-direction: column;
  align-items: start;
  justify-content: end;
}


.blog-main {
display: grid;
grid-template-columns: 2fr 1fr;
gap: var(--spacing-xl);
}

/* Blog Posts */
.blog-posts {
display: flex;
flex-direction: column;
gap: var(--spacing-xl);
}

.post-card {
background-color: var(--bg-color);
border-radius: var(--radius);
overflow: hidden;
box-shadow: var(--box-shadow);
}

.post-card.large {
display: grid;
grid-template-columns: 1fr 1fr;
}

.post-card.large .post-image {
height: 100%;
overflow: hidden;
display: flex;
align-items: center;
}

.post-card.large .post-image img {
width: 100%;
height: 100%;
object-fit: cover;
display: block;
}

.post-content {
padding: var(--spacing-lg);
}

.post-meta {
display: flex;
justify-content: space-between;
align-items: center;
margin-bottom: var(--spacing-md);
color: var(--light-text);
font-size: 14px;
}

.author-info {
display: flex;
align-items: center;
gap: var(--spacing-sm);
}

.author-avatar {
width: 30px;
height: 30px;
border-radius: 50%;
object-fit: cover;
}

.post-title {
margin-bottom: var(--spacing-md);
font-size: 20px;
line-height: 1.4;
}

.large .post-title {
font-size: 24px;
}

.post-excerpt {
color: var(--light-text);
margin-bottom: var(--spacing-lg);
font-size: 15px;
}

.post-footer {
display: flex;
justify-content: space-between;
align-items: center;
}

.read-more {
padding: var(--spacing-sm) var(--spacing-md);
color: white;
background-color: var(--primary-color);
border-radius: 20px;
font-size: 14px;
border: none;
cursor: pointer;
}

.read-more:hover {
background-color: #5f43c0;
}





.even-row {
background: #ffffff;
}

.odd-row {
background: #faf7ff;
}

