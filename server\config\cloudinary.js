const cloudinary = require('cloudinary').v2;

// Configure Cloudinary if environment variables are set
if (process.env.CLOUDINARY_CLOUD_NAME && 
    process.env.CLOUDINARY_API_KEY && 
    process.env.CLOUDINARY_API_SECRET) {
  
  cloudinary.config({
    cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
    api_key: process.env.CLOUDINARY_API_KEY,
    api_secret: process.env.CLOUDINARY_API_SECRET
  });
  
  console.log('Cloudinary configured successfully');
} else {
  console.log('Cloudinary not configured - image upload will use local storage');
}

// Export the configured cloudinary instance
module.exports = cloudinary; 