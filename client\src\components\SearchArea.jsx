import React, { useState, useEffect } from "react";
import { useNavigate, useSearchParams } from 'react-router-dom';
import '../css/SearchArea.css';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faLocationDot, faBriefcase, faAngleDown, faSearch, faPlus, faMinus } from '@fortawesome/free-solid-svg-icons';
import { useLocation } from '../contexts/LocationContext';
import { useJobFilter } from '../contexts/JobFilterContext';

const SearchArea = () => {
    const navigate = useNavigate();
    const [searchParams, setSearchParams] = useSearchParams();
    const [jobTitle, setJobTitle] = useState("");
    const [showSalaryDropdown, setShowSalaryDropdown] = useState(false);
    const [displayedSalary, setDisplayedSalary] = useState("Salary range");

    // Use shared location context
    const { selectedLocation: location, setSelectedLocation: setLocation, districts } = useLocation();
    
    // Use shared job filter context
    const { 
        jobType, 
        minSalary, 
        maxSalary, 
        updateJobType, 
        updateSearchKeyword, 
        updateSalaryRange 
    } = useJobFilter();

    // Sync salary values and job type from URL parameters
    useEffect(() => {
        const minSal = searchParams.get('minSal');
        const maxSal = searchParams.get('maxSal');
        
        // Update displayed salary text if both values are from URL
        if (minSal !== null || maxSal !== null) {
            const displayMin = minSal || "0";
            const displayMax = maxSal || "1000000";
            setDisplayedSalary(`${displayMin} - ${displayMax}`);
        }
    }, [searchParams]);

    // Function to perform search with current filter values
    const performSearch = () => {
        // Update search keyword in context
        updateSearchKeyword(jobTitle);
        
        // Create search parameters
        const searchParams = new URLSearchParams();
        
        if (jobTitle.trim()) {
            searchParams.set('search', jobTitle.trim());
        }
        if (jobType && jobType !== '') {
            searchParams.set('jobType', jobType);
        }
        if (location) {
            searchParams.set('location', location);
        }
        if (minSalary && minSalary !== '0') {
            searchParams.set('minSal', minSalary);
        }
        if (maxSalary && maxSalary !== '1000000') {
            searchParams.set('maxSal', maxSalary);
        }
        
        // Navigate to JobBoard with search parameters
        navigate(`/browse?${searchParams.toString()}`);
    };

    // Track if component has mounted to avoid initial search trigger
    const [hasMounted, setHasMounted] = useState(false);
    
    // Set mounted flag after initial render
    useEffect(() => {
        setHasMounted(true);
    }, []);

    // Handle job type change
    const handleJobTypeChange = (e) => {
        updateJobType(e.target.value);
    };

    // Handle location change
    const handleLocationChange = (e) => {
        const newLocation = e.target.value;
        setLocation(newLocation);
        
        // Update URL parameters to sync with JobBoard
        const newSearchParams = new URLSearchParams(searchParams);
        
        if (newLocation && newLocation !== '') {
            newSearchParams.set('location', newLocation);
        } else {
            newSearchParams.delete('location');
        }
        
        // Update the URL without navigation
        setSearchParams(newSearchParams);
    };

    const handleSalaryUpdate = () => {
        // Update salary range in context
        updateSalaryRange(minSalary, maxSalary);
        
        // Update displayed text
        const salaryText = `${minSalary} - ${maxSalary}`;
        setDisplayedSalary(salaryText);
        
        // Close dropdown
        setShowSalaryDropdown(false);
        
        // If we're on the browse page, apply the search immediately
        if (window.location.pathname === '/browse') {
            performSearch();
        }
    };

    const handleSearch = () => {
        performSearch();
    };

    return (
        <div className="SearchArea">
            <h1 className="job-title-large">There Are 5500+ Jobs <br /> Here For You!</h1>
            <p className="job-subtitle">Discover your next career move, freelance gig, or internship</p>
            <div className="job-search-box">
                <div className="search-input">
                    <span className="search-icon">
                        <FontAwesomeIcon icon={faSearch} />
                    </span>
                    <input
                        type="text"
                        placeholder="UI/UX Designer"
                        value={jobTitle}
                        onChange={(e) => setJobTitle(e.target.value)}
                    />
                </div>

                <div className="select-wrapper job-type search-input">
                    <span className="search-icon">
                        <FontAwesomeIcon icon={faBriefcase}  />
                    </span>     
                    <div className="custom-select">
                        <select
                            value={jobType}
                            onChange={handleJobTypeChange}
                        >
                            <option value="">All Jobs</option>
                            <option value="Full time">Full time</option>
                            <option value="Part time">Part time</option>
                            <option value="Contract">Contract</option>
                            <option value="Internship">Internship</option>
                            <option value="Remote">Remote</option>
                            <option value="Freelance">Freelance</option>
                        </select>
                        <div className="select-arrow">
                            <FontAwesomeIcon icon={faAngleDown} />
                        </div>
                    </div>
                </div>                
                <div className="select-wrapper location search-input">
                    <span className="search-icon">
                         <FontAwesomeIcon icon={faLocationDot} />
                    </span>     
                    <div className="custom-select">
                        <select
                            value={location}
                            onChange={handleLocationChange}
                        >
                            <option value="">All Locations</option>
                            {districts.map(district => (
                                <option key={district} value={district}>{district}</option>
                            ))}
                        </select>
                        <div className="select-arrow">
                            <FontAwesomeIcon icon={faAngleDown} />
                        </div>
                    </div>
                </div>

                <div className=" salary-section">
                    <button
                        className="salary-toggle"
                        onClick={() => setShowSalaryDropdown(!showSalaryDropdown)}
                        type="button"
                    >
                        <span className="salary-icon">
                            <FontAwesomeIcon icon={faPlus} />
                        </span>
                        {displayedSalary}
                        <div className="select-arrow">
                            <FontAwesomeIcon icon={faAngleDown} />
                        </div>
                    </button>
                    {showSalaryDropdown && (
                        <div className="salary-dropdown">
                            <div className="salary-options">
                                <div className="salary-input">
                                    <label>Min Salary</label>
                                    <div className="input-with-spinner">
                                        <input
                                            type="number"
                                            value={minSalary}
                                            onChange={(e) => {
                                                const value = e.target.value;
                                                // Only update if value is valid or empty
                                                updateSalaryRange(value, maxSalary);
                                            }}
                                            min="0"
                                            onBlur={() => {
                                                // Ensure valid value on blur
                                                if (minSalary === '' || isNaN(parseInt(minSalary))) {
                                                    updateSalaryRange('0', maxSalary);
                                                }
                                            }}
                                        />
                                        <div className="spinner-buttons">
                                            <button 
                                                onClick={() => {
                                                    const currentValue = parseInt(minSalary) || 0;
                                                    updateSalaryRange((currentValue + 100).toString(), maxSalary);
                                                }}
                                                className="spinner-up"
                                                type="button"
                                            >
                                                <FontAwesomeIcon icon={faPlus} size="xs" />
                                            </button>
                                            <button 
                                                onClick={() => {
                                                    const currentValue = parseInt(minSalary) || 0;
                                                    updateSalaryRange(Math.max(0, currentValue - 100).toString(), maxSalary);
                                                }}
                                                className="spinner-down"
                                                type="button"
                                            >
                                                <FontAwesomeIcon icon={faMinus} size="xs" />
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div className="salary-input">
                                    <label>Max Salary</label>
                                    <div className="input-with-spinner">
                                        <input
                                            type="number"
                                            value={maxSalary}
                                            onChange={(e) => {
                                                const value = e.target.value;
                                                // Only update if value is valid or empty
                                                updateSalaryRange(minSalary, value);
                                            }}
                                            min="0"
                                            onBlur={() => {
                                                // Ensure valid value on blur
                                                if (maxSalary === '' || isNaN(parseInt(maxSalary))) {
                                                    updateSalaryRange(minSalary, '1000000');
                                                }
                                            }}
                                        />
                                        <div className="spinner-buttons">
                                            <button 
                                                onClick={() => {
                                                    const currentValue = parseInt(maxSalary) || 0;
                                                    updateSalaryRange(minSalary, (currentValue + 100).toString());
                                                }}
                                                className="spinner-up"
                                                type="button"
                                            >
                                                <FontAwesomeIcon icon={faPlus} size="xs" />
                                            </button>
                                            <button 
                                                onClick={() => {
                                                    const currentValue = parseInt(maxSalary) || 0;
                                                    updateSalaryRange(minSalary, Math.max(0, currentValue - 100).toString());
                                                }}
                                                className="spinner-down"
                                                type="button"
                                            >
                                                <FontAwesomeIcon icon={faMinus} size="xs" />
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <button
                                className="update-button"
                                onClick={handleSalaryUpdate}
                                type="button"
                            >
                                Update
                            </button>
                        </div>
                    )}
                </div>

                <button className="find-button" type="button" onClick={handleSearch}>Find now</button>
            </div>
        </div>
    );
};

export default SearchArea;
