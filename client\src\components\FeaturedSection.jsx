/* eslint-disable jsx-a11y/img-redundant-alt */
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import '../css/FeaturedSection.css';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faFacebookF, 
  faTwitter, 
  faInstagram, 
  faYoutube,
  faLinkedin,
  faTiktok,
  faFacebook, 
} from '@fortawesome/free-brands-svg-icons';
import { 
  faChevronLeft, 
  faChevronRight, 
  faShareNodes, 
  faCalendarAlt,
  faSpinner
} from '@fortawesome/free-solid-svg-icons';
import ApiService from '../services/apiService';

const FeaturedSection = () => {
  const [blogs, setBlogs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const navigate = useNavigate();

  // Function to handle blog navigation
  const handleBlogClick = (blogId) => {
    navigate(`/blogs/${blogId}`);
  };

  useEffect(() => {
    const fetchBlogs = async () => {
      try {
        setLoading(true);
        const response = await ApiService.blogs.getAll();
        
        // Get the latest 3 blogs and sort by newest first
        const latestBlogs = response.data
          .sort((a, b) => new Date(b.posted_date) - new Date(a.posted_date))
          .slice(0, 3);
        
        setBlogs(latestBlogs);
        setError(null);
      } catch (err) {
        console.error("Error fetching blogs:", err);
        setError("Failed to load blogs");
      } finally {
        setLoading(false);
      }
    };

    fetchBlogs();
  }, []);

  const formatDate = (dateString) => {
    if (!dateString) return 'No date';
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        day: 'numeric',
        month: 'long',
        year: 'numeric'
      });
    } catch (err) {
      return 'Invalid date';
    }
  };

  const getDefaultImage = () => {
    return 'https://images.unsplash.com/photo-1506744038136-46273834b3fb?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80';
  };

  if (loading) {
    return (
      <div className="featured-container">
        <div className="loading-container" style={{ textAlign: 'center', padding: '50px' }}>
          <FontAwesomeIcon icon={faSpinner} spin size="2x" />
          <p>Loading featured blogs...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="featured-container">
        <div className="error-container" style={{ textAlign: 'center', padding: '50px' }}>
          <p>Error: {error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="featured-container">
      <div className="featured-section">
        <div className="featured-header">
          <h2>Featured Blogs</h2>
          <div className="featured-underline"></div>
          <div className="navigation-buttons">
          </div>
        </div>
        
        <div className="featured-content">
          {/* Main featured article */}
          {blogs.length > 0 && (
            <div className="featured-main-article" onClick={() => handleBlogClick(blogs[0].blog_id)} style={{ cursor: 'pointer' }}>
              <div className="article-image-container">
                <img 
                  src={blogs[0].image_url || getDefaultImage()} 
                  alt={blogs[0].blog_title || 'Featured article'} 
                  className="article-image"
                  style={{ width: "100%", height: "320px", objectFit: "cover" }}
                />
                <div className="article-meta">
                  <span className="article-date">
                    <FontAwesomeIcon icon={faCalendarAlt} className="calendar-icon" />
                    {formatDate(blogs[0].posted_date)}
                  </span>
                </div>
                <h3 className="article-title">{blogs[0].blog_title}</h3>
              </div>
            </div>
          )}
          
          {/* Other featured articles */}
          <div className="featured-articles">
            {blogs.slice(1).map((blog, index) => (
              <div key={blog.blog_id} className="article-card" onClick={() => handleBlogClick(blog.blog_id)} style={{ cursor: 'pointer' }}>
                <div className="article-image-container">
                  <img 
                    src={blog.image_url || getDefaultImage()} 
                    alt={blog.blog_title || 'Article image'} 
                    className="article-image"
                    style={{ width: "100%", height: "180px", objectFit: "cover",
                      backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.7)), url('https://images.unsplash.com/photo-1552664730-d307ca884978?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80')`,
                     }}
                  />
                </div>
                <div className="article-info" >
                  <div className="article-meta">
                    <span className="article-date">
                      <FontAwesomeIcon icon={faCalendarAlt} className="calendar-icon" />
                      {formatDate(blog.posted_date)}
                    </span>
                  </div>
                  <h3 className="article-title">{blog.blog_title}</h3>
                </div>
              </div>
            ))}
            
            {/* Show placeholder if less than 3 blogs */}
            {blogs.length < 3 && Array.from({ length: 3 - blogs.length }).map((_, index) => (
              <div key={`placeholder-${index}`} className="article-card" >
                <div className="article-image-container" >
                  <img 
                    src={getDefaultImage()} 
                    alt="Placeholder article" 
                    className="article-image"
                    style={{ width: "100%", height: "180px", objectFit: "cover"}}
                  />
                </div>
                <div className="article-info">
                  <div className="article-meta">
                    <span className="article-date">
                      <FontAwesomeIcon icon={faCalendarAlt} className="calendar-icon" />
                      Coming Soon
                    </span>
                  </div>
                  <h3 className="article-title">More articles coming soon...</h3>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
      
      {/* Social Feed Sidebar */}
      <div className="social-feed">
        <h3>Social Feed</h3>
        
      <div className="social-media-item" title='Click to like our page'>
        <a href="https://www.facebook.com/profile.php?id=61564913969342" target="_blank" rel="noopener noreferrer">
          <div className="social-icon facebook">
            <FontAwesomeIcon icon={faFacebookF} />
          </div>
          <div className="social-stats">
            <span className="count">400+</span>
            <span className="stat-label">Followers</span>
          </div>
        </a>
      </div>

        <div className="social-media-item" title='Click to follow us'>
        <a href="https://www.instagram.com/jobpagelk/" target="_blank" rel="noopener noreferrer">
          <div className="social-icon instagram">
            <FontAwesomeIcon icon={faInstagram} />
          </div>
          <div className="social-stats">
            <span className="count">50+</span>
            <span className="stat-label">Followers</span>
          </div>
        </a>
      </div>

      <div className="social-media-item" title='Click to follow us'>
        <a href="https://www.linkedin.com/company/jobpage-lk" target="_blank" rel="noopener noreferrer">
          <div className="social-icon linkedin">
            <FontAwesomeIcon icon={faLinkedin} />
          </div>
          <div className="social-stats">
            <span className="count">100+</span>
            <span className="stat-label">Followers</span>
          </div>
        </a>
      </div>

      {/* <div className="social-media-item" title='Click to subscribe us'>
        <a href="https://www.youtube.com/@jobpage_rakiyapituwa" target="_blank" rel="noopener noreferrer">
          <div className="social-icon youtube">
            <FontAwesomeIcon icon={faYoutube} />
          </div>
          <div className="social-stats">
            <span className="count">22.8k</span>
            <span className="stat-label">Subscribers</span>
          </div>
        </a>
      </div> */}

      <div className="social-media-item" title='Click to follow us'>
        <a href="https://www.tiktok.com/@jobpage.lk" target="_blank" rel="noopener noreferrer">
          <div className="social-icon tiktok">
            <FontAwesomeIcon icon={faTiktok} />
          </div>
          <div className="social-stats">
            <span className="count">500+</span>
            <span className="stat-label">Followers</span>
          </div>
        </a>
      </div>
        
        <div className="promo-banner" style={{
          backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.7)), url('https://images.unsplash.com/photo-1552664730-d307ca884978?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80')`,
          backgroundSize: 'cover',
          backgroundPosition: 'center'
        }}>
          <div className="promo-content">
            <p>Start reading all our blogs<br />and fuel your curiosity!</p>
            <a href="/blogs">
              <button className="purchase-btn">Read Now</button>
            </a>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FeaturedSection;
