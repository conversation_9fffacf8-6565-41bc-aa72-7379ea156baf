import api from './api';

const companyService = {
  getAllCompanies: async () => {
    try {
      const response = await api.get('/companies');
      return response.data;
    } catch (error) {
      console.error('Error fetching companies:', error);
      throw error;
    }
  },

  getCompanyById: async (companyId) => {
    try {
      const response = await api.get(`/companies/${companyId}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching company with ID ${companyId}:`, error);
      throw error;
    }
  },

  createCompany: async (companyData) => {
    try {
      const formData = new FormData();
      
      // Add all fields to form data
      Object.keys(companyData).forEach(key => {
        if (key === 'company_logo' && companyData[key] instanceof File) {
          formData.append('company_logo', companyData[key]);
        } else if (companyData[key] !== undefined && companyData[key] !== null) {
          formData.append(key, companyData[key]);
        }
      });
      
      const response = await api.post('/companies', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      
      return response.data;
    } catch (error) {
      console.error('Error creating company:', error);
      throw error;
    }
  },

  updateCompany: async (companyId, companyData) => {
    try {
      const formData = new FormData();
      
      // Add all fields to form data
      Object.keys(companyData).forEach(key => {
        if (key === 'company_logo' && companyData[key] instanceof File) {
          formData.append('company_logo', companyData[key]);
        } else if (companyData[key] !== undefined && companyData[key] !== null) {
          formData.append(key, companyData[key]);
        }
      });
      
      const response = await api.put(`/companies/${companyId}`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      
      return response.data;
    } catch (error) {
      console.error(`Error updating company with ID ${companyId}:`, error);
      throw error;
    }
  },

  deleteCompany: async (companyId) => {
    try {
      const response = await api.delete(`/companies/${companyId}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting company with ID ${companyId}:`, error);
      throw error;
    }
  },

  getCompanyAnalytics: async () => {
    try {
      const response = await api.get('/companies/dashboard');
      return response.data;
    } catch (error) {
      console.error('Error fetching company analytics:', error);
      throw error;
    }
  }
};

export default companyService;