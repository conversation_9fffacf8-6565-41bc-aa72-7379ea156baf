.jobs-admin-container {
  padding: 24px;
  background-color: #f9fafb;
  border-radius: 8px;
  width: 100%;
}

/* Disable all animations for hot badges within JobsAdmin */
.jobs-admin-container .hot-badge,
.jobs-admin-container .hot-badge *,
.jobs-admin-container .urgent-tag,
.jobs-admin-container .jobcard-urgent-tag {
  animation: none !important;
  transform: none !important;
}

.jobs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.jobs-title {
  font-size: 24px;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.add-job-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: #8057ff;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 10px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.add-job-button:hover {
  background-color: #7046e0;
}

.filters-container {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 16px;
}

.search-container {
  position: relative;
  width: 100%;
  max-width: 400px;
  display: flex;
  align-items: center;
  margin-right: 10px;
}

.search-input-wrapper {
  position: relative;
  width: 100%;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 14px;
  top: 50%;
  transform: translateY(-50%);
  color: #6b7280;
  font-size: 14px;
  z-index: 1;
  pointer-events: none;
}

.search-input {
  width: 100%;
  padding: 10px 10px 10px 40px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  font-size: 14px;
  text-indent: 5px;
  box-sizing: border-box;
  min-width: 200px;
}

.filter-select {
  padding: 10px 16px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  font-size: 14px;
  background-color: white;
}

.table-container {
  overflow-x: auto;
}

.jobs-table {
  width: 100%;
  border-collapse: collapse;
  background-color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  overflow: hidden;
}

.jobs-table th {
  padding: 12px 16px;
  text-align: left;
  font-size: 14px;
  font-weight: 600;
  color: #4b5563;
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.jobs-table td {
  padding: 12px 16px;
  font-size: 14px;
  color: #1f2937;
  border-bottom: 1px solid #e5e7eb;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 10px;
  border-radius: 9999px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.active {
  background-color: #dcfce7;
  color: #166534;
}

.status-badge.inactive {
  background-color: #fee2e2;
  color: #991b1b;
}

.action-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 4px;
  border: none;
  background-color: transparent;
  color: #6b7280;
  cursor: pointer;
  margin: 0 4px;
}

.edit-button {
  color: #2563eb;
  background-color: #dbeafe;
}

.edit-button:hover {
  background-color: #bfdbfe;
}

.delete-button {
  color: #dc2626;
  background-color: #fee2e2;
}

.delete-button:hover {
  background-color: #fecaca;
}

.toggle-button {
  transition: background-color 0.2s;
}

.toggle-button.active {
  color: #991b1b;
  background-color: #fee2e2;
}

.toggle-button.active:hover {
  background-color: #fecaca;
}

.toggle-button.inactive {
  color: #166534;
  background-color: #dcfce7;
}

.toggle-button.inactive:hover {
  background-color: #bbf7d0;
}

.hot-badge {
  display: inline-flex;
  align-items: center;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  background-color: #ffe4e6;
  color: #e11d48;
  margin-right: 6px;
  animation: none !important;
  transform: none !important;
}

.hot-badge svg {
  animation: none !important;
  transform: none !important;
}

.job-position {
  font-weight: 500;
}

.job-company {
  color: #6b7280;
  font-size: 13px;
}

.views-container {
  display: flex;
  align-items: center;
}

.views-icon {
  margin-right: 6px;
  color: #6b7280;
}

.empty-state {
  text-align: center;
  padding: 32px 16px;
}

/* Loading state */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #64748b;
  text-align: center;
  gap: 16px;
}

.loading-container svg {
  color: #3b82f6;
}

/* Error message */
.error-message {
  background-color: #fef2f2;
  color: #b91c1c;
  border: 1px solid #fee2e2;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.error-message svg {
  font-size: 18px;
}

/* No jobs message */
.no-jobs-message {
  text-align: center;
  padding: 32px;
  color: #64748b;
  font-size: 14px;
}

/* Disabled buttons */
.action-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.add-job-button:disabled {
  background-color: #a0aec0;
  cursor: not-allowed;
}

/* Debug information styling */
.debug-info {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 10px;
  font-size: 13px;
  margin-bottom: 15px;
  color: #666;
}

.debug-info p {
  margin: 5px 0;
}

.debug-info strong {
  color: #333;
}

/* Add styles for new table columns */
.experience-level {
  font-size: 0.9em;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120px;
}

.cv-email {
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.email-link {
  color: #007bff;
  text-decoration: none;
  font-size: 0.9em;
}

.email-link:hover {
  text-decoration: underline;
  color: #0056b3;
}

/* Adjust table for new columns */
.jobs-table th:nth-child(3),
.jobs-table td:nth-child(3) {
  min-width: 120px;
}

.jobs-table th:nth-child(4),
.jobs-table td:nth-child(4) {
  min-width: 150px;
}

/* Add styles for the job header actions and refresh button */
.job-header-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.refresh-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background-color: #f8f9fa;
  color: #495057;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.refresh-button:hover {
  background-color: #e9ecef;
}

.refresh-button:active {
  background-color: #dae0e5;
}

.refresh-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Add a spin animation for the refresh icon when loading */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.refresh-button svg {
  transition: transform 0.2s ease;
}

.refresh-button:active svg {
  animation: spin 1s linear infinite;
}