import React, { useState, useRef, useEffect } from "react";
import { Link } from "react-router-dom";
import "../css/AboutUs.css";
import NewsLetter from "./NewsLetter";
import ContactUs from "./ContactUs";
import PageHelmet from "./PageHelmet";

const AboutUs = () => {
  // Create refs for each section
  const governmentSectionRef = useRef(null);
  const privateSectionRef = useRef(null);
  const foreignSectionRef = useRef(null);
  const internshipSectionRef = useRef(null);

  // Scroll to top when component mounts
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const services = [
    {
      icon: "https://cdn-icons-png.flaticon.com/512/1869/1869609.png",
      title: "Government Jobs",
      desc: "Secure career opportunities in public sector",
      moreInfo: "Explore stable and rewarding careers in government ministries, departments, and public institutions. From administrative roles to specialized positions, find opportunities that serve the nation while building your future.",
      sectionRef: governmentSectionRef,
      backgroundColor: "#fee48a"
    },
    {
      icon: "https://img.icons8.com/color/96/business.png",
      title: "Private Jobs",
      desc: "Dynamic opportunities in private sector",
      moreInfo: "Discover exciting career prospects in private companies across various industries. From startups to multinational corporations, find roles that match your skills and ambitions in Sri Lanka's thriving private sector.",
      sectionRef: privateSectionRef,
      backgroundColor: "#cbbbfc"
    },
    {
      icon: "https://img.icons8.com/color/96/airplane-take-off.png",
      title: "Foreign Jobs",
      desc: "International career opportunities abroad",
      moreInfo: "Access overseas employment opportunities in the Middle East, Europe, Asia, and beyond. We provide comprehensive support including visa guidance, documentation assistance, and pre-departure orientation.",
      sectionRef: foreignSectionRef,
      backgroundColor: "#ff96aa"
    },
    {
      icon: "https://img.icons8.com/color/96/student-male.png",
      title: "Internships",
      desc: "Gain valuable work experience and skills",
      moreInfo: "Bridge the gap between education and career with quality internship programs. Perfect for students and fresh graduates to gain practical experience, build networks, and kickstart their professional journey.",
      sectionRef: internshipSectionRef,
      backgroundColor: "#94edd8"
    },
  ];

  const testimonials = [
    {
      img: "https://www.bobbin.lk/wp-content/uploads/2019/02/person2.jpg",
      text: "Found my perfect government sector job through jobpage.lk. The application process was seamless and I got interview calls within two weeks!",
      name: "Nimal Perera",
      role: "Civil Service Officer"
    },
    {
      img: "https://www.bobbin.lk/wp-content/uploads/2019/02/person4.jpg",
      text: "As an HR professional, I've found jobpage.lk to be the most effective platform for finding qualified candidates in Sri Lanka. The response rate is outstanding.",
      name: "Kamala Fernando",
      role: "HR Manager"
    },
    {
      img: "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRbyGe_NxWJEQaC-Ro114LapbLeIf6iO8Ayimi99xeA_ilhUI67u07B5TaAnf8rLAkiD6c&usqp=CAU",
      text: "The foreign job listings helped me secure a position in the Middle East. The visa guidance resources were particularly valuable.",
      name: "Ramesh Silva",
      role: "Overseas Worker"
    },
    {
      img: "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQVD3anA1LxE1oy5AO7J5P25OhDDGchLsrVWe0S497JhSsO2tMm7NkP7YqMzU1-Ut1iGhM&usqp=CAU",
      text: "The internship matching service connected me with a great tech company while I was still at university. This early experience launched my career.",
      name: "Sanduni Rajapaksa",
      role: "Software Engineer"
    },
  ];

  const [testimonialPage, setTestimonialPage] = useState(0);
  const testimonialsPerPage = 3;
  const totalPages = Math.ceil(testimonials.length / testimonialsPerPage);

  const blogPosts = [
    {
      img: "https://images.unsplash.com/photo-1521791136064-7986c2920216?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1169&q=80",
      author: "Jobpage Team",
      date: "15 June",
      title: "How to Prepare for Government Job Interviews in Sri Lanka",
    },
    {
      img: "https://images.unsplash.com/photo-1454165804606-c3d57bc86b40?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80",
      author: "Career Advisor",
      date: "2 June",
      title: "Top Skills Private Sector Employers Look For in 2024",
    },
    {
      img: "https://images.unsplash.com/photo-1572025442646-866d16c84a54?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80",
      author: "Overseas Recruiter",
      date: "20 May",
      title: "Essential Documents for Foreign Job Applications",
    },
  ];

  const [blogPage, setBlogPage] = useState(0);
  const blogsPerPage = 3;
  const totalBlogPages = Math.ceil(blogPosts.length / blogsPerPage);

  return (
    <>
      <PageHelmet 
        title="About Us" 
        description="Sri Lanka's Premier Job Platform - Connecting talent with opportunity across all sectors - Government, Private, Foreign, and Internships."
      />
      <div className="about-wrapper">
        <div className="about-grid">
          {/* Left Side Text */}
          <div className="about-text">
            <h1>
              Sri Lanka's Premier <br />Job Platform
            </h1>
            <p>
              Connecting talent with opportunity across all sectors - <br />
              Government, Private, Foreign, and Internships. <br />
              Trusted by over 500,000 professionals and 10,000+ employers.
            </p>
            <div className="about-buttons">
              <Link to="/contact" className="btn-primary">Post a Job</Link>
              <Link to="/browse" className="btn-text">Search Jobs</Link>
            </div>
          </div>

          {/* Right Side Images */}
          <div className="about-images">
            <div className="image-stack">
              <img
                src="https://images.unsplash.com/photo-1521791136064-7986c2920216?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1169&q=80"
                alt="Sri Lankan professionals"
                className="img-small-circle"
              />
              <img
                src="https://images.unsplash.com/photo-1454165804606-c3d57bc86b40?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80"
                alt="Job interview"
                className="img-medium-square"
              />
              <img
                src="https://images.unsplash.com/photo-1572025442646-866d16c84a54?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80"
                alt="Team meeting"
                className="img-large-square"
              />
            </div>
            <img
              src="https://images.unsplash.com/photo-1522071820081-009f0129c71c?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80"
              alt="Diverse Sri Lankan professionals"
              className="main-img"
            />
          </div>
        </div>

        {/* Services Section */}
        <div className="services-wrapper">
          <h2 className="section-title">Our Comprehensive Services</h2>
          <p className="section-subtitle">Bridging the gap between talent and opportunity</p>
          <div className="services-grid">
            {services.map((service, index) => (
              <ServiceCard key={index} service={service} />
            ))}
          </div>
        </div>
      </div>

      {/* Job Categories Section */}
      
      {/* Government Jobs */}
      <div className="job-cta-section" ref={governmentSectionRef}>
        <div className="job-image-box">
          <img
            src="https://images.unsplash.com/photo-1554224155-6726b3ff858f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1170&q=80"
            alt="Government building"
            className="cta-image"
          />
        </div>
        <div className="job-text-box" style={{ backgroundColor: "#fee48a" }}>
          <span className="job-link">Government Sector</span>
          <h2>Secure Your Future with Government Jobs</h2>
          <p>Explore stable and rewarding careers in government ministries, departments, and public institutions. From administrative roles to specialized positions, find opportunities that serve the nation while building your future with excellent benefits and job security.</p>
          
        </div>
      </div>

      {/* Private Jobs */}
      <div className="job-cta-section" ref={privateSectionRef}>
        <div className="job-image-box">
          <img
            src="https://images.unsplash.com/photo-1454165804606-c3d57bc86b40?ixlib=rb-4.0.3&auto=format&fit=crop&w=1170&q=80"
            alt="Modern office"
            className="cta-image"
          />
        </div>
        <div className="job-text-box" style={{ backgroundColor: "#cbbbfc" }}>
          <span className="job-link">Private Sector</span>
          <h2>Dynamic Opportunities in Private Companies</h2>
          <p>Discover exciting career prospects in private companies across various industries. From innovative startups to established multinational corporations, find roles that match your skills and ambitions in Sri Lanka's thriving private sector with competitive packages.</p>
          
        </div>
      </div>

      {/* Foreign Jobs */}
      <div className="job-cta-section" ref={foreignSectionRef}>
        <div className="job-image-box">
          <img
            src="https://images.unsplash.com/photo-1436491865332-7a61a109cc05?ixlib=rb-4.0.3&auto=format&fit=crop&w=1174&q=80"
            alt="International travel"
            className="cta-image"
          />
        </div>
        <div className="job-text-box" style={{ backgroundColor: "#ff96aa" }}>
          <span className="job-link">International Opportunities</span>
          <h2>Expand Your Horizons with Foreign Jobs</h2>
          <p>Access overseas employment opportunities in the Middle East, Europe, Asia, and beyond. We provide comprehensive support including visa guidance, documentation assistance, and pre-departure orientation to help you succeed abroad.</p>
          
        </div>
      </div>

      {/* Internships */}
      <div className="job-cta-section" ref={internshipSectionRef}>
        <div className="job-image-box">
          <img
            src="https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1171&q=80"
            alt="Students learning"
            className="cta-image"
          />
        </div>
        <div className="job-text-box" style={{ backgroundColor: "#94edd8" }}>
          <span className="job-link">Career Development</span>
          <h2>Launch Your Career with Quality Internships</h2>
          <p>Bridge the gap between education and career with quality internship programs. Perfect for students and fresh graduates to gain practical experience, build professional networks, and kickstart their journey in the competitive job market.</p>
          
        </div>
      </div>

      {/* Quality Section */}
      {/* <div className="quality-section">
        <div className="quality-content">
          <span className="quality-subtitle">Our Commitment</span>
          <h2>Dedicated to authentic<br />job opportunities</h2>
          <p>
            We rigorously verify every listing to ensure only legitimate opportunities reach our users. Our team works tirelessly to maintain the highest standards of quality in Sri Lanka's job market.
          </p>
          <p>
            From fresh graduates to experienced professionals, we provide tailored solutions that address the unique needs of every job seeker and employer in our community.
          </p>
          <button className="learn-more-btn">Our Verification Process</button>
        </div>
        <div className="quality-image-box">
          <img
            src="https://images.unsplash.com/photo-1522071820081-009f0129c71c?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80"
            alt="Team reviewing applications"
            className="quality-img"
          />
        </div>
      </div> */}

      {/* Team Section */}
      {/* <section className="team-section">
        <h2 className="team-title">Meet Our Colombo Team</h2>
        <p className="team-desc">
          Passionate professionals dedicated to transforming Sri Lanka's employment landscape
        </p>
        <div className="team-grid">
          {[
            { name: "Dinesh Perera", position: "Founder & CEO", img: "https://randomuser.me/api/portraits/men/32.jpg" },
            { name: "Priyanka Silva", position: "HR Partnerships", img: "https://randomuser.me/api/portraits/women/44.jpg" },
            { name: "Amal Bandara", position: "Tech Lead", img: "https://randomuser.me/api/portraits/men/22.jpg" },
            { name: "Nayomi Fernando", position: "Customer Support", img: "https://randomuser.me/api/portraits/women/63.jpg" },
            { name: "Rajiv Patel", position: "Marketing", img: "https://randomuser.me/api/portraits/men/41.jpg" },
            { name: "Shamali Weerasinghe", position: "Content Manager", img: "https://randomuser.me/api/portraits/women/50.jpg" },
          ].map((member, idx) => (
            <div className="team-card" key={idx}>
              <div className="team-img-rect">
                <img src={member.img} alt={member.name} />
              </div>
              <div className="team-name">{member.name}</div>
              <div className="team-position">{member.position}</div>
              <div className="team-social">
                <a href="#" aria-label="LinkedIn" className="team-social-link">
                  <svg width="18" height="18" viewBox="0 0 24 24" fill="#0077B5">
                    <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"/>
                  </svg>
                </a>
              </div>
            </div>
          ))}
        </div>
      </section> */}

      {/* Testimonials Section */}
      {/* <section className="testimonial-section">
        <h2 className="testimonial-title">
          <span className="testimonial-title-our">Success</span> <span className="testimonial-title-happy">Stories</span>
        </h2>
        <p className="testimonial-desc">
          Hear from job seekers and employers who've transformed their careers and businesses through our platform
        </p>
        <div className="testimonial-cards">
          {testimonials
            .slice(testimonialPage * testimonialsPerPage, testimonialPage * testimonialsPerPage + testimonialsPerPage)
            .map((item, idx) => (
              <div className="testimonial-card" key={idx}>
                <img src={item.img} alt={item.name} className="testimonial-avatar" />
                <p className="testimonial-text">{item.text}</p>
                <div className="testimonial-stars">
                  {Array(5)
                    .fill(0)
                    .map((_, i) => (
                      <svg key={i} width="20" height="20" viewBox="0 0 20 20" fill="#FFC107">
                        <polygon points="10,1.5 12.59,7.36 18.9,7.63 13.97,11.97 15.54,18.09 10,14.77 4.46,18.09 6.03,11.97 1.1,7.63 7.41,7.36" />
                      </svg>
                    ))}
                </div>
                <div className="testimonial-author">{item.name}</div>
                <div className="testimonial-role">{item.role}</div>
              </div>
            ))}
        </div>
        <div className="testimonial-dots">
          {Array.from({ length: totalPages }).map((_, idx) => (
            <span
              key={idx}
              className={`dot${idx === testimonialPage ? " active" : ""}`}
              onClick={() => setTestimonialPage(idx)}
            ></span>
          ))}
        </div>
      </section> */}

      {/* Blog Section */}
      {/* <section className="blog-section">
        <h2 className="blog-title">Career Insights</h2>
        <div className="blog-subtitle">Latest Job Market Trends & Advice</div>
        <div className="blog-cards">
          {blogPosts
            .slice(blogPage * blogsPerPage, blogPage * blogsPerPage + blogsPerPage)
            .map((post, idx) => (
              <div className="blog-card" key={idx}>
                <div className="blog-card-img">
                  <img src={post.img} alt="Blog" />
                </div>
                <div className="blog-card-meta">
                  <span className="blog-author">{post.author}</span>
                  <span className="blog-date">{post.date}</span>
                </div>
                <div className="blog-card-title">{post.title}</div>
                <div className="blog-card-footer">
                  <button className="blog-read-btn">Read Article</button>
                </div>
              </div>
            ))}
        </div>
        <div className="blog-dots">
          {Array.from({ length: totalBlogPages }).map((_, idx) => (
            <span
              key={idx}
              className={`blog-dot${idx === blogPage ? " active" : ""}`}
              onClick={() => setBlogPage(idx)}
            ></span>
          ))}
        </div>
      </section> */}

      {/* Newsletter Section */}
      {/* <NewsLetter /> */}
    </>
  );
};

const ServiceCard = ({ service }) => {
  const scrollToSection = () => {
    if (service.sectionRef && service.sectionRef.current) {
      // Get the element
      const element = service.sectionRef.current;
      
      // Get element's position relative to the viewport
      const elementPosition = element.getBoundingClientRect().top;
      
      // Get the current scroll position
      const offsetPosition = elementPosition + window.pageYOffset - 100; // 100px offset
      
      // Scroll to the element with the offset
      window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth'
      });
    }
  };

  return (
    <div className="service-card" style={service.backgroundColor ? { backgroundColor: service.backgroundColor } : {}}>
      <img src={service.icon} alt={service.title} />
      <h3>{service.title}</h3>
      <p>{service.desc}</p>
      <button onClick={scrollToSection} className="read-more">
        Learn More
      </button>
    </div>
  );
};

export default AboutUs;