.contact-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Header Styles */
.contact-header {
  text-align: center;
  padding: 0px 0 40px;
  position: relative;
}

.header-content {
  max-width: 700px;
  margin: 0 auto;
}

.header-title {
  font-size: 2.5rem;
  font-weight: 300;
  color: #222;
  margin-bottom: 15px;
  letter-spacing: 0.5px;
}

.header-subtitle {
  font-size: 1.1rem;
  color: #666;
  line-height: 1.6;
  margin-bottom: 25px;
}

.header-divider {
  width: 80px;
  height: 3px;
  background: linear-gradient(90deg, #9370DB, #6A5ACD);
  margin: 0 auto;
}

/* Main Content Layout */
.contact-main {
  display: flex;
  gap: 40px;
  margin: 60px 0;
}

.contact-info-panel {
  flex: 1;
  min-width: 300px;
}

.contact-form-panel {
  flex: 2;
}

/* Information Panel */
.info-section {
  background: #fff;
  padding: 30px;
  border-radius: 4px;
  box-shadow: 0 2px 30px rgba(0, 0, 0, 0.05);
  margin-bottom: 30px;
}

.info-section-title {
  font-size: 1.5rem;
  font-weight: 400;
  color: #444;
  margin-bottom: 10px;
  position: relative;
  padding-bottom: 10px;
}

.info-section-title:after {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 40px;
  height: 2px;
  background: #9370DB;
}

.info-section-description {
  color: #777;
  font-size: 0.95rem;
  margin-bottom: 25px;
}

.info-card {
  display: flex;
  align-items: flex-start;
  margin-bottom: 25px;
}

.info-icon {
  width: 50px;
  height: 50px;
  background: rgba(147, 112, 219, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  flex-shrink: 0;
}

.info-icon svg {
  width: 20px;
  height: 20px;
  fill: #9370DB;
}

.info-content h3 {
  font-size: 1.1rem;
  font-weight: 500;
  color: #444;
  margin-bottom: 5px;
}

.info-content p {
  color: #666;
  font-size: 0.95rem;
  line-height: 1.5;
  margin: 3px 0;
}

/* Business Hours */
.business-hours {
  background: #fff;
  padding: 30px;
  border-radius: 4px;
  box-shadow: 0 2px 30px rgba(0, 0, 0, 0.05);
}

.business-hours h3 {
  font-size: 1.2rem;
  font-weight: 400;
  color: #444;
  margin-bottom: 20px;
  position: relative;
  padding-bottom: 10px;
}

.business-hours h3:after {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 40px;
  height: 2px;
  background: #9370DB;
}

.hours-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.hours-item:last-child {
  border-bottom: none;
}

.hours-item span:first-child {
  color: #555;
  font-weight: 500;
}

.hours-item span:last-child {
  color: #777;
}

/* Form Panel */
.form-header {
  margin-bottom: 30px;
}

.form-header h2 {
  font-size: 1.8rem;
  font-weight: 300;
  color: #444;
  margin-bottom: 10px;
}

.form-header p {
  color: #777;
  font-size: 0.95rem;
}

.elegant-form {
  background: #fff;
  padding: 40px;
  border-radius: 4px;
  box-shadow: 0 2px 30px rgba(0, 0, 0, 0.05);
}

.form-row {
  display: flex;
  gap: 30px;
  margin-bottom: 30px;
}

.form-group {
  flex: 1;
  position: relative;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  color: #555;
  font-size: 0.9rem;
  font-weight: 500;
}

.required {
  color: #d9534f;
}

input, select, textarea {

  padding: 10px 0;
  border: none;
  border-bottom: 1px solid #e0e0e0;
  background: transparent;
  font-size: 0.95rem;
  color: #333;
  transition: all 0.3s ease;
}

input:focus, select:focus, textarea:focus {
  outline: none;
  border-bottom-color: #9370DB;
}

.form-underline {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background: #9370DB;
  transition: width 0.3s ease;
}

.form-group:focus-within .form-underline {
  width: 100%;
}

select {
  appearance: none;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 24 24' fill='%23666'%3E%3Cpath d='M7 10l5 5 5-5z'/%3E%3C/svg%3E") no-repeat;
  background-position: right center;
  padding-right: 25px;
}

textarea {
  resize: vertical;
  min-height: 120px;
}

/* Form Footer */
.form-footer {
  margin-top: 40px;
}

.submit-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #9370DB, #6A5ACD);
  color: white;
  border: none;
  padding: 12px 30px;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 10px rgba(147, 112, 219, 0.3);
}

.submit-button:hover {
  background: linear-gradient(135deg, #8360cb, #5a4acd);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(147, 112, 219, 0.4);
}

.submit-button svg {
  width: 18px;
  height: 18px;
  fill: white;
  margin-left: 10px;
}

.form-disclaimer {
  color: #999;
  font-size: 0.8rem;
  margin-top: 20px;
}

.form-disclaimer a {
  color: #9370DB;
  text-decoration: none;
}

/* Responsive Design */
@media (max-width: 900px) {
  .contact-main {
    flex-direction: column;
  }
  
  .form-row {
    flex-direction: column;
    gap: 20px;
  }
}

@media (max-width: 600px) {
  .contact-header {
    padding: 40px 0 30px;
  }
  
  .header-title {
    font-size: 2rem;
  }
  
  .elegant-form {
    padding: 30px 20px;
  }
}

/* Success Popup Styles */
.success-popup {
  position: fixed;
  top: 20px;
  right: 20px;
  background-color: #4CAF50;
  color: white;
  padding: 15px 25px;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  display: flex;
  align-items: center;
  animation: slideIn 0.3s ease-out;
}

.popup-content {
  display: flex;
  align-items: center;
}

.popup-content svg {
  width: 20px;
  height: 20px;
  margin-right: 10px;
  fill: white;
}

.popup-content p {
  margin: 0;
  font-size: 14px;
}

.close-popup {
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  margin-left: 15px;
  cursor: pointer;
  padding: 0 5px;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Disabled button state */
.submit-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.submit-button:disabled svg {
  display: none;
}

/* Error Popup Styles */
/* Add to your stylesheet */
