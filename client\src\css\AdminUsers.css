.admin-users-container {
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.admin-users-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e0e0e0;
}

.admin-users-header h2 {
  display: flex;
  align-items: center;
  margin: 0;
  color: #333;
}

.admin-users-icon {
  margin-right: 10px;
  color: #4a6cf7;
}

.admin-users-controls {
  display: flex;
  align-items: center;
}

.admin-users-search {
  position: relative;
  margin-right: 15px;
}

.search-icon {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
}

.admin-users-search input {
  padding: 8px 10px 8px 35px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  width: 250px;
}

.admin-users-add-btn {
  background-color: #4a6cf7;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 15px;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: background-color 0.2s;
}

.admin-users-add-btn:hover {
  background-color: #3a5bd9;
}

.admin-users-add-btn svg {
  margin-right: 5px;
}

.admin-users-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
  background-color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.admin-users-table th,
.admin-users-table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #e0e0e0;
}

.admin-users-table th {
  background-color: #f1f3f9;
  color: #495057;
  font-weight: 600;
}

.admin-users-table tr:last-child td {
  border-bottom: none;
}

.admin-users-table tr:hover {
  background-color: #f8f9fa;
}

.role-badge {
  display: flex;
  align-items: center;
}

.role-icon {
  margin-right: 8px;
}

.role-icon.superadmin {
  color: #dc3545;
}

.role-icon.admin {
  color: #fd7e14;
}

.role-icon.editor {
  color: #20c997;
}

.role-icon.user {
  color: #6c757d;
}

.table-actions {
  display: flex;
  gap: 8px;
}

.edit-btn,
.delete-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.edit-btn {
  color: #4a6cf7;
}

.delete-btn {
  color: #dc3545;
}

.edit-btn:hover {
  background-color: rgba(74, 108, 247, 0.1);
}

.delete-btn:hover {
  background-color: rgba(220, 53, 69, 0.1);
}

.edit-btn:disabled,
.delete-btn:disabled {
  color: #adb5bd;
  cursor: not-allowed;
}

.admin-users-loading,
.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  color: #6c757d;
}

.admin-users-loading svg,
.loading-spinner svg {
  margin-bottom: 10px;
  color: #4a6cf7;
}

.admin-users-error {
  display: flex;
  align-items: flex-start;
  padding: 15px;
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 4px;
  color: #721c24;
  margin-bottom: 20px;
}

.admin-users-error svg {
  margin-right: 10px;
  font-size: 1.2em;
  color: #dc3545;
}

.error-content {
  flex: 1;
}

.error-actions {
  margin-top: 10px;
}

.retry-btn {
  background-color: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  transition: background-color 0.2s;
}

.retry-btn:hover {
  background-color: #5a6268;
}

.retry-btn svg {
  margin-right: 5px;
  color: white;
}

.no-results {
  text-align: center;
  padding: 30px 0;
  color: #6c757d;
}

/* Modal Styles */
.admin-users-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.admin-users-modal-content {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  width: 100%;
  max-width: 500px;
  padding: 20px;
  animation: modalFadeIn 0.3s;
}

@keyframes modalFadeIn {
  from { opacity: 0; transform: translateY(-20px); }
  to { opacity: 1; transform: translateY(0); }
}

.admin-users-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e0e0e0;
}

.admin-users-modal-header h3 {
  margin: 0;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.2em;
  cursor: pointer;
  color: #6c757d;
  transition: color 0.2s;
}

.close-btn:hover {
  color: #343a40;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #495057;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 10px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 1em;
}

.form-group input:focus,
.form-group select:focus {
  border-color: #4a6cf7;
  outline: none;
  box-shadow: 0 0 0 3px rgba(74, 108, 247, 0.25);
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.cancel-btn {
  background-color: #f8f9fa;
  color: #495057;
  border: 1px solid #ced4da;
  border-radius: 4px;
  padding: 8px 15px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.cancel-btn:hover {
  background-color: #e9ecef;
}

.submit-btn {
  background-color: #4a6cf7;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 15px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.submit-btn:hover {
  background-color: #3a5bd9;
}

.submit-btn:disabled {
  background-color: #a8b8f8;
  cursor: not-allowed;
}

/* Delete Confirmation Dialog Styles */
.delete-confirm-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(2px);
}

.delete-confirm-dialog {
  background-color: white;
  border-radius: 8px;
  width: 90%;
  max-width: 450px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  animation: dialog-appear 0.3s ease;
}

.delete-confirm-header {
  background-color: #f8d7da;
  color: #842029;
  padding: 16px 20px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #f5c2c7;
}

.delete-confirm-header .delete-icon {
  font-size: 1.5rem;
  margin-right: 10px;
  color: #dc3545;
}

.delete-confirm-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.delete-confirm-content {
  padding: 20px;
  text-align: center;
}

.delete-confirm-content p {
  margin: 8px 0;
  color: #333;
}

.delete-confirm-content p:first-child {
  font-weight: 500;
}

.delete-confirm-content strong {
  color: #212529;
  font-weight: 600;
}

.delete-confirm-actions {
  display: flex;
  justify-content: flex-end;
  padding: 16px 20px;
  background-color: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.delete-confirm-actions button {
  padding: 8px 16px;
  margin-left: 10px;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
}

.cancel-delete-btn {
  background-color: #e9ecef;
  color: #495057;
}

.cancel-delete-btn:hover {
  background-color: #dee2e6;
}

.confirm-delete-btn {
  background-color: #dc3545;
  color: white;
}

.confirm-delete-btn:hover {
  background-color: #c82333;
}

@keyframes dialog-appear {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Add these styles for the refresh button */
.refresh-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background-color: #f8f9fa;
  color: #495057;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-right: 10px;
}

.refresh-button:hover {
  background-color: #e9ecef;
}

.refresh-button:active {
  background-color: #dae0e5;
}

.refresh-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Add a spin animation for the refresh icon when loading */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.refresh-button svg {
  transition: transform 0.2s ease;
}

.refresh-button:active svg {
  animation: spin 1s linear infinite;
}