.company-details-container {
  max-width: none;
  margin: 0;
  padding: 40px calc(2% + 210px);
  min-height: 100vh;
  box-sizing: border-box;
}

.content-spacing {
  height: 80px; /* Space for the fixed navbar */
  width: 100%;
}

.back-button-container {
  margin-bottom: 24px;
}

.back-button:hover {
  background: linear-gradient(135deg, #6234c7, #8e24aa);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(119, 67, 219, 0.3);
}

.back-button:active {
  transform: translateY(0);
}

.company-header {
  background: linear-gradient(135deg, #f9f2ff 0%, #ffefef 100%);
  border-radius: 16px;
  padding: 32px;
  border: 1px solid #e2e8f0;
  margin-bottom: 32px;
  display: flex;
  align-items: center;
  gap: 24px;
  transition: all 0.3s ease;
}

.company-logo-large {
  width: 80px;
  height: 80px;
  border-radius: 12px;
  object-fit: contain;
  background: #f8fafc;
  border: 2px solid #e2e8f0;

}

.company-info-block h1 {
  margin: 0 0 8px 0;
  font-size: 2rem;
  font-weight: 700;
  color: #1e293b;
}

.company-description {
  color: #64748b;
  margin-bottom: 12px;
  font-size: 16px;
  line-height: 1.6;
}

.company-website-link {
  color: #7743DB;
  text-decoration: none;
  font-weight: 600;
  font-size: 14px;
  padding: 8px 16px;
  border: 1px solid #7743DB;
  border-radius: 8px;
  display: inline-block;
  transition: all 0.3s ease;
}

.company-website-link:hover {
  background: #7743DB;
  color: white;
  transform: translateY(-1px);
}
.company-jobs-section {
  margin-top: 32px;
}

.company-jobs-section h2 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.jobs-count-badge {
  background: linear-gradient(135deg, #7743DB, #9c27b0);
  color: white;
  font-size: 12px;
  padding: 4px 12px;
  border-radius: 20px;
  font-weight: 600;
}

.company-jobs-list {
  list-style: none;
  padding: 0;
  margin: 0;
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
}
.company-job-item {
  padding: 24px;
  border-bottom: 1px solid #f1f5f9;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 20px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.company-job-item:last-child {
  border-bottom: none;
}

.company-job-item:hover {
  background: #f8fafc;
  transform: translateX(4px);
}

.job-info {
  flex: 1;
}

.job-title-link {
  font-weight: 600;
  color: #1e293b;
  text-decoration: none;
  font-size: 18px;
  display: block;
  margin-bottom: 8px;
  transition: color 0.3s ease;
}

.job-title-link:hover {
  color: #7743DB;
}

.job-meta {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
  margin-top: 4px;
}

.company-job-type {
  background: linear-gradient(135deg, #f3e8ff, #e9d5ff);
  color: #7c3aed;
  font-size: 12px;
  padding: 6px 12px;
  border-radius: 20px;
  font-weight: 600;
  border: 1px solid #e9d5ff;
}

.company-job-category {
  background: linear-gradient(135deg, #dbeafe, #bfdbfe);
  color: #2563eb;
  font-size: 12px;
  padding: 6px 12px;
  border-radius: 20px;
  font-weight: 600;
  border: 1px solid #bfdbfe;
}

.hot-badge {
  background: linear-gradient(135deg, #ef4444, #f97316);
  color: white;
  font-size: 11px;
  padding: 6px 12px;
  border-radius: 20px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  animation: pulse 2s infinite;
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
  }
}

.company-job-date {
  color: #64748b;
  font-size: 14px;
  white-space: nowrap;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
}
.company-details-loading, .company-details-error {
  text-align: center;
  margin: 40px 0;
  padding: 40px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.company-details-loading {
  color: #64748b;
  font-size: 16px;
}

.company-details-error {
  color: #dc2626;
  font-size: 16px;
  font-weight: 600;
}

.no-jobs-message {
  text-align: center;
  padding: 40px;
  color: #64748b;
  font-size: 16px;
  background: white;
  border-radius: 16px;
  margin-top: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
}

/* Responsive design */
@media (max-width: 1400px) {
  .company-details-container {
    padding: 40px calc(2% + 80px);
  }
}

@media (max-width: 768px) {
  .company-details-container {
    padding: 20px calc(2% + 30px);
  }
}

@media (max-width: 480px) {
  .company-details-container {
    padding: 20px calc(2% + 15px);
  }
}

@media (max-width: 768px) {
  .company-details-container {
    margin: 0;
    max-width: 100%;
  }
  
  .company-header {
    flex-direction: column;
    text-align: center;
    gap: 16px;
    padding: 24px;
  }
  
  .company-logo-large {
    width: 60px;
    height: 60px;
  }
  
  .company-info-block h1 {
    font-size: 1.5rem;
  }
  
  .company-job-item {
    flex-direction: column;
    gap: 12px;
    padding: 20px;
  }
  
  .job-meta {
    justify-content: flex-start;
  }
}
