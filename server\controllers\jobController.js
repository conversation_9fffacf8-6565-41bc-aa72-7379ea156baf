const Job = require('../models/jobModel');
const fs = require('fs');
const path = require('path');
const cloudinary = require('cloudinary').v2;

// Get hot jobs
function getHotJobs(req, res) {
  Job.getAll((err, results) => {
    if (err) {
      console.error("Query error (hot jobs):", err);
      return res.status(500).json({ error: "Database error" });
    }
    // Only jobs with hot = 1 or true
    const hotJobs = results.filter(job => job.hot === 1 || job.hot === true || job.hot === '1' || job.hot === 'true');
    // Optionally limit to 10
    res.json(hotJobs.slice(0, 10));
  });
}

// Configure Cloudinary if environment variables are available
let cloudinaryConfigured = false;
try {
  if (process.env.CLOUDINARY_CLOUD_NAME && 
      process.env.CLOUDINARY_API_KEY && 
      process.env.CLOUDINARY_API_SECRET) {
    
    cloudinary.config({
      cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
      api_key: process.env.CLOUDINARY_API_KEY,
      api_secret: process.env.CLOUDINARY_API_SECRET
    });
    cloudinaryConfigured = true;
    // Cloudinary configured successfully
  } else {
    // Cloudinary environment variables are missing, image uploads will be stored in the database
  }
} catch (error) {
  console.error("Error configuring Cloudinary:", error);
}

const jobController = {
  getHotJobs,  getAllJobs: (req, res) => {
    const { category, limit, company } = req.query;
    
    Job.getAll((err, results) => {
      if (err) {
        console.error("Query error:", err);
        return res.status(500).json({ error: "Database error" });
      }
      
      // Total jobs in database: results.length
      // Filtering by category and/or company if provided
      
      let filteredResults = results;
        // Filter by company if provided
      if (company) {
        const originalCount = filteredResults.length;
        
        filteredResults = filteredResults.filter(job => {
          const jobCompany = job.company_name;
          
          if (!jobCompany) {
            return false;
          }
          
          // Normalize both company names for comparison
          const normalizedJobCompany = jobCompany.trim().toLowerCase();
          const normalizedFilterCompany = company.trim().toLowerCase();
          
          const match = normalizedJobCompany === normalizedFilterCompany;
          
          // Debug logging for first few comparisons
          
          return match;
        });
        
      }
      
      // Filter by category (main_topics) if provided
      if (category) {
        filteredResults = filteredResults.filter(job => {
          const jobMainTopics = job.main_topics;
          const match = jobMainTopics && jobMainTopics.toLowerCase() === category.toLowerCase();
          return match;
        });
        // After filtering: filteredResults.length jobs found
      }
      
      // Apply limit if provided
      if (limit) {
        const limitNum = parseInt(limit);
        if (!isNaN(limitNum) && limitNum > 0) {
          filteredResults = filteredResults.slice(0, limitNum);
          // After limit: filteredResults.length jobs returned
        }
      }
      
      res.json(filteredResults);
    });
  },

  // Get dashboard analytics
  getDashboardAnalytics: (req, res) => {
    Job.getAll((err, allJobs) => {
      if (err) {
        console.error("Query error:", err);
        return res.status(500).json({ error: "Database error" });
      }
      
      // Calculate basic stats
      const totalJobs = allJobs.length;
      // Since we don't have a status column, we'll assume all jobs are active for now
      const activeJobs = allJobs.length; 
      // Since we don't have a views column, we'll estimate based on the number of jobs
      const totalViews = allJobs.length * 10; // Assuming each job gets about 10 views on average
      const estimatedApplicants = Math.round(totalViews * 0.05); // Estimate based on views
      
      // Calculate jobs by category
      const categories = {};
      allJobs.forEach(job => {
        const category = job.main_topics || 'Other';
        if (!categories[category]) {
          categories[category] = 0;
        }
        categories[category]++;
      });
      
      const jobsByCategory = Object.keys(categories).map(category => ({
        category,
        count: categories[category]
      })).sort((a, b) => b.count - a.count);
      
      // Get recent jobs
      const recentJobs = [...allJobs]
        .sort((a, b) => new Date(b.start_date || b.created_at || Date.now()) - 
                        new Date(a.start_date || a.created_at || Date.now()))
        .slice(0, 5)
        .map(job => ({
          job_id: job.job_id,
          job_title: job.job_title,
          company_name: job.company_name,
          main_topics: job.main_topics,
          start_date: job.start_date,
          views: job.views || 0
        }));
      
      // Return formatted dashboard data
      res.json({
        stats: {
          totalJobs,
          activeJobs,
          totalViews,
          estimatedApplicants
        },
        jobsByCategory,
        recentJobs
      });
    });
  },

  // Get job by ID
  getJobById: (req, res) => {
    const jobId = req.params.job_id;
    
    Job.getById(jobId, (err, results) => {
      if (err) {
        console.error("Query error:", err);
        return res.status(500).json({ error: "Database error" });
      }
      if (results.length === 0) {
        return res.status(404).json({ error: "Job not found" });
      }
      
      // Process the job data before sending it to the client
      const job = results[0];
      
      // Ensure sub_topics is properly formatted as JSON
      if (job.sub_topics) {
        try {
          // If it's already a string, try to parse it to make sure it's valid JSON
          if (typeof job.sub_topics === 'string') {
            JSON.parse(job.sub_topics);
            // If it parsed successfully, leave it as is
          }
        } catch (err) {
          // If parsing fails, convert to a JSON array with the single value
          job.sub_topics = JSON.stringify([job.sub_topics]);
        }
      } else {
        // If sub_topics is null or undefined, set as empty array
        job.sub_topics = '[]';
      }
      
      res.json(job);
    });
  },
  
  // Create new job
  createJob: async (req, res) => {
    try {
      // Extract form fields from request body
      const jobData = { ...req.body };
      
      // Handle file uploads if they exist
      if (req.files) {
        
        // Process job post image
        if (req.files.job_post_image && req.files.job_post_image[0]) {
          try {
            // If Cloudinary is configured, upload the image
            if (cloudinaryConfigured) {
              // Convert the file buffer to base64 for Cloudinary upload
              const fileBuffer = req.files.job_post_image[0].buffer;
              if (fileBuffer && fileBuffer.length) {
                const base64String = `data:${req.files.job_post_image[0].mimetype};base64,${fileBuffer.toString('base64')}`;
                
                // Upload to Cloudinary
                try {
                  const uploadResult = await cloudinary.uploader.upload(base64String, {
                    folder: 'job_post_images',
                    upload_preset: process.env.CLOUDINARY_UPLOAD_PRESET || undefined
                  });
                  
                  // Add the Cloudinary URL to the job data
                  jobData.job_post_image = uploadResult.secure_url;
                } catch (cloudinaryError) {
                  // Fall back to storing in database if Cloudinary fails
                  // Only use the buffer if it's not too large
                  if (fileBuffer.length > 1048576) { // 1MB
                    jobData.job_post_image = null;
                  } else {
                    jobData.job_post_image = fileBuffer;
                  }
                }
              } else {
                jobData.job_post_image = null;
              }
            } else {
              // Cloudinary not configured, store image in database
              const fileBuffer = req.files.job_post_image[0].buffer;
              // Only use the buffer if it's not too large
              if (fileBuffer && fileBuffer.length > 1048576) { // 1MB
                jobData.job_post_image = null;
              } else if (fileBuffer) {
                jobData.job_post_image = fileBuffer;
              } else {
                jobData.job_post_image = null;
              }
            }
          } catch (fileProcessingError) {
            jobData.job_post_image = null;
          }
        }
        
        // Process job post thumbnail
        if (req.files.job_post_thumbnail && req.files.job_post_thumbnail[0]) {
          try {
            // If Cloudinary is configured, upload the thumbnail
            if (cloudinaryConfigured) {
              // Convert the file buffer to base64 for Cloudinary upload
              const fileBuffer = req.files.job_post_thumbnail[0].buffer;
              if (fileBuffer && fileBuffer.length) {
                const base64String = `data:${req.files.job_post_thumbnail[0].mimetype};base64,${fileBuffer.toString('base64')}`;
                
                // Upload to Cloudinary
                try {
                  const uploadResult = await cloudinary.uploader.upload(base64String, {
                    folder: 'job_post_thumbnails',
                    upload_preset: process.env.CLOUDINARY_UPLOAD_PRESET || undefined
                  });
                  
                  // Add the Cloudinary URL to the job data
                  jobData.job_post_thumbnail = uploadResult.secure_url;
                } catch (cloudinaryError) {
                  // Fall back to storing in database if Cloudinary fails
                  // Only use the buffer if it's not too large
                  if (fileBuffer.length > 1048576) { // 1MB
                    jobData.job_post_thumbnail = null;
                  } else {
                    jobData.job_post_thumbnail = fileBuffer;
                  }
                }
              } else {
                jobData.job_post_thumbnail = null;
              }
            } else {
              // Cloudinary not configured, store thumbnail in database
              const fileBuffer = req.files.job_post_thumbnail[0].buffer;
              // Only use the buffer if it's not too large
              if (fileBuffer && fileBuffer.length > 1048576) { // 1MB
                jobData.job_post_thumbnail = null;
              } else if (fileBuffer) {
                jobData.job_post_thumbnail = fileBuffer;
              } else {
                jobData.job_post_thumbnail = null;
              }
            }
          } catch (fileProcessingError) {
            jobData.job_post_thumbnail = null;
          }
        }
        
        // Process company logo
        if (req.files.company_logo && req.files.company_logo[0]) {
          try {
            // If Cloudinary is configured, upload the logo
            if (cloudinaryConfigured) {
              // Convert the file buffer to base64 for Cloudinary upload
              const fileBuffer = req.files.company_logo[0].buffer;
              if (fileBuffer && fileBuffer.length) {
                const base64String = `data:${req.files.company_logo[0].mimetype};base64,${fileBuffer.toString('base64')}`;
                
                // Upload to Cloudinary
                try {
                  const uploadResult = await cloudinary.uploader.upload(base64String, {
                    folder: 'company_logos',
                    upload_preset: process.env.CLOUDINARY_UPLOAD_PRESET || undefined
                  });
                  
                  // Add the Cloudinary URL to the job data
                  jobData.company_logo = uploadResult.secure_url;
                } catch (cloudinaryError) {
                  // Fall back to storing in database if Cloudinary fails
                  // Only use the buffer if it's not too large
                  if (fileBuffer.length > 1048576) { // 1MB
                    jobData.company_logo = null;
                  } else {
                    jobData.company_logo = fileBuffer;
                  }
                }
              } else {
                jobData.company_logo = null;
              }
            } else {
              // Cloudinary not configured, store logo in database
              const fileBuffer = req.files.company_logo[0].buffer;
              // Only use the buffer if it's not too large
              if (fileBuffer && fileBuffer.length > 1048576) { // 1MB
                jobData.company_logo = null;
              } else if (fileBuffer) {
                jobData.company_logo = fileBuffer;
              } else {
                jobData.company_logo = null;
              }
            }
          } catch (fileProcessingError) {
            jobData.company_logo = null;
          }
        }
      }
      
      // Use existing company logo URL if provided
      if (req.body.existing_company_logo_url) {
        jobData.company_logo = req.body.existing_company_logo_url;
      }
      
      // Use company_logo_url if provided
      if (req.body.company_logo_url) {
        jobData.company_logo = req.body.company_logo_url;
      }
      
      // Handle boolean values from form data
      if (jobData.hot === 'true') jobData.hot = true;
      if (jobData.hot === 'false') jobData.hot = false;
      
      // Handle empty salary value to ensure it gets saved as NULL
      if (jobData.salary_range === '') {
        jobData.salary_range = null;
      }

      // Handle min_salary and max_salary
      if (jobData.min_salary === '') jobData.min_salary = null;
      if (jobData.max_salary === '') jobData.max_salary = null;
      
      // Ensure sub_topics is always an array when no data is provided
      if (!jobData.sub_topics) {
        jobData.sub_topics = [];
      }
      
      // Validate required fields
      if (!jobData.job_title || !jobData.company_name) {
        return res.status(400).json({ message: "Missing required fields" });
      }

      // Force min_salary and max_salary to be numbers or null
      if (jobData.min_salary !== undefined) {
        jobData.min_salary = jobData.min_salary === '' || isNaN(Number(jobData.min_salary))
          ? null
          : Number(jobData.min_salary);
      }
      if (jobData.max_salary !== undefined) {
        jobData.max_salary = jobData.max_salary === '' || isNaN(Number(jobData.max_salary))
          ? null
          : Number(jobData.max_salary);
      }

      // DEBUG: jobData to be saved (log removed)

      // Create the job with safer error handling
      Job.create(jobData, (err, result) => {
        if (err) {
          console.error("Error inserting job data: ", err);
          return res.status(500).json({ message: "Database insertion error", error: err.message });
        }
        res.status(201).json({ message: "Job posted successfully", job_id: result.insertId });
      });
    } catch (error) {
      console.error("Error in createJob controller:", error);
      res.status(500).json({ message: "Internal Server Error", error: error.message });
    }
  },

  // Update job
  updateJob: async (req, res) => {
    try {
      const jobId = req.params.job_id;
      // Extract form fields from request body
      const jobData = { ...req.body };
      
      // Handle file uploads if they exist
      if (req.files) {
        
        // Process job post image
        if (req.files.job_post_image && req.files.job_post_image[0]) {
          try {
            // If Cloudinary is configured, upload the image
            if (cloudinaryConfigured) {
              // Convert the file buffer to base64 for Cloudinary upload
              const fileBuffer = req.files.job_post_image[0].buffer;
              const base64String = `data:${req.files.job_post_image[0].mimetype};base64,${fileBuffer.toString('base64')}`;
              
              // Upload to Cloudinary
              try {
                const uploadResult = await cloudinary.uploader.upload(base64String, {
                  folder: 'job_post_images',
                  upload_preset: process.env.CLOUDINARY_UPLOAD_PRESET || undefined
                });
                
                // Add the Cloudinary URL to the job data
                jobData.job_post_image = uploadResult.secure_url;
              } catch (cloudinaryError) {
                console.error("Cloudinary upload error for job image update:", cloudinaryError);
                // Fall back to storing in database if Cloudinary fails
                jobData.job_post_image = req.files.job_post_image[0].buffer;
              }
            } else {
              // Cloudinary not configured, store image in database
              jobData.job_post_image = req.files.job_post_image[0].buffer;
            }
          } catch (fileProcessingError) {
            console.error("Error processing job post image file for update:", fileProcessingError);
            return res.status(500).json({ message: "File processing error during update", error: fileProcessingError.message });
          }
        }
        
        // Process job post thumbnail
        if (req.files.job_post_thumbnail && req.files.job_post_thumbnail[0]) {
          try {
            // If Cloudinary is configured, upload the thumbnail
            if (cloudinaryConfigured) {
              // Convert the file buffer to base64 for Cloudinary upload
              const fileBuffer = req.files.job_post_thumbnail[0].buffer;
              const base64String = `data:${req.files.job_post_thumbnail[0].mimetype};base64,${fileBuffer.toString('base64')}`;
              
              // Upload to Cloudinary
              try {
                const uploadResult = await cloudinary.uploader.upload(base64String, {
                  folder: 'job_post_thumbnails',
                  upload_preset: process.env.CLOUDINARY_UPLOAD_PRESET || undefined
                });
                
                // Add the Cloudinary URL to the job data
                jobData.job_post_thumbnail = uploadResult.secure_url;
              } catch (cloudinaryError) {
                console.error("Cloudinary upload error for job thumbnail update:", cloudinaryError);
                // Fall back to storing in database if Cloudinary fails
                jobData.job_post_thumbnail = req.files.job_post_thumbnail[0].buffer;
              }
            } else {
              // Cloudinary not configured, store thumbnail in database
              jobData.job_post_thumbnail = req.files.job_post_thumbnail[0].buffer;
            }
          } catch (fileProcessingError) {
            console.error("Error processing job post thumbnail file for update:", fileProcessingError);
            return res.status(500).json({ message: "File processing error during update", error: fileProcessingError.message });
          }
        }
        
        // Process company logo
        if (req.files.company_logo && req.files.company_logo[0]) {
          try {
            // If Cloudinary is configured, upload the logo
            if (cloudinaryConfigured) {
              // Convert the file buffer to base64 for Cloudinary upload
              const fileBuffer = req.files.company_logo[0].buffer;
              const base64String = `data:${req.files.company_logo[0].mimetype};base64,${fileBuffer.toString('base64')}`;
              
              // Upload to Cloudinary
              try {
                const uploadResult = await cloudinary.uploader.upload(base64String, {
                  folder: 'company_logos',
                  upload_preset: process.env.CLOUDINARY_UPLOAD_PRESET || undefined
                });
                
                // Add the Cloudinary URL to the job data
                jobData.company_logo = uploadResult.secure_url;
              } catch (cloudinaryError) {
                console.error("Cloudinary upload error for company logo update:", cloudinaryError);
                // Fall back to storing in database if Cloudinary fails
                jobData.company_logo = req.files.company_logo[0].buffer;
              }
            } else {
              // Cloudinary not configured, store logo in database
              jobData.company_logo = req.files.company_logo[0].buffer;
            }
          } catch (fileProcessingError) {
            console.error("Error processing company logo file for update:", fileProcessingError);
            return res.status(500).json({ message: "File processing error during update", error: fileProcessingError.message });
          }
        }
      }
      
      // Use existing company logo URL if provided
      if (req.body.existing_company_logo_url) {
        jobData.company_logo = req.body.existing_company_logo_url;
      }
      
      // Use company_logo_url if provided
      if (req.body.company_logo_url) {
        jobData.company_logo = req.body.company_logo_url;
      }
      
      // Handle boolean values from form data
      if (jobData.hot === 'true') jobData.hot = true;
      if (jobData.hot === 'false') jobData.hot = false;
      
      // Handle empty salary value to ensure it gets saved as NULL
      if (jobData.salary_range === '') {
        jobData.salary_range = null;
      }

      // Handle min_salary and max_salary
      if (jobData.min_salary === '') jobData.min_salary = null;
      if (jobData.max_salary === '') jobData.max_salary = null;
      
      // Handle sub_topics with better error handling
      if (jobData.sub_topics) {
        // Process sub_topics from update request
        
        if (typeof jobData.sub_topics === 'string') {
          try {
            // Try to parse as JSON
            const parsed = JSON.parse(jobData.sub_topics);
            // Successfully parsed sub_topics for update
            
            // Verify it's an array after parsing
            if (Array.isArray(parsed)) {
              jobData.sub_topics = parsed;
            } else {
              // Handle non-array parsed value for update
              jobData.sub_topics = [String(jobData.sub_topics)];
            }
          } catch (e) {
            // Unable to parse as JSON for update, treat as string
            // If parsing fails, treat as a single string item
            jobData.sub_topics = [String(jobData.sub_topics)];
          }
        } else if (Array.isArray(jobData.sub_topics)) {
          // Already an array, ensure all items are strings
          jobData.sub_topics = jobData.sub_topics.map(item => String(item));
        } else {
          // Not string or array, convert to string and wrap in array
          jobData.sub_topics = [String(jobData.sub_topics)];
        }
      } else {
        // If undefined or null, use empty array
        jobData.sub_topics = [];
      }
      
      Job.update(jobId, jobData, (err, result) => {
        if (err) {
          console.error("Error updating job data: ", err);
          return res.status(500).json({ message: "Database update error", error: err.message });
        }
        if (result.affectedRows === 0) {
          return res.status(404).json({ message: "Job not found" });
        }
        res.status(200).json({ message: "Job updated successfully" });
      });
    } catch (error) {
      console.error("Error in updateJob controller:", error);
      res.status(500).json({ message: "Internal Server Error", error: error.message, stack: error.stack });
    }
  },

  // Delete job and associated Cloudinary images
  deleteJob: async (req, res) => {
    const jobId = req.params.job_id;
    
    try {
      // First, get the job data to extract Cloudinary URLs
      Job.getById(jobId, async (err, jobResults) => {
        if (err) {
          console.error("Error fetching job for deletion: ", err);
          return res.status(500).json({ message: "Internal Server Error" });
        }
        
        if (jobResults.length === 0) {
          return res.status(404).json({ message: "Job not found" });
        }
        
        const job = jobResults[0];
        
        // Function to extract public_id from Cloudinary URL
        const extractPublicId = (url) => {
          if (!url || typeof url !== 'string') return null;
          
          // Check if it's a Cloudinary URL
          if (url.includes('res.cloudinary.com')) {
            try {
              // Extract public_id from URL like: https://res.cloudinary.com/cloud_name/image/upload/v123456/folder/image_id.jpg
              const urlParts = url.split('/');
              const uploadIndex = urlParts.indexOf('upload');
              if (uploadIndex !== -1 && uploadIndex < urlParts.length - 1) {
                // Get everything after 'upload' and before file extension
                const pathAfterUpload = urlParts.slice(uploadIndex + 1).join('/');
                // Remove version if present (starts with 'v' followed by numbers)
                const withoutVersion = pathAfterUpload.replace(/^v\d+\//, '');
                // Remove file extension
                const publicId = withoutVersion.replace(/\.[^.]+$/, '');
                return publicId;
              }
            } catch (error) {
              console.error('Error extracting public_id from URL:', url, error);
            }
          }
          return null;
        };
        
        // Collect all Cloudinary public IDs to delete
        const publicIdsToDelete = [];
        
        // Check job_post_image
        if (job.job_post_image) {
          const publicId = extractPublicId(job.job_post_image);
          if (publicId) publicIdsToDelete.push(publicId);
        }
        
        // Check job_post_thumbnail
        if (job.job_post_thumbnail) {
          const publicId = extractPublicId(job.job_post_thumbnail);
          if (publicId) publicIdsToDelete.push(publicId);
        }
        
        // Check company_logo
        if (job.company_logo) {
          const publicId = extractPublicId(job.company_logo);
          if (publicId) publicIdsToDelete.push(publicId);
        }
        
        // Delete images from Cloudinary if configured and there are images to delete
        if (cloudinaryConfigured && publicIdsToDelete.length > 0) {
          try {
            // Remove console log to avoid extra logs
            // console.log(`Deleting ${publicIdsToDelete.length} images from Cloudinary for job ${jobId}:`, publicIdsToDelete);
            
            // Delete each image from Cloudinary
            const deletePromises = publicIdsToDelete.map(publicId => 
              cloudinary.uploader.destroy(publicId)
                .then(result => {
                  // Remove console log to avoid extra logs
                  // console.log(`Deleted image ${publicId} from Cloudinary:`, result);
                  return result;
                })
                .catch(error => {
                  console.error(`Failed to delete image ${publicId} from Cloudinary:`, error);
                  // Don't throw error, continue with job deletion even if image deletion fails
                  return { error: error.message };
                })
            );
            
            await Promise.all(deletePromises);
          } catch (cloudinaryError) {
            console.error('Error deleting images from Cloudinary:', cloudinaryError);
            // Continue with job deletion even if Cloudinary deletion fails
          }
        }
        
        // Now delete the job from database
        Job.delete(jobId, (deleteErr, result) => {
          if (deleteErr) {
            console.error("Error deleting job from database: ", deleteErr);
            return res.status(500).json({ message: "Internal Server Error" });
          }
          
          if (result.affectedRows === 0) {
            return res.status(404).json({ message: "Job not found" });
          }
          
          const message = publicIdsToDelete.length > 0 
            ? `Job and ${publicIdsToDelete.length} associated image(s) deleted successfully`
            : "Job deleted successfully";
          
          res.status(200).json({ message });
        });
      });
    } catch (error) {
      console.error('Error in deleteJob controller:', error);
      res.status(500).json({ message: "Internal Server Error", error: error.message });
    }
  },
  
  // Increment view count for a job
  incrementViewCount: (req, res) => {
    const jobId = req.params.job_id;
    
    Job.incrementViewCount(jobId, (err, result) => {
      if (err) {
        console.error("Error incrementing view count:", err);
        return res.status(500).json({ error: "Failed to update view count" });
      }
      
      if (result.affectedRows === 0) {
        return res.status(404).json({ error: "Job not found" });
      }
      
      res.status(200).json({ message: "View count incremented" });
    });
  }
};

module.exports = jobController;
