const express = require('express');
const router = express.Router();
const cvController = require('../controllers/cvController');
const cvUpload = require('../middleware/cvUploadMiddleware');

// GET - Get dashboard analytics for CVs
router.get('/dashboard', cvController.getDashboardAnalytics);

// GET - Get all CV submissions
router.get('/', cvController.getAllCVs);

// GET - Get CV by national ID
router.get('/:national_id', cvController.getCVByNationalId);

// GET - Serve CV file for viewing
router.get('/:national_id/file', cvController.serveCVFile);

// POST - Submit a new CV or update existing one
router.post('/submit', cvUpload.fields([
  { name: 'cv_file', maxCount: 1 }
]), cvController.submitCV);

// DELETE - Delete CV by national ID
router.delete('/:national_id', cvController.deleteCV);

module.exports = router;