import React from 'react';
import { useNavigate } from 'react-router-dom';
import '../css/JobCard.css';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faBriefcase, faClock } from '@fortawesome/free-solid-svg-icons';
import { ApiService } from '../services/apiService';

const JobCard = ({ 
  id,
  hot,
  image,
  logo,
  company,
  position,
  location,
  salary,
  workTime,
  postedTime,
  views
}) => {
  const navigate = useNavigate();

  // Function to increment view count
  const incrementViewCount = async (jobId) => {
    try {
      await ApiService.jobs.recordView(jobId);
    } catch (err) {
      console.error("Error incrementing view count:", err);
    }
  };

  const handleJobClick = (e) => {
    e.preventDefault();
    e.stopPropagation();
    
    console.log('JobCard clicked, ID:', id);
    
    if (id) {
      // Increment view count
      incrementViewCount(id);
      
      // Scroll to top before navigation
      window.scrollTo({ top: 0, behavior: 'smooth' });
      
      // Navigate to job details with a small delay to ensure smooth scrolling
      setTimeout(() => {
        navigate(`/job/${id}`);
      }, 100);
    } else {
      console.warn('No job ID provided for navigation');
    }
  };

  // Use default image if none provided
  const jobImage = image || "https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=140&q=80";
  
  // Use default logo if none provided
  const companyLogo = logo || "https://via.placeholder.com/30";
  
  // Format salary as Rs. with comma separators
  const formatSalary = (salaryValue) => {
    if (!salaryValue) return 'Unknown';
    let numeric = salaryValue;
    if (typeof salaryValue === 'string') {
      numeric = salaryValue.replace(/K|k|,/g, '');
    }
    let salary = parseFloat(numeric);
    if (!isNaN(salary)) {
      if (typeof salaryValue === 'string' && salaryValue.toLowerCase().includes('k')) {
        salary = salary * 1000;
      }
      return 'Rs. ' + salary.toLocaleString('en-IN');
    }
    return salaryValue;
  };
  
  return (
    <div className="job-card" onClick={handleJobClick} style={{ cursor: 'pointer' }}>
      {hot && (
        <span className="urgent-tag jobcard-urgent-tag">🔥 URGENT</span>
      )}
      <div className="job-card-image">
        <img src={jobImage} alt={position} onError={(e) => {
          e.target.onerror = null;
          e.target.src = "https://via.placeholder.com/400x140/f5f5f5/999999?text=Job+Image";
        }} />
      </div>
      <div className="job-card-content">
          <div className="company-info-urgent">
            <div className="company-logo-urgent">
              <img 
                src={logo} 
                alt={company}
                onError={(e) => {
                  e.target.onerror = null;
                  e.target.src = 'https://via.placeholder.com/30';
                }}
              />
            </div>
            <div className="company-details-urgent">
              <h3 className="company-name-urgent">{company}</h3>
            </div>
          </div>
        <h3 className="job-title">{position}</h3>
        <div className="job-meta">
          <div className="job-location">
            <FontAwesomeIcon icon={faBriefcase} className="icon" />
            {location}
          </div>
          <div className="job-time">
            <FontAwesomeIcon icon={faClock} className="icon" />
            {workTime}
          </div>
        </div>
        <div className="job-footer">
          <div className="job-salary">
            {formatSalary(salary)}
          </div>
          <div className="job-date">
            {postedTime}
          </div>
        </div>
      </div>
    </div>
  );
};

export default JobCard;
