[{"D:\\Thirmaa Office\\job_page\\client\\src\\index.js": "1", "D:\\Thirmaa Office\\job_page\\client\\src\\reportWebVitals.js": "2", "D:\\Thirmaa Office\\job_page\\client\\src\\App.js": "3", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Footer.jsx": "4", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Navbar.jsx": "5", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\NewsLetter.jsx": "6", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\SearchArea.jsx": "7", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\JobDetailsDis.jsx": "8", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Banner.jsx": "9", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\ContactUs.jsx": "10", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\JobDetails.jsx": "11", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\AdminLogin.jsx": "12", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\JobBoard.jsx": "13", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Companies.jsx": "14", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\FAQ.jsx": "15", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\HomePage.jsx": "16", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\JobCard.jsx": "17", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\BlogArticle.jsx": "18", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\AboutUs.jsx": "19", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\JobBlog.jsx": "20", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Admin\\AdminPanel.jsx": "21", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\FeaturedCompanies.jsx": "22", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\FeaturedSection.jsx": "23", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\SocialMediaFeeds.jsx": "24", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Admin\\BlogAdmin.jsx": "25", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Admin\\AnalyticsDashboard.jsx": "26", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Admin\\CompaniesAdmin.jsx": "27", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Admin\\JobsAdmin.jsx": "28", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Admin\\CvAdmin.jsx": "29", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\CompanyDetails.jsx": "30", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Cv.jsx": "31", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\FullDetails.jsx": "32", "D:\\Thirmaa Office\\job_page\\client\\src\\contexts\\LocationContext.js": "33", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\UrgentJobCard.jsx": "34", "D:\\Thirmaa Office\\job_page\\client\\src\\services\\api.js": "35", "D:\\Thirmaa Office\\job_page\\client\\src\\services\\apiService.js": "36", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Admin\\AdminUsers.jsx": "37", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\ProtectedRoute.jsx": "38", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\PrivacyPolicy.jsx": "39", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\TermsAndConditions.jsx": "40", "D:\\Thirmaa Office\\job_page\\client\\src\\contexts\\JobFilterContext.jsx": "41", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\PageHelmet.jsx": "42"}, {"size": 653, "mtime": 1749805078395, "results": "43", "hashOfConfig": "44"}, {"size": 375, "mtime": 1748403601275, "results": "45", "hashOfConfig": "44"}, {"size": 6285, "mtime": 1749805078336, "results": "46", "hashOfConfig": "44"}, {"size": 2193, "mtime": 1749466327689, "results": "47", "hashOfConfig": "44"}, {"size": 2844, "mtime": 1749466327693, "results": "48", "hashOfConfig": "44"}, {"size": 2491, "mtime": 1748403601256, "results": "49", "hashOfConfig": "44"}, {"size": 14414, "mtime": 1749805078382, "results": "50", "hashOfConfig": "44"}, {"size": 6247, "mtime": 1748403601256, "results": "51", "hashOfConfig": "44"}, {"size": 299, "mtime": 1748403601256, "results": "52", "hashOfConfig": "44"}, {"size": 9342, "mtime": 1749805078372, "results": "53", "hashOfConfig": "44"}, {"size": 21394, "mtime": 1749805078379, "results": "54", "hashOfConfig": "44"}, {"size": 3127, "mtime": 1749805078356, "results": "55", "hashOfConfig": "44"}, {"size": 39513, "mtime": 1749805078378, "results": "56", "hashOfConfig": "44"}, {"size": 6874, "mtime": 1749805078370, "results": "57", "hashOfConfig": "44"}, {"size": 8819, "mtime": 1749805078374, "results": "58", "hashOfConfig": "44"}, {"size": 30095, "mtime": 1749805078375, "results": "59", "hashOfConfig": "44"}, {"size": 3918, "mtime": 1749805078378, "results": "60", "hashOfConfig": "44"}, {"size": 11125, "mtime": 1749805078369, "results": "61", "hashOfConfig": "44"}, {"size": 19481, "mtime": 1749805078337, "results": "62", "hashOfConfig": "44"}, {"size": 13231, "mtime": 1749805078376, "results": "63", "hashOfConfig": "44"}, {"size": 12658, "mtime": 1749800356799, "results": "64", "hashOfConfig": "44"}, {"size": 3837, "mtime": 1749800356814, "results": "65", "hashOfConfig": "44"}, {"size": 9842, "mtime": 1749805078374, "results": "66", "hashOfConfig": "44"}, {"size": 2575, "mtime": 1749805078383, "results": "67", "hashOfConfig": "44"}, {"size": 23312, "mtime": 1749805078351, "results": "68", "hashOfConfig": "44"}, {"size": 13942, "mtime": 1749800356800, "results": "69", "hashOfConfig": "44"}, {"size": 17835, "mtime": 1749805078352, "results": "70", "hashOfConfig": "44"}, {"size": 59444, "mtime": 1749805078355, "results": "71", "hashOfConfig": "44"}, {"size": 16900, "mtime": 1749816560425, "results": "72", "hashOfConfig": "44"}, {"size": 7028, "mtime": 1749805078371, "results": "73", "hashOfConfig": "44"}, {"size": 23259, "mtime": 1749805078373, "results": "74", "hashOfConfig": "44"}, {"size": 13722, "mtime": 1749800356816, "results": "75", "hashOfConfig": "44"}, {"size": 1193, "mtime": 1749466288746, "results": "76", "hashOfConfig": "44"}, {"size": 2260, "mtime": 1749466327694, "results": "77", "hashOfConfig": "44"}, {"size": 2070, "mtime": 1749800356847, "results": "78", "hashOfConfig": "44"}, {"size": 3256, "mtime": 1749805078397, "results": "79", "hashOfConfig": "44"}, {"size": 22346, "mtime": 1749805078350, "results": "80", "hashOfConfig": "44"}, {"size": 1015, "mtime": 1749800356822, "results": "81", "hashOfConfig": "44"}, {"size": 3794, "mtime": 1749805078380, "results": "82", "hashOfConfig": "44"}, {"size": 3783, "mtime": 1749805078384, "results": "83", "hashOfConfig": "44"}, {"size": 4156, "mtime": 1749805078384, "results": "84", "hashOfConfig": "44"}, {"size": 578, "mtime": 1749805078380, "results": "85", "hashOfConfig": "44"}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "qmit6d", {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Thirmaa Office\\job_page\\client\\src\\index.js", [], [], "D:\\Thirmaa Office\\job_page\\client\\src\\reportWebVitals.js", [], [], "D:\\Thirmaa Office\\job_page\\client\\src\\App.js", ["212"], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Footer.jsx", ["213", "214", "215", "216"], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Navbar.jsx", [], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\NewsLetter.jsx", [], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\SearchArea.jsx", ["217"], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\JobDetailsDis.jsx", [], ["218", "219", "220", "221", "222", "223", "224", "225", "226", "227"], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Banner.jsx", [], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\ContactUs.jsx", [], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\JobDetails.jsx", ["228", "229"], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\AdminLogin.jsx", [], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\JobBoard.jsx", ["230", "231", "232"], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Companies.jsx", ["233", "234"], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\FAQ.jsx", [], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\HomePage.jsx", ["235", "236", "237", "238", "239", "240", "241", "242", "243"], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\JobCard.jsx", ["244"], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\BlogArticle.jsx", [], ["245", "246", "247", "248"], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\AboutUs.jsx", ["249", "250", "251", "252", "253", "254", "255", "256"], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\JobBlog.jsx", ["257", "258", "259", "260", "261"], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Admin\\AdminPanel.jsx", [], ["262", "263", "264", "265", "266", "267", "268", "269", "270", "271", "272", "273", "274", "275", "276"], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\FeaturedCompanies.jsx", [], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\FeaturedSection.jsx", ["277", "278", "279", "280", "281", "282"], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\SocialMediaFeeds.jsx", [], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Admin\\BlogAdmin.jsx", [], ["283", "284", "285"], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Admin\\AnalyticsDashboard.jsx", [], ["286", "287", "288", "289", "290", "291"], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Admin\\CompaniesAdmin.jsx", [], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Admin\\JobsAdmin.jsx", [], ["292", "293", "294", "295", "296"], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Admin\\CvAdmin.jsx", [], ["297"], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\CompanyDetails.jsx", [], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Cv.jsx", ["298", "299"], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\FullDetails.jsx", ["300", "301", "302", "303"], [], "D:\\Thirmaa Office\\job_page\\client\\src\\contexts\\LocationContext.js", [], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\UrgentJobCard.jsx", [], [], "D:\\Thirmaa Office\\job_page\\client\\src\\services\\api.js", [], [], "D:\\Thirmaa Office\\job_page\\client\\src\\services\\apiService.js", [], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Admin\\AdminUsers.jsx", [], ["304", "305", "306", "307"], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\ProtectedRoute.jsx", [], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\PrivacyPolicy.jsx", [], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\TermsAndConditions.jsx", [], [], "D:\\Thirmaa Office\\job_page\\client\\src\\contexts\\JobFilterContext.jsx", [], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\PageHelmet.jsx", [], [], {"ruleId": "308", "severity": 1, "message": "309", "line": 7, "column": 8, "nodeType": "310", "messageId": "311", "endLine": 7, "endColumn": 18}, {"ruleId": "308", "severity": 1, "message": "312", "line": 4, "column": 10, "nodeType": "310", "messageId": "311", "endLine": 4, "endColumn": 20}, {"ruleId": "308", "severity": 1, "message": "313", "line": 4, "column": 22, "nodeType": "310", "messageId": "311", "endLine": 4, "endColumn": 30}, {"ruleId": "308", "severity": 1, "message": "314", "line": 4, "column": 32, "nodeType": "310", "messageId": "311", "endLine": 4, "endColumn": 43}, {"ruleId": "308", "severity": 1, "message": "315", "line": 4, "column": 45, "nodeType": "310", "messageId": "311", "endLine": 4, "endColumn": 55}, {"ruleId": "308", "severity": 1, "message": "316", "line": 71, "column": 12, "nodeType": "310", "messageId": "311", "endLine": 71, "endColumn": 22}, {"ruleId": "317", "severity": 1, "message": "318", "line": 19, "column": 11, "nodeType": "319", "endLine": 19, "endColumn": 23, "suppressions": "320"}, {"ruleId": "317", "severity": 1, "message": "318", "line": 19, "column": 34, "nodeType": "319", "endLine": 19, "endColumn": 46, "suppressions": "321"}, {"ruleId": "317", "severity": 1, "message": "318", "line": 30, "column": 15, "nodeType": "319", "endLine": 30, "endColumn": 27, "suppressions": "322"}, {"ruleId": "317", "severity": 1, "message": "318", "line": 30, "column": 38, "nodeType": "319", "endLine": 30, "endColumn": 50, "suppressions": "323"}, {"ruleId": "317", "severity": 1, "message": "318", "line": 126, "column": 15, "nodeType": "319", "endLine": 126, "endColumn": 60, "suppressions": "324"}, {"ruleId": "317", "severity": 1, "message": "318", "line": 130, "column": 15, "nodeType": "319", "endLine": 130, "endColumn": 59, "suppressions": "325"}, {"ruleId": "317", "severity": 1, "message": "318", "line": 134, "column": 15, "nodeType": "319", "endLine": 134, "endColumn": 59, "suppressions": "326"}, {"ruleId": "317", "severity": 1, "message": "318", "line": 138, "column": 15, "nodeType": "319", "endLine": 138, "endColumn": 59, "suppressions": "327"}, {"ruleId": "317", "severity": 1, "message": "318", "line": 141, "column": 15, "nodeType": "319", "endLine": 141, "endColumn": 60, "suppressions": "328"}, {"ruleId": "317", "severity": 1, "message": "318", "line": 144, "column": 15, "nodeType": "319", "endLine": 144, "endColumn": 61, "suppressions": "329"}, {"ruleId": "308", "severity": 1, "message": "330", "line": 13, "column": 3, "nodeType": "310", "messageId": "311", "endLine": 13, "endColumn": 13}, {"ruleId": "308", "severity": 1, "message": "331", "line": 14, "column": 3, "nodeType": "310", "messageId": "311", "endLine": 14, "endColumn": 14}, {"ruleId": "308", "severity": 1, "message": "332", "line": 14, "column": 3, "nodeType": "310", "messageId": "311", "endLine": 14, "endColumn": 9}, {"ruleId": "308", "severity": 1, "message": "333", "line": 25, "column": 9, "nodeType": "310", "messageId": "311", "endLine": 25, "endColumn": 17}, {"ruleId": "334", "severity": 1, "message": "335", "line": 158, "column": 6, "nodeType": "336", "endLine": 158, "endColumn": 41, "suggestions": "337"}, {"ruleId": "308", "severity": 1, "message": "338", "line": 4, "column": 45, "nodeType": "310", "messageId": "311", "endLine": 4, "endColumn": 51}, {"ruleId": "308", "severity": 1, "message": "339", "line": 20, "column": 9, "nodeType": "310", "messageId": "311", "endLine": 20, "endColumn": 20}, {"ruleId": "308", "severity": 1, "message": "340", "line": 5, "column": 10, "nodeType": "310", "messageId": "311", "endLine": 5, "endColumn": 24}, {"ruleId": "308", "severity": 1, "message": "341", "line": 5, "column": 35, "nodeType": "310", "messageId": "311", "endLine": 5, "endColumn": 40}, {"ruleId": "308", "severity": 1, "message": "330", "line": 5, "column": 105, "nodeType": "310", "messageId": "311", "endLine": 5, "endColumn": 115}, {"ruleId": "308", "severity": 1, "message": "342", "line": 5, "column": 117, "nodeType": "310", "messageId": "311", "endLine": 5, "endColumn": 124}, {"ruleId": "308", "severity": 1, "message": "343", "line": 5, "column": 126, "nodeType": "310", "messageId": "311", "endLine": 5, "endColumn": 132}, {"ruleId": "308", "severity": 1, "message": "309", "line": 8, "column": 8, "nodeType": "310", "messageId": "311", "endLine": 8, "endColumn": 18}, {"ruleId": "308", "severity": 1, "message": "344", "line": 13, "column": 8, "nodeType": "310", "messageId": "311", "endLine": 13, "endColumn": 25}, {"ruleId": "308", "severity": 1, "message": "345", "line": 277, "column": 9, "nodeType": "310", "messageId": "311", "endLine": 277, "endColumn": 19}, {"ruleId": "308", "severity": 1, "message": "346", "line": 339, "column": 9, "nodeType": "310", "messageId": "311", "endLine": 339, "endColumn": 27}, {"ruleId": "308", "severity": 1, "message": "347", "line": 58, "column": 9, "nodeType": "310", "messageId": "311", "endLine": 58, "endColumn": 20}, {"ruleId": "308", "severity": 1, "message": "348", "line": 7, "column": 8, "nodeType": "310", "messageId": "311", "endLine": 7, "endColumn": 17, "suppressions": "349"}, {"ruleId": "308", "severity": 1, "message": "350", "line": 23, "column": 9, "nodeType": "310", "messageId": "311", "endLine": 23, "endColumn": 19, "suppressions": "351"}, {"ruleId": "308", "severity": 1, "message": "352", "line": 33, "column": 9, "nodeType": "310", "messageId": "311", "endLine": 33, "endColumn": 20, "suppressions": "353"}, {"ruleId": "334", "severity": 1, "message": "354", "line": 54, "column": 6, "nodeType": "336", "endLine": 54, "endColumn": 26, "suggestions": "355", "suppressions": "356"}, {"ruleId": "308", "severity": 1, "message": "309", "line": 4, "column": 8, "nodeType": "310", "messageId": "311", "endLine": 4, "endColumn": 18}, {"ruleId": "308", "severity": 1, "message": "357", "line": 5, "column": 8, "nodeType": "310", "messageId": "311", "endLine": 5, "endColumn": 17}, {"ruleId": "308", "severity": 1, "message": "358", "line": 82, "column": 10, "nodeType": "310", "messageId": "311", "endLine": 82, "endColumn": 25}, {"ruleId": "308", "severity": 1, "message": "359", "line": 82, "column": 27, "nodeType": "310", "messageId": "311", "endLine": 82, "endColumn": 45}, {"ruleId": "308", "severity": 1, "message": "360", "line": 84, "column": 9, "nodeType": "310", "messageId": "311", "endLine": 84, "endColumn": 19}, {"ruleId": "308", "severity": 1, "message": "361", "line": 107, "column": 10, "nodeType": "310", "messageId": "311", "endLine": 107, "endColumn": 18}, {"ruleId": "308", "severity": 1, "message": "362", "line": 107, "column": 20, "nodeType": "310", "messageId": "311", "endLine": 107, "endColumn": 31}, {"ruleId": "308", "severity": 1, "message": "363", "line": 109, "column": 9, "nodeType": "310", "messageId": "311", "endLine": 109, "endColumn": 23}, {"ruleId": "308", "severity": 1, "message": "364", "line": 2, "column": 20, "nodeType": "310", "messageId": "311", "endLine": 2, "endColumn": 30}, {"ruleId": "308", "severity": 1, "message": "365", "line": 2, "column": 74, "nodeType": "310", "messageId": "311", "endLine": 2, "endColumn": 93}, {"ruleId": "308", "severity": 1, "message": "348", "line": 6, "column": 8, "nodeType": "310", "messageId": "311", "endLine": 6, "endColumn": 17}, {"ruleId": "308", "severity": 1, "message": "366", "line": 133, "column": 9, "nodeType": "310", "messageId": "311", "endLine": 133, "endColumn": 23}, {"ruleId": "308", "severity": 1, "message": "367", "line": 175, "column": 9, "nodeType": "310", "messageId": "311", "endLine": 175, "endColumn": 22}, {"ruleId": "308", "severity": 1, "message": "368", "line": 7, "column": 3, "nodeType": "310", "messageId": "311", "endLine": 7, "endColumn": 10, "suppressions": "369"}, {"ruleId": "308", "severity": 1, "message": "370", "line": 8, "column": 3, "nodeType": "310", "messageId": "311", "endLine": 8, "endColumn": 13, "suppressions": "371"}, {"ruleId": "308", "severity": 1, "message": "372", "line": 9, "column": 3, "nodeType": "310", "messageId": "311", "endLine": 9, "endColumn": 13, "suppressions": "373"}, {"ruleId": "308", "severity": 1, "message": "374", "line": 10, "column": 3, "nodeType": "310", "messageId": "311", "endLine": 10, "endColumn": 8, "suppressions": "375"}, {"ruleId": "308", "severity": 1, "message": "332", "line": 12, "column": 3, "nodeType": "310", "messageId": "311", "endLine": 12, "endColumn": 9, "suppressions": "376"}, {"ruleId": "308", "severity": 1, "message": "377", "line": 13, "column": 3, "nodeType": "310", "messageId": "311", "endLine": 13, "endColumn": 11, "suppressions": "378"}, {"ruleId": "308", "severity": 1, "message": "379", "line": 111, "column": 10, "nodeType": "310", "messageId": "311", "endLine": 111, "endColumn": 22, "suppressions": "380"}, {"ruleId": "308", "severity": 1, "message": "381", "line": 112, "column": 10, "nodeType": "310", "messageId": "311", "endLine": 112, "endColumn": 21, "suppressions": "382"}, {"ruleId": "308", "severity": 1, "message": "383", "line": 113, "column": 10, "nodeType": "310", "messageId": "311", "endLine": 113, "endColumn": 19, "suppressions": "384"}, {"ruleId": "308", "severity": 1, "message": "385", "line": 113, "column": 21, "nodeType": "310", "messageId": "311", "endLine": 113, "endColumn": 33, "suppressions": "386"}, {"ruleId": "308", "severity": 1, "message": "387", "line": 119, "column": 9, "nodeType": "310", "messageId": "311", "endLine": 119, "endColumn": 26, "suppressions": "388"}, {"ruleId": "308", "severity": 1, "message": "389", "line": 127, "column": 9, "nodeType": "310", "messageId": "311", "endLine": 127, "endColumn": 26, "suppressions": "390"}, {"ruleId": "308", "severity": 1, "message": "391", "line": 139, "column": 9, "nodeType": "310", "messageId": "311", "endLine": 139, "endColumn": 25, "suppressions": "392"}, {"ruleId": "308", "severity": 1, "message": "393", "line": 151, "column": 9, "nodeType": "310", "messageId": "311", "endLine": 151, "endColumn": 21, "suppressions": "394"}, {"ruleId": "308", "severity": 1, "message": "395", "line": 177, "column": 9, "nodeType": "310", "messageId": "311", "endLine": 177, "endColumn": 24, "suppressions": "396"}, {"ruleId": "308", "severity": 1, "message": "397", "line": 8, "column": 3, "nodeType": "310", "messageId": "311", "endLine": 8, "endColumn": 12}, {"ruleId": "308", "severity": 1, "message": "398", "line": 10, "column": 3, "nodeType": "310", "messageId": "311", "endLine": 10, "endColumn": 12}, {"ruleId": "308", "severity": 1, "message": "399", "line": 13, "column": 3, "nodeType": "310", "messageId": "311", "endLine": 13, "endColumn": 13}, {"ruleId": "308", "severity": 1, "message": "400", "line": 16, "column": 3, "nodeType": "310", "messageId": "311", "endLine": 16, "endColumn": 16}, {"ruleId": "308", "severity": 1, "message": "401", "line": 17, "column": 3, "nodeType": "310", "messageId": "311", "endLine": 17, "endColumn": 17}, {"ruleId": "308", "severity": 1, "message": "402", "line": 18, "column": 3, "nodeType": "310", "messageId": "311", "endLine": 18, "endColumn": 15}, {"ruleId": "308", "severity": 1, "message": "403", "line": 10, "column": 3, "nodeType": "310", "messageId": "311", "endLine": 10, "endColumn": 16, "suppressions": "404"}, {"ruleId": "308", "severity": 1, "message": "405", "line": 14, "column": 3, "nodeType": "310", "messageId": "311", "endLine": 14, "endColumn": 16, "suppressions": "406"}, {"ruleId": "308", "severity": 1, "message": "407", "line": 21, "column": 8, "nodeType": "310", "messageId": "311", "endLine": 21, "endColumn": 13, "suppressions": "408"}, {"ruleId": "308", "severity": 1, "message": "409", "line": 9, "column": 3, "nodeType": "310", "messageId": "311", "endLine": 9, "endColumn": 14, "suppressions": "410"}, {"ruleId": "308", "severity": 1, "message": "411", "line": 16, "column": 3, "nodeType": "310", "messageId": "311", "endLine": 16, "endColumn": 13, "suppressions": "412"}, {"ruleId": "308", "severity": 1, "message": "413", "line": 21, "column": 3, "nodeType": "310", "messageId": "311", "endLine": 21, "endColumn": 15, "suppressions": "414"}, {"ruleId": "308", "severity": 1, "message": "415", "line": 38, "column": 10, "nodeType": "310", "messageId": "311", "endLine": 38, "endColumn": 25, "suppressions": "416"}, {"ruleId": "308", "severity": 1, "message": "417", "line": 168, "column": 9, "nodeType": "310", "messageId": "311", "endLine": 168, "endColumn": 24, "suppressions": "418"}, {"ruleId": "308", "severity": 1, "message": "419", "line": 177, "column": 9, "nodeType": "310", "messageId": "311", "endLine": 177, "endColumn": 21, "suppressions": "420"}, {"ruleId": "308", "severity": 1, "message": "421", "line": 4, "column": 38, "nodeType": "310", "messageId": "311", "endLine": 4, "endColumn": 44, "suppressions": "422"}, {"ruleId": "308", "severity": 1, "message": "403", "line": 12, "column": 3, "nodeType": "310", "messageId": "311", "endLine": 12, "endColumn": 16, "suppressions": "423"}, {"ruleId": "334", "severity": 1, "message": "424", "line": 292, "column": 6, "nodeType": "336", "endLine": 292, "endColumn": 19, "suggestions": "425", "suppressions": "426"}, {"ruleId": "308", "severity": 1, "message": "427", "line": 481, "column": 9, "nodeType": "310", "messageId": "311", "endLine": 481, "endColumn": 27, "suppressions": "428"}, {"ruleId": "429", "severity": 1, "message": "430", "line": 549, "column": 25, "nodeType": "431", "messageId": "432", "endLine": 549, "endColumn": 26, "suggestions": "433", "suppressions": "434"}, {"ruleId": "308", "severity": 1, "message": "435", "line": 4, "column": 46, "nodeType": "310", "messageId": "311", "endLine": 4, "endColumn": 54, "suppressions": "436"}, {"ruleId": "308", "severity": 1, "message": "437", "line": 1, "column": 27, "nodeType": "310", "messageId": "311", "endLine": 1, "endColumn": 36}, {"ruleId": "308", "severity": 1, "message": "438", "line": 2, "column": 10, "nodeType": "310", "messageId": "311", "endLine": 2, "endColumn": 14}, {"ruleId": "317", "severity": 1, "message": "318", "line": 330, "column": 17, "nodeType": "319", "endLine": 336, "endColumn": 18}, {"ruleId": "317", "severity": 1, "message": "318", "line": 339, "column": 17, "nodeType": "319", "endLine": 345, "endColumn": 18}, {"ruleId": "317", "severity": 1, "message": "318", "line": 348, "column": 17, "nodeType": "319", "endLine": 354, "endColumn": 18}, {"ruleId": "317", "severity": 1, "message": "318", "line": 357, "column": 17, "nodeType": "319", "endLine": 363, "endColumn": 18}, {"ruleId": "308", "severity": 1, "message": "403", "line": 10, "column": 3, "nodeType": "310", "messageId": "311", "endLine": 10, "endColumn": 16, "suppressions": "439"}, {"ruleId": "308", "severity": 1, "message": "413", "line": 18, "column": 3, "nodeType": "310", "messageId": "311", "endLine": 18, "endColumn": 15, "suppressions": "440"}, {"ruleId": "308", "severity": 1, "message": "419", "line": 111, "column": 9, "nodeType": "310", "messageId": "311", "endLine": 111, "endColumn": 21, "suppressions": "441"}, {"ruleId": "308", "severity": 1, "message": "442", "line": 309, "column": 9, "nodeType": "310", "messageId": "311", "endLine": 309, "endColumn": 18, "suppressions": "443"}, "no-unused-vars", "'NewsLetter' is defined but never used.", "Identifier", "unusedVar", "'FaFacebook' is defined but never used.", "'FaTiktok' is defined but never used.", "'FaInstagram' is defined but never used.", "'FaLinkedin' is defined but never used.", "'hasMounted' is assigned a value but never used.", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", ["444"], ["445"], ["446"], ["447"], ["448"], ["449"], ["450"], ["451"], ["452"], ["453"], "'faBookmark' is defined but never used.", "'faShieldAlt' is defined but never used.", "'faBell' is defined but never used.", "'navigate' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'maxSalary', 'minSalary', and 'selectedJobType'. Either include them or remove the dependency array.", "ArrayExpression", ["454"], "'faStar' is defined but never used.", "'refreshData' is assigned a value but never used.", "'faMapMarkerAlt' is defined but never used.", "'faEye' is defined but never used.", "'faShare' is defined but never used.", "'faBars' is defined but never used.", "'FeaturedCompanies' is defined but never used.", "'urgentJobs' is assigned a value but never used.", "'JobLoadingSkeleton' is assigned a value but never used.", "'companyLogo' is assigned a value but never used.", "'banneradd' is defined but never used.", ["455"], "'categories' is assigned a value but never used.", ["456"], "'popularTags' is assigned a value but never used.", ["457"], "React Hook useEffect has a missing dependency: 'fetchBlogById'. Either include it or remove the dependency array.", ["458"], ["459"], "'ContactUs' is defined but never used.", "'testimonialPage' is assigned a value but never used.", "'setTestimonialPage' is assigned a value but never used.", "'totalPages' is assigned a value but never used.", "'blogPage' is assigned a value but never used.", "'setBlogPage' is assigned a value but never used.", "'totalBlogPages' is assigned a value but never used.", "'FaBookmark' is defined but never used.", "'FaExclamationCircle' is defined but never used.", "'getLatestBlogs' is assigned a value but never used.", "'formatExcerpt' is assigned a value but never used.", "'faUsers' is defined but never used.", ["460"], "'faCalendar' is defined but never used.", ["461"], "'faComments' is defined but never used.", ["462"], "'faCog' is defined but never used.", ["463"], ["464"], "'faSearch' is defined but never used.", ["465"], "'imagePreview' is assigned a value but never used.", ["466"], "'logoPreview' is assigned a value but never used.", ["467"], "'activeTab' is assigned a value but never used.", ["468"], "'setActiveTab' is assigned a value but never used.", ["469"], "'handleInputChange' is assigned a value but never used.", ["470"], "'handleImageUpload' is assigned a value but never used.", ["471"], "'handleLogoUpload' is assigned a value but never used.", ["472"], "'handleSubmit' is assigned a value but never used.", ["473"], "'toggleAccordion' is assigned a value but never used.", ["474"], "'faTwitter' is defined but never used.", "'faYoutube' is defined but never used.", "'faFacebook' is defined but never used.", "'faChevronLeft' is defined but never used.", "'faChevronRight' is defined but never used.", "'faShareNodes' is defined but never used.", "'faCheckCircle' is defined but never used.", ["475"], "'faCalendarAlt' is defined but never used.", ["476"], "'axios' is defined but never used.", ["477"], "'faChartLine' is defined but never used.", ["478"], "'faIndustry' is defined but never used.", ["479"], "'faSignOutAlt' is defined but never used.", ["480"], "'recentCompanies' is assigned a value but never used.", ["481"], "'maxCompanyCount' is assigned a value but never used.", ["482"], "'handleLogout' is assigned a value but never used.", ["483"], "'useRef' is defined but never used.", ["484"], ["485"], "React Hook useEffect has a missing dependency: 'fetchCompanies'. Either include it or remove the dependency array.", ["486"], ["487"], "'handleToggleStatus' is assigned a value but never used.", ["488"], "no-useless-escape", "Unnecessary escape character: \\/.", "Literal", "unnecessaryEscape", ["489", "490"], ["491"], "'faFilter' is defined but never used.", ["492"], "'useEffect' is defined but never used.", "'Link' is defined but never used.", ["493"], ["494"], ["495"], "'canCreate' is assigned a value but never used.", ["496"], {"kind": "497", "justification": "498"}, {"kind": "497", "justification": "498"}, {"kind": "497", "justification": "498"}, {"kind": "497", "justification": "498"}, {"kind": "497", "justification": "498"}, {"kind": "497", "justification": "498"}, {"kind": "497", "justification": "498"}, {"kind": "497", "justification": "498"}, {"kind": "497", "justification": "498"}, {"kind": "497", "justification": "498"}, {"desc": "499", "fix": "500"}, {"kind": "497", "justification": "498"}, {"kind": "497", "justification": "498"}, {"kind": "497", "justification": "498"}, {"desc": "501", "fix": "502"}, {"kind": "497", "justification": "498"}, {"kind": "497", "justification": "498"}, {"kind": "497", "justification": "498"}, {"kind": "497", "justification": "498"}, {"kind": "497", "justification": "498"}, {"kind": "497", "justification": "498"}, {"kind": "497", "justification": "498"}, {"kind": "497", "justification": "498"}, {"kind": "497", "justification": "498"}, {"kind": "497", "justification": "498"}, {"kind": "497", "justification": "498"}, {"kind": "497", "justification": "498"}, {"kind": "497", "justification": "498"}, {"kind": "497", "justification": "498"}, {"kind": "497", "justification": "498"}, {"kind": "497", "justification": "498"}, {"kind": "497", "justification": "498"}, {"kind": "497", "justification": "498"}, {"kind": "497", "justification": "498"}, {"kind": "497", "justification": "498"}, {"kind": "497", "justification": "498"}, {"kind": "497", "justification": "498"}, {"kind": "497", "justification": "498"}, {"kind": "497", "justification": "498"}, {"kind": "497", "justification": "498"}, {"kind": "497", "justification": "498"}, {"kind": "497", "justification": "498"}, {"desc": "503", "fix": "504"}, {"kind": "497", "justification": "498"}, {"kind": "497", "justification": "498"}, {"messageId": "505", "fix": "506", "desc": "507"}, {"messageId": "508", "fix": "509", "desc": "510"}, {"kind": "497", "justification": "498"}, {"kind": "497", "justification": "498"}, {"kind": "497", "justification": "498"}, {"kind": "497", "justification": "498"}, {"kind": "497", "justification": "498"}, {"kind": "497", "justification": "498"}, "directive", "", "Update the dependencies array to be: [maxSalary, minSalary, searchParams, selectedJobType, setSelectedLocation]", {"range": "511", "text": "512"}, "Update the dependencies array to be: [location.state, id, fetchBlogById]", {"range": "513", "text": "514"}, "Update the dependencies array to be: [fetchCompanies, isModalOpen]", {"range": "515", "text": "516"}, "removeEscape", {"range": "517", "text": "498"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "518", "text": "519"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", [6045, 6080], "[max<PERSON><PERSON><PERSON>, minSalary, searchParams, selectedJobType, setSelectedLocation]", [1801, 1821], "[location.state, id, fetchBlogById]", [10469, 10482], "[fetchCompanies, isModalOpen]", [20099, 20100], [20099, 20099], "\\"]