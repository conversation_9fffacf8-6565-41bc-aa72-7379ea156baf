import { useState } from "react";
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faChevronRight, 
  faChevronLeft, 
  faBookmark, 
  faClock, 
  faMapMarkerAlt, 
  faHeart 
} from '@fortawesome/free-solid-svg-icons';
import "../css/JobListingPage.css";
import jobdetails from "../assets/jobdetails.png"; // location

import { useEffect } from "react";
import ApiService from "../services/apiService";

const JobListingPage = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const [jobListings, setJobListings] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchJobs = async () => {
      try {
        setLoading(true);
        const response = await ApiService.jobs.getAll();
        // Map backend jobs to the format needed for this page
        const jobs = response.data.map(job => ({
          id: job.job_id,
          title: job.job_title,
          location: job.main_topics,
          description: job.job_description,
          salary: job.salary_range,
          urgent: job.hot || false,
          senior: job.senior || false,
          postedAgo: job.created_at ? new Date(job.created_at).toLocaleDateString() : "Recently",
          company: job.company_name,
          logo: job.company_logo_url || job.company_logo || null,
        }));
        setJobListings(jobs);
        setError(null);
      } catch (err) {
        setError("Failed to load jobs from server");
      } finally {
        setLoading(false);
      }
    };
    fetchJobs();
  }, []);

  const categories = [
    { name: "Government", count: 42 },
    { name: "Foreign", count: 65 },
    { name: "Intern", count: 22 },
    { name: "Private", count: 39 }
    
  ];

  return (
    <div className="job-listing-page">
      <div className="container">
        {/* Header */}
        <div className="header">
          <h1>New Jobs</h1>
          <div className="header-line"></div>
        </div>

        {/* Tab navigation */}
        <div className="tab-navigation">
          <button className="tab-active">All</button>
          <button>Government Jobs</button>
          <button>Private Jobs</button>
          <button>Foreign Jobs</button>
          <button>Internships</button>
        </div>

        <div className="jobs-count">
          Showing 41-60 of 944 jobs
        </div>

        <div className="content-area">
          {/* Left Sidebar - Featured Jobs */}
          <div className="sidebar-left">
            {/* Job Card 1 */}
            <div className="featured-job-card">
              <span className="urgent-tag">URGENT</span>
              <div className="featured-profile-image">
                <img src={jobdetails} alt="Office meeting with diverse professionals" className="profile-image" />
              </div>
              <div className="featured-job-content">
                <div className="company-info">
                  <div className="company-badge">A</div>
                  <span className="company-name">Airbnotes</span>
                  <span className="job-type-tag">• Full-time</span>
                </div>
                <h3 className="featured-job-title">Senior Full Stack Engineer, Creator Success</h3>
                <div className="job-meta">
                  <div className="meta-item">
                    <FontAwesomeIcon icon={faClock} size="sm" />
                    <span>3 mins ago</span>
                  </div>
                  <div className="meta-item">
                    <FontAwesomeIcon icon={faMapMarkerAlt} size="sm" />
                    <span>Chicago</span>
                  </div>
                </div>
                <div className="job-actions">
                  <span className="job-salary">$120<span className="salary-period">/Month</span></span>
                </div>
              </div>
            </div>
            
            {/* Job Card 2 */}
            <div className="featured-job-card">
              <span className="urgent-tag">URGENT</span>
              <div className="featured-profile-image">
                <img src={jobdetails} alt="Office meeting with diverse professionals" className="profile-image" />
              </div>
              <div className="featured-job-content">
                <div className="company-info">
                  <div className="company-badge">A</div>
                  <span className="company-name">Airbnotes</span>
                  <span className="job-type-tag">• Full-time</span>
                </div>
                <h3 className="featured-job-title">Senior Full Stack Engineer, Creator Success</h3>
                <div className="job-meta">
                  <div className="meta-item">
                    <FontAwesomeIcon icon={faClock} size="sm" />
                    <span>3 mins ago</span>
                  </div>
                  <div className="meta-item">
                    <FontAwesomeIcon icon={faMapMarkerAlt} size="sm" />
                    <span>Chicago</span>
                  </div>
                </div>
                <div className="job-actions">
                  <span className="job-salary">$120<span className="salary-period">/Month</span></span>
                </div>
              </div>
            </div>
            
            {/* Recruiting Box */}
            <div className="recruiting-box">
              <h3>Recruiting?</h3>
              <p>Advertise your jobs to millions of monthly users and search 15.8 million CVs in our database.</p>
              <button className="post-job-btn">Post a Job</button>
              <div className="recruiting-image">
                <img src={jobdetails} alt="Recruiting illustration" width="120" height="80" />
              </div>
            </div>
          </div>

          {/* Main Content - Job Listings */}
          <div className="main-content">
            {loading ? (
              <div>Loading jobs...</div>
            ) : error ? (
              <div style={{color: 'red'}}>{error}</div>
            ) : jobListings.length === 0 ? (
              <div>No jobs found.</div>
            ) : (
              jobListings.map(job => (
                <div key={job.id} className="job-card">
                  <div className="job-card-wrapper">
                    <div className="job-card-left">
                      {/* Show company logo if available, else fallback */}
                      {job.logo ? (
                        <img src={job.logo} alt={job.company} className="job-thumbnail" style={{objectFit:'contain',width:'60px',height:'60px',background:'#fff',borderRadius:'8px',border:'1px solid #eee'}} onError={e => {e.target.onerror=null;e.target.src='https://via.placeholder.com/60'}} />
                      ) : (
                        <img src="https://via.placeholder.com/60" alt="No logo" className="job-thumbnail" style={{objectFit:'contain',width:'60px',height:'60px',background:'#fff',borderRadius:'8px',border:'1px solid #eee'}} />
                      )}
                    </div>
                    <div className="job-card-content">
                      <div className="job-header">
                        <div className="job-title-container">
                          <h3 className="job-title">{job.title}</h3>
                          <div className="job-salary">{job.salary}</div>
                        </div>
                      </div>
                      <div className="job-meta-info">
                        <div className="job-location">
                          <FontAwesomeIcon icon={faMapMarkerAlt} className="meta-icon" />
                          <span>{job.location}</span>
                        </div>
                        <div className="job-time">
                          <FontAwesomeIcon icon={faClock} className="meta-icon" />
                          <span>{job.postedAgo}</span>
                        </div>
                      </div>
                      <p className="job-description">{job.description}</p>
                      <div className="job-footer">
                        <div className="job-tags">
                          {job.urgent && (
                            <span className="tag urgent">Urgent</span>
                          )}
                          {job.senior && (
                            <span className="tag senior">Senior</span>
                          )}
                        </div>
                        <div className="job-actions">
                          <button className="action-button-small">
                            <FontAwesomeIcon icon={faHeart} />
                          </button>
                          <button className="action-button-small">
                            <FontAwesomeIcon icon={faBookmark} />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))
            )}
            
            {/* Pagination */}
            <div className="pagination">
              <button className="pagination-arrow">
                <FontAwesomeIcon icon={faChevronLeft} />
              </button>
              {[1, 2, 3, 4, 5, 6].map(page => (
                <button 
                  key={page} 
                  className={`pagination-number ${page === currentPage ? 'active' : ''}`}
                  onClick={() => setCurrentPage(page)}
                >
                  {page}
                </button>
              ))}
              <button className="pagination-arrow">
                <FontAwesomeIcon icon={faChevronRight} />
              </button>
            </div>
            
            <div className="view-more">
              <button className="view-more-btn">
                View more <FontAwesomeIcon icon={faChevronRight} />
              </button>
            </div>
          </div>

          {/* Right Sidebar - Categories */}
          <div className="sidebar-right">
            <div className="categories-box">
              <h3>Categories</h3>
              
              <div className="categories-grid">
                {categories.map((category, index) => (
                  <div 
                    key={index} 
                    className="category-card"
                  >
                    <div className="category-name">{category.name}</div>
                    <div className="category-count">{category.count}</div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default JobListingPage;