/* F.css */
.newsletter-container {
    position: relative;
    width: 100%;
    max-width: none;
    margin: 20px 0;
    padding: 0 calc(2% + 210px);
    box-sizing: border-box;
  }
  
  .newsletter-content-wrapper {
    max-width: 1200px;
    margin: 0 auto;
  }
  
  .newsletter-content-wrapper {
    position: relative;
    width: 100%;
    display: block;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  }
  
  .newsletter-image {
    width: 100%;
    display: block;
    height: 300px;
    object-fit: cover;
  }
  
  .newsletter-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 40px;
    box-sizing: border-box;
  }
  
  .newsletter-text { 
    padding-top: 60px;
    color: white;
    text-align: left;
  }
  
  .newsletter-text h1 {
    font-size: 32px;
    font-weight: bold;
    margin: 0;
    line-height: 1.2;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
  }
  
  /* Email box container */
  .email-container {
    display: flex;
    align-items: center;
    background: white;
    padding: 10px;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    width: 90%;
    max-width: 500px;
    /* margin: 0 auto; Removed for left alignment */
    align-self: flex-start; /* Align to the left */
  }
  
  /* Email input field */
  .email-input {
    border: none;
    outline: none;
    padding: 12px;
    font-size: 16px;
    flex: 1;
    width: 100%;
    border-radius: 10px;
  }
  
  /* Subscribe button */
  .subscribe-button {
    background: #7743DB;
    color: white;
    border: none;
    padding: 12px 20px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    border-radius: 10px;
    transition: background 0.3s ease;
  }
  
  .subscribe-button:hover {
    background: #6234c7;
  }
  
  /* Responsive Adjustments */
  @media (max-width: 992px) {
    .newsletter-container {
      width: 95%;
    }
  }
  
  @media (max-width: 768px) {
    .newsletter-overlay {
      padding: 20px;
    }
    
    .newsletter-text h1 {
      font-size: 24px;
    }
    
    .email-container {
      flex-direction: column;
      gap: 10px;
    }
    
    .email-input,
    .subscribe-button {
      width: 100%;
    }
    
    .newsletter-image {
      height: 280px;
    }
  }
  
  @media (max-width: 480px) {
    .newsletter-overlay {
      padding: 15px;
    }
    
    .newsletter-text h1 {
      font-size: 20px;
    }
    
    .newsletter-image {
      height: 250px;
    }
  }
  
  /* Image slider styles preserved for future use */
  .image-slider-container {
    display: flex;
    align-items: center;
    justify-self: center;
    width: 100%;
    margin: 0 auto;
  }
  
  .image-slider {
    display: flex;
    gap: 10px;
    overflow: hidden;
    white-space: nowrap;
    scroll-behavior: smooth;
    flex: 1;
    width: 100%;
  }
  
  .slider-box {
    flex: 0 0 calc(100% / 7);
    aspect-ratio: 4 / 1;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  }
  
  .slider-box img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .slider-button {
    background: rgba(0, 0, 0, 0.5);
    color: white;
    border: none;
    padding: 10px 15px;
    cursor: pointer;
    font-size: 24px;
    transition: background 0.3s ease;
  }
  
  .slider-button:hover {
    background: rgba(0, 0, 0, 0.8);
  }
  
  /* Responsive adjustments */
  @media (max-width: 1400px) {
    .newsletter-container {
      padding: 0 calc(2% + 80px);
    }
  }
  
  @media (max-width: 768px) {
    .newsletter-container {
      padding: 0 calc(2% + 30px);
    }
  }
  
  @media (max-width: 480px) {
    .newsletter-container {
      padding: 0 calc(2% + 15px);
    }
  }
