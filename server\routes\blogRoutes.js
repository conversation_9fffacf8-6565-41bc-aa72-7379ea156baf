const express = require('express');
const router = express.Router();
const blogController = require('../controllers/blogController');
const blogImageUpload = require('../middleware/blogImageUploadMiddleware');

// GET - Get all blogs
router.get('/', blogController.getAllBlogs);

// GET - Get blog by ID
router.get('/:blog_id', blogController.getBlogById);

// POST - Create a new blog
router.post('/', blogImageUpload.single('blog_image'), blogController.createBlog);

// PUT - Update a blog
router.put('/:blog_id', blogImageUpload.single('blog_image'), blogController.updateBlog);

// DELETE - Delete a blog
router.delete('/:blog_id', blogController.deleteBlog);

// POST - Increment view count for a blog
router.post('/:blog_id/view', blogController.incrementViewCount);

module.exports = router;