import React, { useState, useEffect } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import JobCard from './JobCard';
import '../css/JobBoard.css';
import { ApiService } from '../services/apiService';
// Import Font Awesome
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faMapMarkerAlt, 
  faBriefcase, 
  faBuilding, 
  faChartLine, 
  faDollarSign,
  faBell,
  faChevronDown,
  faFilter,
  faTimes
} from '@fortawesome/free-solid-svg-icons';
import { FaSpinner } from 'react-icons/fa';
import { useLocation } from '../contexts/LocationContext';
import { useJobFilter } from '../contexts/JobFilterContext';
import PageHelmet from './PageHelmet';

const JobBoard = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const navigate = useNavigate();
  const { selectedLocation, setSelectedLocation, districts } = useLocation();
  const [jobsArray, setJobsArray] = useState([]);
  const [filteredJobs, setFilteredJobs] = useState([]);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [workLocationTypes, setWorkLocationTypes] = useState({
    onsite: false,
    remote: false,
    hybrid: false
  });
  
  // Use shared job filter context
  const { 
    jobType: selectedJobType, 
    updateJobType, 
    minSalary,
    maxSalary,
    salaryFilterApplied,
    updateSalaryRange
  } = useJobFilter();
  
  // Add state for Employment Type and Experience Level filters
  const [selectedEmploymentTypes, setSelectedEmploymentTypes] = useState({
    'Full Time Jobs': false,
    'Part Time Jobs': false,
    'Remote Jobs': false,
    'Freelance': false,
    'Temporary': false
  });
  
  const [selectedExperienceLevels, setSelectedExperienceLevels] = useState({
    "No Experience Required": false,
    "Entry Level": false,
    "Mid Level": false,
    "Senior Level": false,
    "Manager": false,
    "Executive": false
  });
  
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isFilterSidebarOpen, setIsFilterSidebarOpen] = useState(false);

  useEffect(() => {
    const fetchJobs = async () => {      try {        setLoading(true);
        // Add a timestamp to prevent caching, like HomePage (MCP style)
        const response = await ApiService.jobs.getAll({ _t: new Date().getTime() });        
        console.log('RAW API RESPONSE - First 3 jobs:');
        response.data.slice(0, 3).forEach((job, index) => {
          console.log(`Job ${index + 1}:`, {
            job_title: job.job_title,
            experience_level: job.experience_level,
            job_type: job.job_type
          });
        });
        
        // Map backend jobs to the format expected by JobCard, and sort newest first (like HomePage)
        const jobs = response.data
          .map(job => ({
            id: job.job_id,
            hot: job.hot || false,
            image: job.job_post_thumbnail || job.job_post_image || 'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d',
            logo: job.company_logo || job.company_logo_url || 'https://via.placeholder.com/40',
            company: job.company_name,
            position: job.job_title,
            location: job.main_topics,
            workTime: job.job_type,
            // Compose salary string from min/max
            salary: formatSalary(job.min_salary, job.max_salary),
            postedTime: job.start_date ? formatPostedDate(job.start_date) : '',
            views: Math.floor(Math.random() * 1000),
            status: job.status || 'Active',
            description: job.job_description || 'No description provided',
            created_at: job.created_at || job.start_date,
            // Store raw salary values for filtering
            minSalary: job.min_salary ? Number(job.min_salary) : null,
            maxSalary: job.max_salary ? Number(job.max_salary) : null,            // Add experience level for filtering
            experience_level: job.experience_level || ''
          }))
          // Debug: Log first few jobs to see what experience_level values we're getting
        console.log('JobBoard - Sample job data with experience levels:', 
          jobs.slice(0, 3).map(job => ({
            position: job.position,
            experience_level: job.experience_level,
            workTime: job.workTime
          }))
        );
        
        // Debug: Log unique experience levels found in data
        const uniqueExpLevels = [...new Set(jobs.map(job => job.experience_level).filter(Boolean))];
        console.log('JobBoard - Unique experience levels in data:', uniqueExpLevels);
        
        const filteredActiveJobs = jobs
          // Only show jobs with status 'Active'
          .filter(job => job.status === 'Active')
          .sort((a, b) => {
            // Sort by created_at/start_date descending
            if (a.created_at && b.created_at) {
              return new Date(b.created_at) - new Date(a.created_at);
            }
            return 0;
          });
        setJobsArray(filteredActiveJobs);
        setFilteredJobs(filteredActiveJobs);
        setError(null);
      } catch (err) {
        setError('Failed to load jobs from server');
      } finally {
        setLoading(false);
      }
    };
    fetchJobs();
  }, []);

  // Handle URL search parameters
  useEffect(() => {
    console.log('=== URL PARAMETERS DEBUG ===');
    const search = searchParams.get('search');
    const location = searchParams.get('location');
    
    console.log('URL Parameters:', { search, jobType: selectedJobType, location, minSal: minSalary, maxSal: maxSalary });
    
    if (search) {
      console.log('Setting search keyword to:', search);
      setSearchKeyword(search);
    }
    
    if (location) {
      console.log('Setting location to:', location);
      setSelectedLocation(location);
    }
    
    console.log('=== END URL PARAMETERS DEBUG ===');
  }, [searchParams, setSelectedLocation]);

  // For salary range
  const [salaryRange, setSalaryRange] = useState([0, 1000000]);
  const [sliderValues, setSliderValues] = useState([0, 100]); // Percentage values for slider
  
  // Sync salary range with context
  useEffect(() => {
    if (minSalary !== '0' || maxSalary !== '1000000') {
      const newSalaryRange = [parseInt(minSalary), parseInt(maxSalary)];
      setSalaryRange(newSalaryRange);
      
      // Update slider values to match the salary range
      const minValue = 0;
      const maxValue = 1000000;
      const range = maxValue - minValue;
      const newSliderValues = [
        ((newSalaryRange[0] - minValue) / range) * 100,
        ((newSalaryRange[1] - minValue) / range) * 100
      ];
      setSliderValues(newSliderValues);
    }
  }, [minSalary, maxSalary]);
  
  // Filter jobs based on selected job category, location, and salary range
  useEffect(() => {
    let filtered = jobsArray;

    // Filter by search keyword
    if (searchKeyword.trim()) {
      console.log('=== SEARCH KEYWORD FILTER DEBUG ===');
      console.log('Search keyword:', searchKeyword);
      console.log('Jobs before search filtering:', filtered.length);
      
      filtered = filtered.filter(job => {
        const jobTitle = (job.position || '').toLowerCase();
        const jobDescription = (job.description || '').toLowerCase();
        const companyName = (job.company || '').toLowerCase();
        const searchTerm = searchKeyword.toLowerCase();
        
        const titleMatch = jobTitle.includes(searchTerm);
        const descMatch = jobDescription.includes(searchTerm);
        const companyMatch = companyName.includes(searchTerm);
        const matches = titleMatch || descMatch || companyMatch;
        
        if (matches) {
          console.log(`MATCH FOUND - Job: "${job.position}" at ${job.company}`);
          console.log(`  Title match: ${titleMatch}, Desc match: ${descMatch}, Company match: ${companyMatch}`);
        }
        
        return matches;
      });
      
      console.log('Jobs after search filtering:', filtered.length);
      console.log('=== END SEARCH KEYWORD FILTER DEBUG ===');
    }

    // Filter by job type (matching SearchArea dropdown options)
    if (selectedJobType !== '' && selectedJobType !== 'all') {
      console.log('=== JOB TYPE FILTER DEBUG ===');
      console.log('Selected job type:', selectedJobType);
      console.log('Jobs before job type filtering:', filtered.length);
      
      filtered = filtered.filter(job => {
        const jobWorkTime = (job.workTime || '').toLowerCase();
        const selectedType = selectedJobType.toLowerCase();
        
        console.log(`Job "${job.position}": workTime="${job.workTime}", matches=${jobWorkTime === selectedType}`);
        
        // Direct match with job's workTime field
        return jobWorkTime === selectedType;
      });
      
      console.log('Jobs after job type filtering:', filtered.length);
      console.log('=== END JOB TYPE FILTER DEBUG ===');
    }

    // Filter by location
    if (selectedLocation !== '' && selectedLocation !== 'all') {
      filtered = filtered.filter(job => {
        // Check if job location/description contains the selected district
        const jobLocation = job.location || '';
        const jobDescription = job.description || '';
        const searchText = (jobLocation + ' ' + jobDescription).toLowerCase();
        return searchText.includes(selectedLocation.toLowerCase());
      });
    }    // Filter by salary range if applied
    if (salaryFilterApplied) {
      filtered = filtered.filter(job => {
        const [minFilter, maxFilter] = salaryRange;
        
        // If minimum filter is 0, show all jobs including those without salary data
        if (minFilter === 0 && (job.minSalary === null && job.maxSalary === null)) {
          return true;
        }
        
        // If job has no salary data and min filter > 0, exclude it when filtering
        if (job.minSalary === null && job.maxSalary === null && minFilter > 0) {
          return false;
        }
        
        // Check if job salary range overlaps with filter range
        const jobMin = job.minSalary || 0;
        const jobMax = job.maxSalary || job.minSalary || 999999;
        
        // Job overlaps with filter if:
        // - Job's max salary is >= filter's min salary
        // - Job's min salary is <= filter's max salary
        return jobMax >= minFilter && jobMin <= maxFilter;
      });
    }

    // Filter by work location types (onsite, remote, hybrid)
    const selectedWorkTypes = Object.keys(workLocationTypes).filter(key => workLocationTypes[key]);
    if (selectedWorkTypes.length > 0) {
      filtered = filtered.filter(job => {
        const jobWorkType = (job.workTime || '').toLowerCase();
        const jobDescription = (job.description || '').toLowerCase();
        
        return selectedWorkTypes.some(type => {
          switch(type) {
            case 'remote':
              return jobWorkType.includes('remote') || jobDescription.includes('remote') || jobWorkType.includes('work from home');
            case 'onsite':
              return jobWorkType.includes('onsite') || jobWorkType.includes('on-site') || jobWorkType.includes('office') || 
                     (!jobWorkType.includes('remote') && !jobWorkType.includes('hybrid') && !jobDescription.includes('remote'));
            case 'hybrid':
              return jobWorkType.includes('hybrid') || jobDescription.includes('hybrid');
            default:
              return false;
          }
        });
      });
    }    // Filter by Employment Type
    const selectedEmpTypes = Object.keys(selectedEmploymentTypes).filter(key => selectedEmploymentTypes[key]);
    if (selectedEmpTypes.length > 0) {
      filtered = filtered.filter(job => {
        const jobWorkTime = job.workTime || '';
        return selectedEmpTypes.includes(jobWorkTime);
      });
    }    // Filter by Experience Level  
    const selectedExpLevels = Object.keys(selectedExperienceLevels).filter(key => selectedExperienceLevels[key]);
    if (selectedExpLevels.length > 0) {
      console.log('=== EXPERIENCE LEVEL FILTER DEBUG ===');
      console.log('Selected experience levels:', selectedExpLevels);
      console.log('Jobs before experience filtering:', filtered.length);
      
      // Log all unique experience levels in the current filtered jobs
      const allExpLevels = [...new Set(filtered.map(job => job.experience_level).filter(Boolean))];
      console.log('All unique experience levels in current jobs:', allExpLevels);
      
      // Log detailed comparison for first few jobs
      filtered.slice(0, 3).forEach(job => {
        const jobExperienceLevel = job.experience_level || '';
        const matches = selectedExpLevels.includes(jobExperienceLevel);
        console.log(`Job "${job.position}": exp_level="${jobExperienceLevel}", matches=${matches}`);
        console.log(`  - Selected levels: [${selectedExpLevels.join(', ')}]`);
        console.log(`  - Includes check: ${selectedExpLevels.includes(jobExperienceLevel)}`);
      });
      
      filtered = filtered.filter(job => {
        const jobExperienceLevel = job.experience_level || '';
        const matches = selectedExpLevels.includes(jobExperienceLevel);
        return matches;
      });
      
      console.log('Jobs after experience filtering:', filtered.length);
      console.log('=== END EXPERIENCE LEVEL FILTER DEBUG ===');
    }

    setFilteredJobs(filtered);
  }, [selectedJobType, selectedLocation, jobsArray, salaryRange, salaryFilterApplied, workLocationTypes, selectedEmploymentTypes, selectedExperienceLevels, searchKeyword]);

  // Job types that should match SearchArea.jsx options
  const predefinedJobCategories = [
    'Full time',
    'Part time', 
    'Contract',
    'Internship',
    'Remote',
    'Freelance'
  ];

  // Get available job categories (blog categories for filtering)
  const getAvailableJobCategories = () => {
    // Return all predefined blog categories since we're using keyword-based filtering
    return predefinedJobCategories;
  };
  
  const handleJobTypeChange = (e) => {
    updateJobType(e.target.value);
  };
  
  const handleLocationChange = (e) => {
    const newLocation = e.target.value;
    setSelectedLocation(newLocation);
    
    // Update URL parameters to sync with SearchArea
    const newSearchParams = new URLSearchParams(searchParams);
    
    if (newLocation && newLocation !== '') {
      newSearchParams.set('location', newLocation);
    } else {
      newSearchParams.delete('location');
    }
    
    // Update the URL without navigation
    setSearchParams(newSearchParams);
  };
  
  const handleWorkLocationTypeChange = (type) => {
    setWorkLocationTypes(prev => ({
      ...prev,
      [type]: !prev[type]
    }));
  };

  // Helper function to format salary as "Rs." with comma separators, handling min/max
  function formatSalary(min, max) {
    if ((min === null || min === undefined || min === '' || isNaN(Number(min))) &&
        (max === null || max === undefined || max === '' || isNaN(Number(max)))) {
      return 'Negotiable';
    }
    if ((min === 0 || min === '0') && (max === 0 || max === '0')) {
      return 'Negotiable';
    }
    if (min && max) {
      return `Rs. ${Number(min).toLocaleString()} - Rs. ${Number(max).toLocaleString()}`;
    }
    if (min) {
      return `Rs. ${Number(min).toLocaleString()}`;
    }
    if (max) {
      return `Rs. ${Number(max).toLocaleString()}`;
    }
    return 'Negotiable';
  }

  // Helper function (move outside useEffect)
  function formatPostedDate(dateString) {
    if (!dateString) return 'Recently';
    try {
      const postedDate = new Date(dateString);
      const now = new Date();
      const postedDateNormalized = new Date(postedDate.getFullYear(), postedDate.getMonth(), postedDate.getDate()).getTime();
      const todayNormalized = new Date(now.getFullYear(), now.getMonth(), now.getDate()).getTime();
      const oneDayMs = 24 * 60 * 60 * 1000;
      const daysAgo = Math.round((todayNormalized - postedDateNormalized) / oneDayMs);
      if (daysAgo === 0) return 'Today';
      if (daysAgo === 1) return 'Yesterday';
      if (daysAgo < 7) return `${daysAgo} days ago`;
      if (daysAgo < 30) return `${Math.floor(daysAgo / 7)} weeks ago`;
      return `${Math.floor(daysAgo / 30)} months ago`;
    } catch {
      return 'Recently';
    }
  }

  const firstJob = filteredJobs.length > 0 ? filteredJobs[0] : null;
  const remainingJobs = filteredJobs.length > 0 ? filteredJobs.slice(1) : [];

  const handleSalaryInputChange = (e, index) => {
    // Remove commas and handle empty values
    const inputValue = e.target.value.replace(/,/g, '');
    const value = inputValue === '' ? 0 : parseInt(inputValue) || 0;
    
    const newRange = [...salaryRange];
    newRange[index] = value;
    setSalaryRange(newRange);
    
    // Update slider positions based on input values
    const minValue = 0;
    const maxValue = 1000000;
    const range = maxValue - minValue;
    
    const newSliderValues = [...sliderValues];
    newSliderValues[index] = ((value - minValue) / range) * 100;
    setSliderValues(newSliderValues);
  };

  const handleSliderChange = (e, index) => {
    const value = parseInt(e.target.value);
    const newSliderValues = [...sliderValues];
    newSliderValues[index] = value;
    setSliderValues(newSliderValues);
    
    // Update salary input values based on slider positions
    const minValue = 0;
    const maxValue = 1000000;
    const range = maxValue - minValue;
    
    const newSalaryRange = [...salaryRange];
    newSalaryRange[index] = Math.round((value / 100) * range) + minValue;
    setSalaryRange(newSalaryRange);
  };

  const applySalaryFilter = () => {
    // Ensure values are valid numbers or default to 0 and 1000000
    const minValue = isNaN(salaryRange[0]) ? 0 : salaryRange[0];
    const maxValue = isNaN(salaryRange[1]) ? 1000000 : salaryRange[1];
    
    updateSalaryRange(minValue.toString(), maxValue.toString());
  };

  const resetFilters = () => {
    setSalaryRange([0, 1000000]);
    setSliderValues([0, 100]);
    updateJobType('');
    setSelectedEmploymentTypes({
      'Full Time Jobs': false,
      'Part Time Jobs': false,
      'Remote Jobs': false,
      'Freelance': false,
      'Temporary': false
    });
    setSelectedExperienceLevels({
      "No Experience Required": false,
      "Entry Level": false,
      "Mid Level": false,
      "Senior Level": false,
      "Manager": false,
      "Executive": false
    });
    setWorkLocationTypes({
      onsite: false,
      remote: false,
      hybrid: false
    });
    
    // Reset salary range in context
    updateSalaryRange('0', '1000000');
  };

  // Toggle filter sidebar
  const toggleFilterSidebar = () => {
    setIsFilterSidebarOpen(!isFilterSidebarOpen);
  };

  return (
    <div className="job-board-container">
      {/* Add padding at the top to prevent navbar overlap */}
      <div className="job-board-top-spacing"></div>
      
      <div className="filters-sidebar">
        <div className="filter-section">
          <h3><FontAwesomeIcon icon={faBriefcase} /> Job Category</h3>
          <div className="job-type-dropdown">
            <div className="select-wrapper">
              <select 
                value={selectedJobType} 
                onChange={handleJobTypeChange}
                className="job-type-select"
              >
                <option value="">All Categories</option>
                {getAvailableJobCategories().map(category => (
                  <option key={category} value={category}>{category}</option>
                ))}
              </select>
              <FontAwesomeIcon icon={faChevronDown} className="dropdown-icon" />
            </div>
          </div>
        </div>
        
        <div className="filter-section">
          <h3><FontAwesomeIcon icon={faBuilding} /> Employment Type</h3>
          <div className="filter-option">
            <input 
              type="checkbox" 
              id="emp-fulltime" 
              checked={selectedEmploymentTypes['Full Time Jobs']}
              onChange={() => setSelectedEmploymentTypes(prev => ({ 
                ...prev, 
                'Full Time Jobs': !prev['Full Time Jobs'] 
              }))}
            />
            <label htmlFor="emp-fulltime">Full Time</label>
          </div>
          <div className="filter-option">
            <input 
              type="checkbox" 
              id="emp-parttime" 
              checked={selectedEmploymentTypes['Part Time Jobs']}
              onChange={() => setSelectedEmploymentTypes(prev => ({ 
                ...prev, 
                'Part Time Jobs': !prev['Part Time Jobs'] 
              }))}
            />
            <label htmlFor="emp-parttime">Part Time</label>
          </div>
          <div className="filter-option">
            <input 
              type="checkbox" 
              id="emp-remote" 
              checked={selectedEmploymentTypes['Remote Jobs']}
              onChange={() => setSelectedEmploymentTypes(prev => ({ 
                ...prev, 
                'Remote Jobs': !prev['Remote Jobs'] 
              }))}
            />
            <label htmlFor="emp-remote">Remote</label>
          </div>
          <div className="filter-option">
            <input 
              type="checkbox" 
              id="emp-freelance" 
              checked={selectedEmploymentTypes['Freelance']}
              onChange={() => setSelectedEmploymentTypes(prev => ({ 
                ...prev, 
                'Freelance': !prev['Freelance'] 
              }))}
            />
            <label htmlFor="emp-freelance">Freelance</label>
          </div>
          <div className="filter-option">
            <input 
              type="checkbox" 
              id="emp-temporary" 
              checked={selectedEmploymentTypes['Temporary']}
              onChange={() => setSelectedEmploymentTypes(prev => ({ 
                ...prev, 
                'Temporary': !prev['Temporary'] 
              }))}
            />
            <label htmlFor="emp-temporary">Temporary</label>
          </div>
        </div>          <div className="filter-section">
          <h3><FontAwesomeIcon icon={faMapMarkerAlt} /> Location</h3>
          <div className="dropdown-wrapper">
            <select 
              value={selectedLocation} 
              onChange={handleLocationChange}
              className="location-dropdown"
            >
              <option value="">All Locations</option>
              {districts.map(district => (
                <option key={district} value={district}>{district}</option>
              ))}
            </select>
            <div className="dropdown-icon-wrapper">
              <FontAwesomeIcon icon={faChevronDown} className="dropdown-icon" />
            </div>
          </div>
          
          <div className="work-location-types">
            <div className="filter-option">
              <input 
                type="checkbox" 
                id="work-onsite" 
                checked={workLocationTypes.onsite}
                onChange={() => handleWorkLocationTypeChange('onsite')}
              />
              <label htmlFor="work-onsite">On-site</label>
            </div>
            <div className="filter-option">
              <input 
                type="checkbox" 
                id="work-remote" 
                checked={workLocationTypes.remote}
                onChange={() => handleWorkLocationTypeChange('remote')}
              />
              <label htmlFor="work-remote">Remote</label>
            </div>
            <div className="filter-option">
              <input 
                type="checkbox" 
                id="work-hybrid" 
                checked={workLocationTypes.hybrid}
                onChange={() => handleWorkLocationTypeChange('hybrid')}
              />
              <label htmlFor="work-hybrid">Hybrid</label>
            </div>
          </div>
        </div>
        
        <div className="filter-section">
          <h3><FontAwesomeIcon icon={faChartLine} /> Experience Level</h3>
          <div className="filter-option">
            <input 
              type="checkbox" 
              id="exp-no-experience" 
              checked={selectedExperienceLevels['No Experience Required']}
              onChange={() => setSelectedExperienceLevels(prev => ({ 
                ...prev, 
                'No Experience Required': !prev['No Experience Required'] 
              }))}
            />
            <label htmlFor="exp-no-experience">No Experience Required</label>
          </div>
          <div className="filter-option">
            <input 
              type="checkbox" 
              id="exp-entry" 
              checked={selectedExperienceLevels['Entry Level']}
              onChange={() => setSelectedExperienceLevels(prev => ({ 
                ...prev, 
                'Entry Level': !prev['Entry Level'] 
              }))}
            />
            <label htmlFor="exp-entry">Entry Level</label>
          </div>
          <div className="filter-option">
            <input 
              type="checkbox" 
              id="exp-mid" 
              checked={selectedExperienceLevels['Mid Level']}
              onChange={() => setSelectedExperienceLevels(prev => ({ 
                ...prev, 
                'Mid Level': !prev['Mid Level'] 
              }))}
            />
            <label htmlFor="exp-mid">Mid Level</label>
          </div>
          <div className="filter-option">
            <input 
              type="checkbox" 
              id="exp-senior" 
              checked={selectedExperienceLevels['Senior Level']}
              onChange={() => setSelectedExperienceLevels(prev => ({ 
                ...prev, 
                'Senior Level': !prev['Senior Level'] 
              }))}
            />
            <label htmlFor="exp-senior">Senior Level</label>
          </div>
          <div className="filter-option">
            <input 
              type="checkbox" 
              id="exp-manager" 
              checked={selectedExperienceLevels['Manager']}
              onChange={() => setSelectedExperienceLevels(prev => ({ 
                ...prev, 
                'Manager': !prev['Manager'] 
              }))}
            />
            <label htmlFor="exp-manager">Manager</label>
          </div>
          <div className="filter-option">
            <input 
              type="checkbox" 
              id="exp-executive" 
              checked={selectedExperienceLevels['Executive']}
              onChange={() => setSelectedExperienceLevels(prev => ({ 
                ...prev, 
                'Executive': !prev['Executive'] 
              }))}
            />
            <label htmlFor="exp-executive">Executive</label>
          </div>
        </div>
        
        <div className="filter-section salary-range-section">
          <h3><FontAwesomeIcon icon={faDollarSign} /> Salary Range</h3>
          
          <div className="salary-slider-container">
            <div className="salary-slider-track">
              <div 
                className="salary-slider-progress" 
                style={{
                  left: `${sliderValues[0]}%`,
                  width: `${sliderValues[1] - sliderValues[0]}%`
                }}
              ></div>
            </div>
            
            <input 
              type="range" 
              min="0" 
              max="100" 
              value={sliderValues[0]} 
              onChange={(e) => handleSliderChange(e, 0)}
              className="salary-range-input min-value"
            />
            
            <input 
              type="range" 
              min="0" 
              max="100" 
              value={sliderValues[1]} 
              onChange={(e) => handleSliderChange(e, 1)}
              className="salary-range-input max-value"
            />
          </div>
          
          <div className="salary-labels">
            <div>From</div>
            <div>To</div>
          </div>
          
          <div className="salary-input-fields">
            <input 
              type="text" 
              value={(isNaN(salaryRange[0]) ? 0 : salaryRange[0]).toLocaleString()}
              onChange={(e) => handleSalaryInputChange(e, 0)}
              className="salary-text-input"
            />
            <div className="salary-separator">-</div>
            <input 
              type="text" 
              value={(isNaN(salaryRange[1]) ? 1000000 : salaryRange[1]).toLocaleString()}
              onChange={(e) => handleSalaryInputChange(e, 1)}
              className="salary-text-input"
            />
          </div>
          
          <div className="salary-filter-buttons">
            <button className="apply-filter-button" onClick={applySalaryFilter}>
              Apply Filter
            </button>
            <button className="reset-filter-button" onClick={resetFilters}>
              Reset filter
            </button>
          </div>
        </div>
        
        {/* <div className="job-alert-section">
          <h3><FontAwesomeIcon icon={faBell} /> Job Reminder</h3>
          <p>Set job reminder to get notifications about jobs</p>
          <button className="set-alert-button">Set Job Reminder</button>
        </div> */}
      </div>
      
      <div className="job-listings">
              <PageHelmet 
                title="Browse" 
                description="Expert advice, recruitment trends, and professional development resources for job seekers."
              />
        <div className="job-search-header">
          <div className="job-search-left">
            <button 
              className="filter-toggle-btn-jobboard" 
              onClick={toggleFilterSidebar}
              aria-label="Toggle filters"
            >
              <FontAwesomeIcon icon={faFilter} />
              Filters
            </button>
            <div>Showing {filteredJobs.length} out of {jobsArray.length} Jobs</div>
          </div>
          {/* Removed sort-options dropdown */}
        </div>          <div className="job-grid">
          {loading ? (
            <div className="loading-overlay">
              <FaSpinner className="spinner" />
              <p>Loading jobs...</p>
            </div>
          ) : error ? (
            <div className="error-message">{error}</div>
          ) : filteredJobs.length === 0 ? (
            <div className="no-jobs-message">No jobs found matching your filters.</div>
          ) : (
            <>
              {/* First job card as individual component */}
              {firstJob && (
                <JobCard 
                  key={firstJob.id || 0}
                  id={firstJob.id}
                  hot={firstJob.hot}
                  image={firstJob.image}
                  logo={firstJob.logo}
                  company={firstJob.company}
                  position={firstJob.position}
                  location={firstJob.location}
                  workTime={firstJob.workTime}
                  salary={firstJob.salary}
                  postedTime={firstJob.postedTime}
                  views={firstJob.views}
                />
              )}
              
              {/* Remaining job cards */}
              {remainingJobs.map((job, index) => (
                <JobCard 
                  key={job.id || index}
                  id={job.id}
                  hot={job.hot}
                  image={job.image}
                  logo={job.logo}
                  company={job.company}
                  position={job.position}
                  location={job.location}
                  workTime={job.workTime}
                  salary={job.salary}
                  postedTime={job.postedTime}
                  views={job.views}
                />
              ))}
            </>
          )}
        </div>
      </div>

      {/* Filter Sidebar Overlay */}
      {isFilterSidebarOpen && (
        <div className="filter-sidebar-overlay" onClick={toggleFilterSidebar}></div>
      )}

      {/* Sliding Filter Sidebar */}
      <div className={`filter-sidebar-mobile ${isFilterSidebarOpen ? 'open' : ''}`}>
        <div className="filter-sidebar-header">
          <h3><FontAwesomeIcon icon={faFilter} /> Filters</h3>
          <button className="close-sidebar-btn" onClick={toggleFilterSidebar}>
            <FontAwesomeIcon icon={faTimes} />
          </button>
        </div>
        
        <div className="filter-sidebar-content">
          <div className="filter-section">
            <h3><FontAwesomeIcon icon={faBriefcase} /> Job Category</h3>
            <div className="job-type-dropdown">
              <div className="select-wrapper">
                <select 
                  value={selectedJobType} 
                  onChange={handleJobTypeChange}
                  className="job-type-select"
                >
                  <option value="">All Categories</option>
                  {getAvailableJobCategories().map(category => (
                    <option key={category} value={category}>{category}</option>
                  ))}
                </select>
                <FontAwesomeIcon icon={faChevronDown} className="dropdown-icon" />
              </div>
            </div>
          </div>
          
          <div className="filter-section">
            <h3><FontAwesomeIcon icon={faBuilding} /> Employment Type</h3>
            <div className="filter-option">
              <input type="checkbox" id="mobile-emp-fulltime" />
              <label htmlFor="mobile-emp-fulltime">Full Time</label>
            </div>
            <div className="filter-option">
              <input type="checkbox" id="mobile-emp-parttime" />
              <label htmlFor="mobile-emp-parttime">Part Time</label>
            </div>
            <div className="filter-option">
              <input type="checkbox" id="mobile-emp-remote" />
              <label htmlFor="mobile-emp-remote">Remote</label>
            </div>
            <div className="filter-option">
              <input type="checkbox" id="mobile-emp-freelance" />
              <label htmlFor="mobile-emp-freelance">Freelance</label>
            </div>
            <div className="filter-option">
              <input type="checkbox" id="mobile-emp-temporary" />
              <label htmlFor="mobile-emp-temporary">Temporary</label>
            </div>
          </div>
          
          <div className="filter-section">
            <h3><FontAwesomeIcon icon={faMapMarkerAlt} /> Location</h3>
            <div className="filter-option">
              <input type="checkbox" id="mobile-loc-remote" />
              <label htmlFor="mobile-loc-remote">Remote</label>
            </div>
            <div className="filter-option">
              <input type="checkbox" id="mobile-loc-onsite" />
              <label htmlFor="mobile-loc-onsite">On-site</label>
            </div>
            <div className="filter-option">
              <input type="checkbox" id="mobile-loc-hybrid" />
              <label htmlFor="mobile-loc-hybrid">Hybrid</label>
            </div>
          </div>
          
          <div className="filter-section">
            <h3><FontAwesomeIcon icon={faChartLine} /> Experience Level</h3>
            <div className="filter-option">
              <input type="checkbox" id="mobile-exp-entry" />
              <label htmlFor="mobile-exp-entry">Entry Level</label>
            </div>
            <div className="filter-option">
              <input type="checkbox" id="mobile-exp-mid" />
              <label htmlFor="mobile-exp-mid">Mid Level</label>
            </div>
            <div className="filter-option">
              <input type="checkbox" id="mobile-exp-senior" />
              <label htmlFor="mobile-exp-senior">Senior Level</label>
            </div>
            <div className="filter-option">
              <input type="checkbox" id="mobile-exp-manager" />
              <label htmlFor="mobile-exp-manager">Manager</label>
            </div>
            <div className="filter-option">
              <input type="checkbox" id="mobile-exp-executive" />
              <label htmlFor="mobile-exp-executive">Executive</label>
            </div>
          </div>
          
          <div className="filter-section salary-range-section">
            <h3><FontAwesomeIcon icon={faDollarSign} /> Salary Range</h3>
            
            <div className="salary-slider-container">
              <div className="salary-slider-track">
                <div 
                  className="salary-slider-progress" 
                  style={{
                    left: `${sliderValues[0]}%`,
                    width: `${sliderValues[1] - sliderValues[0]}%`
                  }}
                ></div>
              </div>
              
              <input 
                type="range" 
                min="0" 
                max="100" 
                value={sliderValues[0]} 
                onChange={(e) => handleSliderChange(e, 0)}
                className="salary-range-input min-value"
              />
              
              <input 
                type="range" 
                min="0" 
                max="100" 
                value={sliderValues[1]} 
                onChange={(e) => handleSliderChange(e, 1)}
                className="salary-range-input max-value"
              />
            </div>
            
            <div className="salary-labels">
              <div>From</div>
              <div>To</div>
            </div>
            
            <div className="salary-input-fields">
              <input 
                type="text" 
                value={(isNaN(salaryRange[0]) ? 0 : salaryRange[0]).toLocaleString()}
                onChange={(e) => handleSalaryInputChange(e, 0)}
                className="salary-text-input"
              />
              <div className="salary-separator">-</div>
              <input 
                type="text" 
                value={(isNaN(salaryRange[1]) ? 1000000 : salaryRange[1]).toLocaleString()}
                onChange={(e) => handleSalaryInputChange(e, 1)}
                className="salary-text-input"
              />
            </div>
            
            <div className="salary-filter-buttons">
              <button className="apply-filter-button" onClick={applySalaryFilter}>
                Apply Filter
              </button>
              <button className="reset-filter-button" onClick={resetFilters}>
                Reset filter
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default JobBoard;
