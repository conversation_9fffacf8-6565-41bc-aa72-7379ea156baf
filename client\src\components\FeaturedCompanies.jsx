import React, { useRef, useEffect, useState } from 'react';
import '../css/FeaturedCompanies.css';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faChevronLeft, faChevronRight } from '@fortawesome/free-solid-svg-icons';
import ApiService from '../services/apiService';

const FeaturedCompanies = () => {
  const sliderRef = useRef(null);
  const [companies, setCompanies] = useState([]);
  const [loading, setLoading] = useState(true);

  // Fetch companies from API
  useEffect(() => {
    const fetchCompanies = async () => {
      try {
        setLoading(true);
        const response = await ApiService.companies.getAll();
        
        // Transform API data to match the expected format
        const formattedCompanies = (response.data.data || []).map((company) => ({
          id: company.company_id,
          name: company.company_name,
          logo: company.company_logo_url || `https://via.placeholder.com/200x200?text=${company.company_name.charAt(0)}`
        }));
        
        setCompanies(formattedCompanies);
      } catch (err) {
        console.error('Error fetching companies:', err);
        // Fallback to empty array if API fails
        setCompanies([]);
      } finally {
        setLoading(false);
      }
    };

    fetchCompanies();
  }, []);

  const scrollLeft = () => {
    if (sliderRef.current) {
      sliderRef.current.scrollBy({ left: -400, behavior: "smooth" }); // Scroll more to show more items
    }
  };

  const scrollRight = () => {
    if (sliderRef.current) {
      sliderRef.current.scrollBy({ left: 400, behavior: "smooth" }); // Scroll more to show more items
    }
  };

  // Auto-scroll to show there are more logos
  useEffect(() => {
    if (companies.length === 0) return; // Don't start auto-scroll if no companies
    
    const interval = setInterval(() => {
      if (sliderRef.current) {
        const isAtEnd = sliderRef.current.scrollLeft + sliderRef.current.clientWidth >= sliderRef.current.scrollWidth - 20;
        
        if (isAtEnd) {
          sliderRef.current.scrollTo({ left: 0, behavior: "smooth" });
        } else {
          sliderRef.current.scrollBy({ left: 200, behavior: "smooth" });
        }
      }
    }, 5000); // Auto-scroll every 5 seconds

    return () => clearInterval(interval);
  }, [companies]);

  return (
    <section className="featured-companies-container">
      <h2>Featured Companies</h2>
      <div className="company-slider-wrapper">
        <button className="slider-arrow left-arrow" onClick={scrollLeft} aria-label="Scroll left">
          <FontAwesomeIcon icon={faChevronLeft} />
        </button>
        <div className="company-list" ref={sliderRef}>
          {loading ? (
            <div className="loading-message">Loading companies...</div>
          ) : companies.length === 0 ? (
            <div className="no-companies-message">No companies available</div>
          ) : (
            companies.map(company => (
              <div key={`company-${company.id}`} className="company-name-badge">
                <img 
                  src={company.logo} 
                  alt={`${company.name} logo`}
                  className="company-logo"
                  onError={(e) => {
                    e.target.onerror = null;
                    e.target.src = `https://via.placeholder.com/200x200?text=${company.name.charAt(0)}`;
                  }}
                />
              </div>
            ))
          )}
        </div>
        <button className="slider-arrow right-arrow" onClick={scrollRight} aria-label="Scroll right">
          <FontAwesomeIcon icon={faChevronRight} />
        </button>
      </div>
    </section>
  );
};

export default FeaturedCompanies;
