// GET - Get hot jobs

const express = require('express');
const router = express.Router();
const jobController = require('../controllers/jobController');
const upload = require('../middleware/uploadMiddleware');

// GET - Get hot jobs
router.get('/hot', jobController.getHotJobs);

// GET - Get dashboard analytics
router.get('/dashboard', jobController.getDashboardAnalytics);

// GET - Get all jobs
router.get('/', jobController.getAllJobs);

// GET - Get job by ID
router.get('/:job_id', jobController.getJobById);

// POST - Create a new job
router.post('/', upload.fields([
  { name: 'job_post_image', maxCount: 1 },
  { name: 'job_post_thumbnail', maxCount: 1 },
  { name: 'company_logo', maxCount: 1 }
]), jobController.createJob);

// POST - Increment view count for a job
router.post('/:job_id/view', jobController.incrementViewCount);

// PUT - Update a job
router.put('/:job_id', upload.fields([
  { name: 'job_post_image', maxCount: 1 },
  { name: 'job_post_thumbnail', maxCount: 1 },
  { name: 'company_logo', maxCount: 1 }
]), jobController.updateJob);

// DELETE - Delete a job
router.delete('/:job_id', jobController.deleteJob);

module.exports = router;