const express = require('express');
const router = express.Router();
const { Job } = require('../models');

// Get hot jobs
router.get('/hot', async (req, res) => {
  try {
    const hotJobs = await Job.findAll({
      where: { hot: 1 },
      order: [['created_at', 'DESC']],
      limit: 10
    });
    res.json(hotJobs);
  } catch (error) {
    console.error('Error fetching hot jobs:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;