.companies-admin-container {
  padding: 1.5rem;
  background-color: #f8f9fa;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.companies-admin-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.companies-admin-header h2 {
  margin: 0;
  font-size: 1.4rem;
  color: #333;
  display: flex;
  align-items: center;
}

.companies-admin-header h2 svg {
  margin-right: 0.5rem;
  color: #4a6cf7;
}

.header-controls {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.search-wrapper {
  position: relative;
}

.search-icon {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: #aaa;
}

.search-input {
  padding: 8px 12px 8px 32px;
  border: 1px solid #ddd;
  border-radius: 4px;
  width: 250px;
  font-size: 0.9rem;
}

.add-company-btn {
  background-color: #4a6cf7;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 15px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.add-company-btn:hover {
  background-color: #3a5bd9;
}

.add-company-btn:disabled {
  background-color: #a4b0e0;
  cursor: not-allowed;
}

/* Form Styles */
.company-form-container {
  background-color: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.company-form-container h3 {
  margin-top: 0;
  margin-bottom: 1.5rem;
  font-size: 1.2rem;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 0.8rem;
}

.company-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.form-group label {
  font-weight: 500;
  color: #555;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.form-icon {
  color: #4a6cf7;
  width: 16px;
}

.form-group input {
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.95rem;
}

.logo-upload-container {
  display: flex;
  gap: 2rem;
  align-items: center;
  border: 1px dashed #ddd;
  padding: 1.5rem;
  border-radius: 8px;
  background-color: #f9f9f9;
}

.logo-preview {
  width: 120px;
  height: 120px;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #eee;
}

.logo-preview img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.no-logo {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  color: #aaa;
}

.no-logo svg {
  font-size: 2rem;
}

.logo-upload {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.logo-upload label {
  font-weight: 500;
  color: #555;
}

.browse-logo-btn {
  background-color: #4a6cf7;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 10px 15px;
  cursor: pointer;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 8px;
  transition: background-color 0.2s;
}

.browse-logo-btn:hover {
  background-color: #3a5bd9;
}

.hidden-file-input {
  display: none;
}

.selected-file {
  font-size: 0.85rem;
  color: #4a6cf7;
  margin: 5px 0 0 0;
  font-style: italic;
}

.file-hint {
  font-size: 0.8rem;
  color: #888;
  margin: 0;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1rem;
}

.cancel-btn {
  background-color: #f0f0f0;
  color: #555;
  border: 1px solid #ddd;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.cancel-btn:hover {
  background-color: #e0e0e0;
}

.save-btn {
  background-color: #4a6cf7;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.save-btn:hover {
  background-color: #3a5bd9;
}

/* Companies List Styles */
.companies-list-container {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.companies-table-wrapper {
  overflow-x: auto;
}

.companies-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.95rem;
}

.companies-table th {
  background-color: #f5f7ff;
  padding: 12px 16px;
  text-align: left;
  color: #444;
  font-weight: 600;
  border-bottom: 1px solid #eee;
}

.companies-table td {
  padding: 12px 16px;
  border-bottom: 1px solid #eee;
  color: #555;
}

.companies-table tr:hover {
  background-color: #f9f9f9;
}

.company-logo-cell {
  width: 60px;
}

.company-logo-thumbnail {
  width: 40px;
  height: 40px;
  border-radius: 4px;
  object-fit: contain;
  background-color: #f5f5f5;
}

.no-logo-thumbnail {
  width: 40px;
  height: 40px;
  border-radius: 4px;
  background-color: #eee;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #aaa;
}

.actions-cell {
  display: flex;
  width: 50px;
  white-space: nowrap;
}


.edit-btn,
.delete-btn {
  background: none;
  border: none;
  width: 32px;
  height: 32px;
  border-radius: 4px;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: #555;
  margin-right: 5px;
}

.edit-btn {
  color: #4a6cf7;
}

.edit-btn:hover {
  background-color: #eef1fe;
}

.delete-btn {
  color: #f86464;
}

.delete-btn:hover {
  background-color: #fee8e8;
}

.loading-message,
.error-message,
.no-companies-message {
  padding: 2rem;
  text-align: center;
  color: #666;
}

.error-message {
  color: #f86464;
}

/* Notification Styles */
.admin-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 12px 20px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  animation: slideIn 0.3s ease-out;
}

.admin-notification.success {
  background-color: #e8f5e9;
  color: #2e7d32;
  border-left: 4px solid #2e7d32;
}

.admin-notification.error {
  background-color: #ffebee;
  color: #c62828;
  border-left: 4px solid #c62828;
}

.admin-notification button {
  background: none;
  border: none;
  cursor: pointer;
  color: inherit;
  opacity: 0.7;
  padding: 0;
}

.admin-notification button:hover {
  opacity: 1;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Modal styles for popup */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.modal-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
  background-color: #f8f9fa;
  border-radius: 12px 12px 0 0;
}

.modal-header h3 {
  margin: 0;
  color: #1f2937;
  font-size: 20px;
  font-weight: 600;
}

.modal-close-btn {
  background: none;
  border: none;
  font-size: 18px;
  color: #6b7280;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.2s;
}

.modal-close-btn:hover {
  background-color: #f3f4f6;
  color: #374151;
}

.modal-content {
  padding: 24px;
}

.company-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-weight: 500;
  color: #374151;
  display: flex;
  align-items: center;
  gap: 8px;
}

.form-icon {
  color: #6b7280;
}

.form-group input,
.textarea-field {
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s;
}

.form-group input:focus,
.textarea-field:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.textarea-field {
  resize: vertical;
  min-height: 100px;
  font-family: inherit;
}

.logo-upload-container {
  display: flex;
  gap: 20px;
  align-items: flex-start;
  padding: 16px;
  background-color: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.logo-preview {
  width: 80px;
  height: 80px;
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: white;
}

.logo-preview img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 6px;
}

.no-logo {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #9ca3af;
  font-size: 12px;
}

.no-logo svg {
  font-size: 24px;
  margin-bottom: 4px;
}

.logo-upload {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.logo-upload label {
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

.browse-logo-btn {
  padding: 10px 16px;
  background-color: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  cursor: pointer;
  color: #374151;
  font-size: 14px;
  transition: all 0.2s;
  align-self: flex-start;
}

.browse-logo-btn:hover {
  background-color: #e5e7eb;
}

.hidden-file-input {
  display: none;
}

.file-hint {
  font-size: 12px;
  color: #6b7280;
  margin: 0;
}

.selected-file {
  font-size: 12px;
  color: #059669;
  margin: 0;
}

.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
}

.cancel-btn,
.save-btn {
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 6px;
}

.cancel-btn {
  background-color: #f3f4f6;
  border: 1px solid #d1d5db;
  color: #374151;
}

.cancel-btn:hover {
  background-color: #e5e7eb;
}

.save-btn {
  background-color: #3b82f6;
  border: 1px solid #3b82f6;
  color: white;
}

.save-btn:hover {
  background-color: #2563eb;
}

/* Mobile responsiveness for modal */
@media (max-width: 768px) {
  .modal-overlay {
    padding: 10px;
  }
  
  .modal-container {
    max-width: 100%;
    max-height: 95vh;
  }
  
  .modal-header {
    padding: 16px 20px;
  }
  
  .modal-content {
    padding: 20px;
  }
  
  .logo-upload-container {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }
  
  .logo-preview {
    width: 100px;
    height: 100px;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .cancel-btn,
  .save-btn {
    width: 100%;
    justify-content: center;
  }
}

/* Responsive Styles */
@media (max-width: 768px) {
  .companies-admin-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .header-controls {
    width: 100%;
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-input {
    width: 100%;
  }
  
  .logo-upload-container {
    flex-direction: column;
  }
}

/* Add styles for the refresh button */
.refresh-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background-color: #f8f9fa;
  color: #495057;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-right: 10px;
}

.refresh-button:hover {
  background-color: #e9ecef;
}

.refresh-button:active {
  background-color: #dae0e5;
}

.refresh-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Add a spin animation for the refresh icon when loading */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.refresh-button svg {
  transition: transform 0.2s ease;
}

.refresh-button:active svg {
  animation: spin 1s linear infinite;
}