/* CV Form Styles - Clean Black & White Design */

/* Modal Styles */
.cv-modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
  animation: modalFadeIn 0.3s ease-out;
}

.cv-modal-container {
  position: relative;
  max-width: 520px;
  max-height: 85vh;
  overflow-y: auto;
  border-radius: 8px;
  background: white;
  animation: modalSlideIn 0.3s ease-out;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
}

.cv-form-container {
  background: white;
  border-radius: 8px;
  max-width: 100%;
  margin: 0;
  padding: 32px 24px;
  position: relative;
  border: 1px solid #e5e5e5;
}

.cv-form-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 24px;
  position: relative;
  padding-bottom: 16px;
  border-bottom: 1px solid #e5e5e5;
}

.cv-form-title {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 1.5rem;
  font-weight: 600;
  color: #000;
  text-align: left;
  letter-spacing: -0.025em;
  flex: 1;
  margin: 0;
}

.cv-modal-close {
  background: #dc2626;
  color: white;
  border: none;
  border-radius: 6px;
  width: 32px;
  height: 32px;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  flex-shrink: 0;
  margin-left: 16px;
}

.cv-modal-close:hover {
  background: #b91c1c;
  transform: scale(1.05);
}.cv-form-section {
  margin-bottom: 24px;
  padding: 0;
  background: transparent;
  border: none;
  border-radius: 0;
}

.cv-form-section-title {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 0.875rem;
  font-weight: 600;
  color: #000;
  margin-bottom: 16px;
  padding: 0;
  border: none;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.cv-form-group {
  margin-bottom: 16px;
  display: flex;
  flex-direction: column;
}

.cv-form-label {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 6px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.cv-form-label .required {
  color: #dc2626;
  font-size: 0.875rem;
}

.cv-form-input,
.cv-form-select {
  padding: 12px 16px;
  border-radius: 6px;
  border: 1px solid #d1d5db;
  background: white;
  font-size: 0.875rem;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  color: #111827;
  outline: none;
  transition: all 0.2s ease;
}

.cv-form-input:focus,
.cv-form-select:focus {
  border-color: #000;
  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.05);
}

.cv-form-input::placeholder {
  color: #9ca3af;
  font-weight: 400;
}

.cv-form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.cv-form-row .cv-form-group {
  margin-bottom: 0;
}

.cv-form-radio-group {
  display: flex;
  gap: 12px;
  align-items: center;
  margin-top: 6px;
  flex-wrap: wrap;
}

.cv-form-radio-option {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border-radius: 6px;
  border: 1px solid #d1d5db;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 500;
  color: #374151;
  min-width: 80px;
  justify-content: center;
  font-size: 0.875rem;
}

.cv-form-radio-option:hover {
  border-color: #6b7280;
  background: #f9fafb;
}

.cv-form-radio-option.selected {
  border-color: #000;
  background: #f9fafb;
  color: #000;
  font-weight: 600;
}

.cv-form-radio {
  display: none;
}

.cv-form-slider-section {
  margin-top: 12px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

.cv-form-slider-group {
  display: flex;
  align-items: center;
  gap: 12px;
}

.cv-form-slider-label {
  font-size: 0.875rem;
  color: #374151;
  font-weight: 500;
  min-width: 16px;
  text-align: center;
}

.cv-form-slider {
  flex: 1;
  height: 4px;
  border-radius: 2px;
  background: #e5e7eb;
  outline: none;
  -webkit-appearance: none;
  appearance: none;
}

.cv-form-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #000;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cv-form-slider::-webkit-slider-thumb:hover {
  transform: scale(1.1);
}

.cv-form-slider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #000;
  cursor: pointer;
  border: none;
}
.cv-form-slider-value {
  font-size: 0.875rem;
  color: #000;
  font-weight: 600;
  min-width: 24px;
  text-align: center;
  background: #f3f4f6;
  border-radius: 4px;
  padding: 4px 8px;
}

.cv-form-upload-section {
  background: #f9fafb;
  border-radius: 8px;
  border: 2px dashed #d1d5db;
  padding: 24px 16px;
  margin: 20px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  transition: all 0.2s ease;
}

.cv-form-upload-section:hover {
  border-color: #6b7280;
  background: #f3f4f6;
}

.cv-form-upload-icon {
  font-size: 2rem;
  margin-bottom: 12px;
  color: #6b7280;
}

.cv-form-upload-label {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 600;
  font-size: 1rem;
  color: #111827;
  margin-bottom: 4px;
}

.cv-form-upload-desc {
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 16px;
}

.cv-form-upload-btn {
  background: #9777FA;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 10px 20px;
  font-size: 0.875rem;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cv-form-upload-btn:hover {
  background: #8560F8;
}

.cv-form-file-name {
  margin-top: 12px;
  padding: 8px 12px;
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  color: #374151;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.875rem;
}

.cv-form-submit-btn {
  background: #9777FA;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 14px 0;
  width: 100%;
  font-size: 0.875rem;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 600;
  margin-top: 24px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.cv-form-submit-btn:hover:not(:disabled) {
  background: #8560F8;
}

.cv-form-submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.cv-form-loading {
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.cv-form-loading::after {
  content: '';
  width: 12px;
  height: 12px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes modalFadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Company Dropdown Styles */
.cv-company-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e5e5e5;
  border-top: none;
  border-radius: 0 0 4px 4px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  max-height: 200px;
  overflow-y: auto;
}

.cv-company-option {
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s ease;
}

.cv-company-option:hover {
  background-color: #f8f9fa;
}

.cv-company-option:last-child {
  border-bottom: none;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .cv-modal-backdrop {
    padding: 16px;
  }
  
  .cv-modal-container {
    max-width: 95vw;
    max-height: 90vh;
  }

  .cv-form-container {
    padding: 24px 16px;
  }
  
  .cv-form-title {
    font-size: 1.25rem;
  }
  
  .cv-form-row {
    grid-template-columns: 1fr;
    gap: 0;
  }
  
  .cv-form-row .cv-form-group {
    margin-bottom: 16px;
  }
  
  .cv-form-radio-group {
    gap: 8px;
  }
  
  .cv-form-radio-option {
    min-width: 70px;
    padding: 8px 12px;
    font-size: 0.875rem;
  }
}

@media (max-width: 480px) {
  .cv-modal-backdrop {
    padding: 12px;
  }
  
  .cv-modal-container {
    max-width: 98vw;
  }

  .cv-form-container {
    padding: 20px 12px;
  }
  
  .cv-form-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .cv-modal-close {
    align-self: flex-end;
    margin-left: 0;
  }
  
  .cv-form-radio-group {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  
  .cv-form-radio-option {
    min-width: auto;
    justify-content: flex-start;
  }
}

/* Success and Error Messages */
.cv-form-success-message {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
  border-radius: 4px;
  padding: 12px 15px;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.cv-form-error-message {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
  border-radius: 4px;
  padding: 12px 15px;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.cv-form-warning-message {
  background-color: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
  border-radius: 4px;
  padding: 12px 15px;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}
