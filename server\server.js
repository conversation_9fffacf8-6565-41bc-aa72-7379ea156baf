const express = require("express");
const cors = require("cors");
const bodyParser = require("body-parser");
require("dotenv").config();

// Import middleware
const errorHandler = require('./middleware/errorHandler');
const notFoundHandler = require('./middleware/notFoundHandler');

// Import routes
const jobRoutes = require('./routes/jobRoutes');
const blogRoutes = require('./routes/blogRoutes');
const companyRoutes = require('./routes/companyRoutes');
const cvRoutes = require('./routes/cvRoutes');
const adminUserRoutes = require('./routes/adminUserRoutes');

// Initialize Express app
const app = express();

// Apply middleware
app.use(cors());

// Body parser middleware - only for non-multipart requests
// For multipart/form-data, let multer handle it
app.use((req, res, next) => {
  const contentType = req.headers['content-type'] || '';
  if (contentType.includes('multipart/form-data')) {
    // Skip bodyParser for multipart requests (multer will handle these)
    return next();
  }
  // Apply bodyParser for JSON and URL-encoded requests
  bodyParser.json({ limit: '10mb' })(req, res, next);
});
app.use((req, res, next) => {
  const contentType = req.headers['content-type'] || '';
  if (contentType.includes('multipart/form-data')) {
    // Skip bodyParser for multipart requests
    return next();
  }
  // Apply bodyParser for URL-encoded requests
  bodyParser.urlencoded({ extended: true, limit: '10mb' })(req, res, next);
});

// API Routes
app.use('/api/jobs', jobRoutes);
app.use('/api/blogs', blogRoutes);
app.use('/api/companies', companyRoutes);
app.use('/api/cvs', cvRoutes);
app.use('/api/admin-users', adminUserRoutes);

// Handle 404 routes
app.use(notFoundHandler);

// Error handling middleware
app.use(errorHandler);

// Set port and start server
const PORT = process.env.PORT || 5000;
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  console.log(`API available at http://localhost:${PORT}/api`);
});


