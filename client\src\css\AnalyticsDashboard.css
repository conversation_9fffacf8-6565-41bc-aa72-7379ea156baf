.analytics-dashboard {
  padding: 1.5rem;
  background-color: #f8fafc;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.modern-dashboard {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.dashboard-header h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.refresh-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: #f1f5f9;
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s ease;
}

.refresh-button:hover {
  background-color: #e2e8f0;
  color: #334155;
}

/* Stats grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 24px;
}

.stat-card {
  background-color: #ffffff;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 1.25rem;
  display: flex;
  flex-direction: column;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.stat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
}

.stat-value {
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 4px;
}

.stat-title {
  font-size: 14px;
  color: #64748b;
}

.stat-change {
  font-size: 12px;
  font-weight: 500;
  padding: 2px 8px;
  border-radius: 20px;
}

.stat-change.positive {
  color: #10b981;
  background-color: #10b98115;
}

.stat-change.negative {
  color: #ef4444;
  background-color: #ef444415;
}

/* Dashboard cards */
.dashboard-card {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.card-header {
  padding: 1rem 1.25rem;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.card-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.info-tooltip {
  color: #64748b;
  cursor: help;
  transition: color 0.2s ease;
}

.info-tooltip:hover {
  color: #334155;
}

.card-body {
  padding: 24px;
}

/* Chart styles */
.chart-container {
  width: 100%;
  min-height: 300px;
}

.chart-bar-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: 16px;
}

.chart-bar {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.chart-bar-label {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
}

.chart-bar-value {
  height: 12px;
  border-radius: 6px;
  background-color: #8057ff;
}

/* Table styles */
.analytics-table {
  width: 100%;
  border-collapse: collapse;
}

.analytics-table th {
  padding: 12px 16px;
  text-align: left;
  font-size: 14px;
  font-weight: 600;
  color: #64748b;
  border-bottom: 1px solid #e2e8f0;
}

.analytics-table td {
  padding: 12px 16px;
  font-size: 14px;
  color: #334155;
  border-bottom: 1px solid #f1f5f9;
}

.job-title {
  font-weight: 500;
  color: #1e293b;
  margin-bottom: 0.25rem;
  font-size: 14px;
}

.date-display {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #64748b;
}

.table-responsive {
  overflow-x: auto;
  width: 100;
}

.company-name {
  font-size: 13px;
  color: #64748b;
  margin-top: 4px;
}

.location-display {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #64748b;
}

.applications-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: #8057ff15;
  color: #8057ff;
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

/* Company-specific styles */
.company-cell {
  display: flex;
  align-items: center;
  gap: 10px;
}

.company-logo-small {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  object-fit: contain;
  background-color: #f5f5f5;
}

.company-logo-placeholder {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #aaa;
  font-size: 12px;
}

/* Two column layout */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

/* Loading and Error States */
.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1rem;
  background-color: #fff;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 1.5rem;
}

.loading-container {
  color: #3b82f6;
  gap: 1rem;
}

.error-container {
  color: #ef4444;
  gap: 1rem;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1rem;
  font-weight: 500;
}

.retry-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: #ef4444;
  border: none;
  border-radius: 0.375rem;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #fff;
  cursor: pointer;
  transition: all 0.2s ease;
}

.retry-button:hover {
  background-color: #dc2626;
}

/* No data message */
.no-data-message {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 2rem 1rem;
  color: #94a3b8;
  font-size: 0.875rem;
  text-align: center;
}

.dashboard-actions {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.logout-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: #f87171;
  border: 1px solid #ef4444;
  border-radius: 0.375rem;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #fff;
  cursor: pointer;
  transition: all 0.2s ease;
}

.logout-button:hover {
  background-color: #ef4444;
}

@media (max-width: 640px) {
  .dashboard-actions {
    flex-direction: column;
    align-items: flex-end;
    gap: 0.5rem;
  }
  
  .refresh-button,
  .logout-button {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
  }
}