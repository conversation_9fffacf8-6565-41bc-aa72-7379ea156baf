const jwt = require('jsonwebtoken');
const AdminUser = require('../models/adminUserModel');

// Authentication middleware
const authenticate = (req, res, next) => {
  // Get token from header
  const token = req.header('x-auth-token');

  // Check if no token
  if (!token) {
    return res.status(401).json({ error: 'No token, authorization denied' });
  }

  try {
    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your_jwt_secret');
    
    // Add user from payload
    req.user = decoded;
    next();
  } catch (err) {
    res.status(401).json({ error: 'Token is not valid' });
  }
};

// Role-based authorization middleware
const authorize = (requiredRole) => {
  return (req, res, next) => {
    // First ensure user is authenticated
    if (!req.user || !req.user.admin_id) {
      return res.status(401).json({ error: 'User not authenticated' });
    }

    // Check if user has the required role
    AdminUser.hasRole(req.user.admin_id, requiredRole, (err, hasAccess) => {
      if (err) {
        console.error('Role check error:', err);
        return res.status(500).json({ error: 'Server error during authorization' });
      }

      if (!hasAccess) {
        return res.status(403).json({ 
          error: `Access denied. Required role: ${requiredRole}` 
        });
      }

      // User has the required role, proceed
      next();
    });
  };
};

module.exports = {
  authenticate,
  authorize
};