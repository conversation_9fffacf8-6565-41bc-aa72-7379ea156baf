/* FAQ Page Styles */
.faq-page {
  width: 100%;
  margin: 0 auto;
  padding: 0 210px; /* Add horizontal padding to replace main-content-padded */
  font-family: Arial, sans-serif;
  color: #333;
  overflow-x: hidden;
  margin-bottom: 0;
  box-sizing: border-box;
}

/* Responsive padding for FAQ */
@media (max-width: 1400px) {
  .faq-page {
    padding: 0 80px;
  }
}

@media (max-width: 768px) {
  .faq-page {
    padding: 0 30px;
  }
}

@media (max-width: 480px) {
  .faq-page {
    padding: 0 15px;
  }
}

/* Header Section */
.faq-header {
  text-align: center;
  padding: 100px 0 60px; /* Remove horizontal padding, use page padding instead */
  max-width: 800px;
  margin: 0 auto;
  position: relative;
}

.faq-header:after {
  content: '';
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background-color: var(--primary-color);
  border-radius: 2px;
}

.faq-header h1 {
  font-size: 48px;
  font-weight: 700;
  margin-bottom: 20px;
  color: #222;
  letter-spacing: -0.5px;
}

.faq-header p {
  font-size: 18px;
  line-height: 1.6;
  color: #666;
  max-width: 700px;
  margin: 0 auto;
}

/* Image Section */
.faq-images {
  display: flex;
  padding: 0 var(--padding-size);
  margin: 0 auto 80px;
  gap: 24px;
  max-width: 1400px;
}

.faq-image-main, .faq-image-side {
  position: relative;
  height: 450px;
}

.faq-image-main {
  flex: 2;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.faq-image-main img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 16px;
  transition: transform 0.5s ease;
}

.faq-image-side {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.faq-image-side > div {
  position: relative;
  height: calc((450px - 24px) / 2);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.08);
}

.faq-image-side img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.faq-images img:hover {
  transform: scale(1.05);
}

/* Make the entire image section a fixed height to ensure consistency */
@media (min-width: 768px) {
  .faq-images {
    height: 450px;
  }
}

/* FAQ Content Section */
.faq-content {
  /* padding: 40px var(--padding-size) 80px; */
  max-width: 1400px;
  margin: 0 auto;
}

.faq-title {
  text-align: center;
  margin-bottom: 60px;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
  position: relative;
}

.faq-title:after {
  content: '';
  position: absolute;
  bottom: -20px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background-color: var(--primary-color);
  border-radius: 2px;
}

.faq-title h2 {
  font-size: 36px;
  font-weight: 700;
  margin-bottom: 20px;
  color: #222;
  letter-spacing: -0.5px;
}

.faq-title p {
  font-size: 18px;
  line-height: 1.6;
  color: #666;
}

.faq-columns {
  display: flex;
  gap: 40px;
  margin: 0 auto;
}

.faq-column {
  flex: 1;
}

.faq-item {
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  margin-bottom: 20px;
  overflow: hidden;
  transition: all 0.3s ease;
  background-color: white;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.03);
}

.faq-item:hover {
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.08);
  transform: translateY(-2px);
}

.faq-item.expanded {
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.08);
  border-color: var(--primary-color);
}

.faq-question {
  display: flex;
  align-items: flex-start;
  padding: 24px;
  cursor: pointer;
  background-color: white;
  transition: background-color 0.2s ease;
}

.faq-question:hover {
  background-color: #f9f9f9;
}

.faq-question svg {
  margin-right: 15px;
  margin-top: 4px;
  color: var(--primary-color);
  flex-shrink: 0;
  transition: transform 0.3s ease;
}

.faq-item.expanded .faq-question svg {
  transform: rotate(180deg);
}

.faq-question h3 {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  color: #333;
  line-height: 1.4;
}

.faq-answer {
  padding: 0 24px 24px 57px;
  line-height: 1.7;
  color: #666;
  animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Commitment Section */
.commitment-section {
  padding: 80px var(--padding-size);
  background-color: #f8f9fa;
  margin-top: 60px;
  position: relative;
  overflow: hidden;
}

.commitment-section:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 8px;
  background: linear-gradient(to right, var(--primary-color), var(--primary-color-h));
  opacity: 0.7;
}

.commitment-content {
  display: flex;
  gap: 60px;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
}

.commitment-text {
  flex: 1;
  padding-right: 20px;
}

.subtitle {
  display: block;
  font-size: 16px;
  font-weight: 600;
  text-transform: uppercase;
  color: var(--primary-color);
  letter-spacing: 2px;
  margin-bottom: 15px;
  position: relative;
  padding-left: 15px;
}

.subtitle:before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6px;
  height: 20px;
  background-color: var(--primary-color);
  border-radius: 3px;
}

.commitment-text h2 {
  font-size: 36px;
  font-weight: 700;
  margin-bottom: 25px;
  color: #222;
  line-height: 1.3;
}

.commitment-text p {
  margin-bottom: 20px;
  line-height: 1.8;
  color: #555;
  font-size: 17px;
}

.learn-more-btn {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 14px 30px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 16px;
  cursor: pointer;
  margin-top: 15px;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(var(--primary-color-rgb), 0.3);
}

.learn-more-btn:hover {
  background-color: var(--primary-color-h);
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(var(--primary-color-rgb), 0.4);
}

.commitment-image {
  flex: 1;
  position: relative;
}

.commitment-image:before {
  content: '';
  position: absolute;
  bottom: -20px;
  right: -20px;
  width: 80%;
  height: 80%;
  border: 6px solid var(--primary-color);
  border-radius: 12px;
  opacity: 0.2;
  z-index: 0;
}

.commitment-image img {
  width: 100%;
  border-radius: 12px;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
  position: relative;
  z-index: 1;
}

/* Call To Action Section */
.cta-section {
  padding: 80px var(--padding-size) 0;
  background: linear-gradient(135deg, #f0f4ff 0%, #e8f0ff 100%);
  text-align: center;
  position: relative;
  margin-bottom: 0;
}

.cta-section:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.03'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  opacity: 0.7;
}

.cta-content {
  max-width: 900px;
  margin: 0 auto;
  position: relative;
  z-index: 2;
  background-color: rgba(255, 255, 255, 0.7);
  padding: 50px 50px 30px;
  border-radius: 16px;
  backdrop-filter: blur(5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.08);
  margin-bottom: 0;
}

.cta-section h2 {
  font-size: 38px;
  font-weight: 700;
  margin-bottom: 20px;
  color: #222;
  line-height: 1.3;
}

.cta-section p {
  margin-bottom: 30px;
  color: #555;
  font-size: 18px;
  line-height: 1.7;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
}

.cta-buttons {
  display: flex;
  justify-content: center;
  gap: 25px;
}

.contact-btn {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 16px 32px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(var(--primary-color-rgb), 0.3);
}

.contact-btn:hover {
  background-color: var(--primary-color-h);
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(var(--primary-color-rgb), 0.4);
}

.learn-more-link {
  background-color: transparent;
  color: #333;
  border: 2px solid #ddd;
  padding: 15px 30px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.learn-more-link:hover {
  color: var(--primary-color);
  border-color: var(--primary-color);
  background-color: rgba(var(--primary-color-rgb), 0.05);
  transform: translateY(-3px);
}

/* Responsive Styles */
@media (max-width: 1200px) {
  .faq-images, .faq-content, .commitment-content {
    max-width: 95%;
  }
}

@media (max-width: 992px) {
  .faq-columns {
    flex-direction: column;
  }
  
  .commitment-content {
    flex-direction: column;
  }
  
  .faq-images {
    flex-direction: column;
  }
  
  .faq-image-side {
    flex-direction: row;
  }
  
  .faq-image-main {
    height: 350px;
  }
  
  .faq-image-side img {
    height: 180px;
  }
  
  .commitment-text {
    padding-right: 0;
    margin-bottom: 40px;
  }
  
  .faq-header h1, .cta-section h2 {
    font-size: 36px;
  }
}

@media (max-width: 768px) {
  /* .faq-header {
    padding: 80px var(--padding-size) 40px;
  } */
  
  .faq-header h1 {
    font-size: 32px;
  }
  
  .faq-title h2, .commitment-text h2 {
    font-size: 28px;
  }
  
  .cta-section h2 {
    font-size: 28px;
  }
  
  .faq-image-side {
    flex-direction: column;
  }
  
  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .cta-content {
    padding: 30px 20px;
  }
  
  .faq-question h3 {
    font-size: 16px;
  }
}

@media (max-width: 576px) {
  .faq-header p, .faq-title p, .commitment-text p, .cta-section p {
    font-size: 16px;
  }
  
  .subtitle {
    font-size: 14px;
  }
  
  .learn-more-btn, .contact-btn, .learn-more-link {
    width: 100%;
    padding: 12px 20px;
    font-size: 15px;
  }
}