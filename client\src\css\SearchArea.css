/* SearchArea.css */
.SearchArea {
  background: linear-gradient(135deg, #f9f2ff 0%, #ffefef 100%);
  padding: 80px 20px;
  max-width: 100%;
  text-align: start;
  margin-bottom: 40px;
  box-sizing: border-box;
  position: relative;
  /* overflow: hidden; */
  margin: 0 auto;
    border: 1px solid #e2e8f0;
}

/* Animated background elements */
.SearchArea::before {
  content: "";
  position: absolute;
  top: -50px;
  right: -50px;
  width: 200px;
  height: 200px;
  background: rgba(134, 87, 243, 0.1);
  border-radius: 50%;
  z-index: 0;
}

.SearchArea::after {
  content: "";
  position: absolute;
  bottom: -0px;
  left: -100px;
  width: 300px;
  height: 300px;
  background: rgba(255, 107, 107, 0.1);
  border-radius: 50%;
  z-index: 0;
}

/* Content wrapper */
.SearchArea .job-title,
.SearchArea .job-subtitle,
.SearchArea .job-search-box,
.SearchArea .selected-filters {
  position: relative;
  z-index: 2;
  margin-left: auto;
  margin-right: auto;
  max-width: calc(100% - 25%);
}

.job-title {
  font-size: 42px;
  font-weight: 800;
  color: #2a2a2a;
  margin-bottom: 15px;
  line-height: 1.3;
  text-shadow: 0 2px 4px rgba(0,0,0,0.05);
  background:#000;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.job-subtitle {
  font-size: 18px;
  color: #666;
  margin-bottom: 40px;
  max-width: 600px;
}

.job-search-box {
  display: flex;
  align-items: center;
  background-color: #ffffff;
  border-radius: 16px;
  box-shadow: 0 15px 30px rgba(134, 87, 243, 0.15);
  padding: 12px;
  gap: 12px;
  width: 100%;
  transform: translateY(0);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid rgba(255,255,255,0.3);
  backdrop-filter: blur(5px);
}

.job-search-box:hover {
  transform: translateY(-3px);
  box-shadow: 0 20px 40px rgba(134, 87, 243, 0.2);
}

.search-input {
  flex: 1;
  position: relative;
  min-width: 100%;
}

.search-icon {
  position: absolute;
  left: 18px;
  top: 50%;
  transform: translateY(-50%);
  color: #8657f3;
  z-index: 1;
  pointer-events: none;
  transition: all 0.3s ease;
}

.search-input input  , .search-input select {
  width: 100%;
  padding: 16px 16px 16px 48px;
  border: none;
  border-radius: 10px;
  font-size: 15px;
  color: #333;

  outline: none;
  transition: all 0.3s ease;
  box-shadow: inset 0 1px 3px rgba(0,0,0,0.05);
}

.search-input input::placeholder {
  color: #aaa;
  transition: all 0.3s ease;
}

/* .search-input input:focus {
  background-color: #fff;
  box-shadow: inset 0 0 0 2px #8657f3, 0 3px 6px rgba(134, 87, 243, 0.1);
} */

.search-input input:focus::placeholder {
  opacity: 0.5;
}

.search-input input:focus + .search-icon {
  color: #ff6b6b;
  transform: translateY(-50%) scale(1.1);
}

.select-wrapper {
  position: relative;
  min-width: 140px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.custom-select {
  position: relative;
  width: 100%;
}

.select-arrow {
  position: absolute;
  top: 54%;
  right: 16px;
  transform: translateY(-50%);
  pointer-events: none;
  color: #8657f3;
  font-size: 12px;
  transition: all 0.3s ease;
}

.select-wrapper.job-type {
  border-left: 1px solid rgba(134, 87, 243, 0.1);
  border-right: 1px solid rgba(134, 87, 243, 0.1);
  padding: 0 15px;
  margin: 0 5px;
}

.select-wrapper.location {
  padding: 0 15px;
}

.select-icon {
  position: absolute;
  left: 35px; 
  top: 50%;
  transform: translateY(-50%);
  color: #8657f3;
  font-size: 15px;
  z-index: 1;
  pointer-events: none;
  transition: all 0.3s ease;
}

.dropdown-icon {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #8657f3;
  font-size: 12px;
  pointer-events: none;
  transition: all 0.3s ease;
}

select {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  border: none;
  background: #f9f5ff;
  width: 100%;
  padding: 16px 30px 16px 45px;
  font-size: 15px;
  color: #333;
  cursor: pointer;
  outline: none;
  position: relative;
  z-index: 0;
  border-radius: 10px;
  transition: all 0.3s ease;
  box-shadow: inset 0 1px 3px rgba(0,0,0,0.05);
}

/* select:focus {
  background-color: #fff;
  box-shadow: inset 0 0 0 2px #8657f3, 0 3px 6px rgba(134, 87, 243, 0.1);
} */

select:focus + .select-arrow,
select:focus ~ .select-icon {
  color: #ff6b6b;
}

select::-ms-expand {
  display: none;
}

.salary-section {
  position: relative;
  min-width: 130px;
  padding: 0 5px;
}

.salary-toggle {
  display: flex;
  align-items: center;
  background: #f9f5ff;
  border: none;
  border-radius: 10px;
  padding: 16px 30px 16px 45px;
  cursor: pointer;
  font-size: 15px;
  color: #333;
  position: relative;
  white-space: nowrap;
  width: 100%;
  text-align: left;
  transition: all 0.3s ease;
  box-shadow: inset 0 1px 3px rgba(0,0,0,0.05);
}

.salary-toggle:hover {
  background: #f0e6ff;
}

.salary-icon {
  position: absolute;
  left: 18px;
  top: 50%;
  transform: translateY(-50%);
  color: #8657f3;
  pointer-events: none;
  transition: all 0.3s ease;
}

.salary-dropdown {
  position: absolute;
  top: calc(100% + 10px);
  left: 0;
  width: 320px;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 15px 30px rgba(134, 87, 243, 0.2);
  padding: 20px;
  margin-top: 5px;
  animation: fadeInUp 0.3s ease-out;
  border: 1px solid rgba(134, 87, 243, 0.1);
}

.salary-options {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.salary-input {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.salary-input label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
  margin-bottom: 5px;
}

.input-with-spinner {
  display: flex;
  position: relative;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.input-with-spinner:focus-within {
  border-color: #8657f3;
  box-shadow: 0 0 0 2px rgba(134, 87, 243, 0.2);
}

.input-with-spinner input {
  flex: 1;
  padding: 12px 15px;
  border: none;
  font-size: 15px;
  outline: none;
  width: calc(100% - 30px);
  background: #f9f9f9;
}

.spinner-buttons {
  display: flex;
  flex-direction: column;
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  width: 30px;
  background-color: #f5f5f5;
  border-left: 1px solid #e0e0e0;
}

.spinner-up, .spinner-down {
  height: 50%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  background-color: #f5f5f5;
  color: #8657f3;
  font-size: 10px;
  cursor: pointer;
  padding: 0;
  transition: all 0.2s ease;
}

.spinner-up:hover, .spinner-down:hover {
  background-color: #8657f3;
  color: white;
}

.spinner-up {
  border-bottom: 1px solid #e0e0e0;
}

.update-button {
  width: 100%;
  padding: 14px;
  background: linear-gradient(90deg, #8657f3, #a678ff);
  border: none;
  border-radius: 8px;
  color: white;
  font-weight: 600;
  cursor: pointer;
  margin-top: 20px;
  transition: all 0.3s ease;
  text-align: center;
  box-shadow: 0 4px 10px rgba(134, 87, 243, 0.3);
}

.update-button:hover {
  background: linear-gradient(90deg, #7745e6, #9565f5);
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(134, 87, 243, 0.4);
}

.update-button:active {
  transform: translateY(0);
}

.find-button {
  background: linear-gradient(90deg, #8657f3, #ff6b6b);
  color: white;
  border: none;
  border-radius: 10px;
  padding: 16px 28px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  white-space: nowrap;
  transition: all 0.3s ease;
  min-width: 120px;
  box-shadow: 0 4px 15px rgba(134, 87, 243, 0.4);
  position: relative;
  overflow: hidden;
}

.find-button:hover {
  background: linear-gradient(90deg, #7745e6, #ff5757);
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(134, 87, 243, 0.5);
}

.find-button:active {
  transform: translateY(0);
}

.find-button::after {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: rgba(255,255,255,0.1);
  transform: rotate(30deg);
  transition: all 0.3s ease;
}

.find-button:hover::after {
  left: 100%;
}

/* Keyframe animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(134, 87, 243, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(134, 87, 243, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(134, 87, 243, 0);
  }
}

/* Responsive Adjustments */
@media (max-width: 1200px) {
  .job-title {
    font-size: 38px;
  }
  
  .job-search-box {
    padding: 10px;
    gap: 10px;
  }
}

@media (max-width: 992px) {
  .job-title {
    font-size: 34px;
  }
  
  .job-subtitle {
    font-size: 16px;
  }
  
  .search-input input,
  select,
  .salary-toggle {
    padding: 14px 14px 14px 42px;
    font-size: 14px;
  }
  
  .select-icon,
  .salary-icon {
    left: 16px;
  }
}

@media (max-width: 1400px) {
  .SearchArea {
    padding: 80px 0 40px;
  }
  
  .job-title {
    font-size: 30px;
    text-align: start;
  }
  
  .job-subtitle {
    text-align: start;
    margin-bottom: 30px;
  }
  
  .job-search-box {
    flex-direction: column;
    padding: 20px;
    gap: 15px;
    max-width: 50px;
  }
  
  .search-input,
  .select-wrapper,
  .salary-section {
    width: 100%;
  }
  
  .find-button {
    width: 100%;
  }
  
  .salary-dropdown {
    width: 100%;
    left: 0;
    z-index: 1;
  }
}

@media (max-width: 576px) {
  .job-title {
    font-size: 26px;
  }
  
  .job-subtitle {
    font-size: 15px;
  }
  
  .job-search-box {
    padding: 15px;
  }
  
  .search-input input,
  select,
  .salary-toggle {
    padding: 12px 12px 12px 40px;
  }
  
  .select-icon,
  .salary-icon {
    left: 14px;
  }
}

/* Removed floating animation from search box */

/* Removed pulse animation from find button */

.job-title-large {
  font-size: 42px;
  font-weight: 800;
  color: #2a2a2a;
  margin-bottom: 15px;
  line-height: 1.3;
  text-shadow: 0 2px 4px rgba(0,0,0,0.05);
  background: linear-gradient(135deg, #333 0%, #000 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  max-width: calc(100% - 25%);
  margin-left: auto;
  margin-right: auto;
  position: relative;
  z-index: 2;
}

/* Update responsive styles for the new large title */
@media (max-width: 1200px) {
  .job-title-large {
    font-size: 48px;
  }
}

@media (max-width: 992px) {
  .job-title-large {
    font-size: 42px;
  }
}

@media (max-width: 768px) {
  .job-title-large {
    font-size: 35px;
    padding: 0 20px;
  }
}

@media (max-width: 576px) {
  .job-title-large {
    font-size: 32px;
    padding: 0 10px;
  }
}

  /* @media (max-width: 1400px) {
  .job-title-large {
    font-size: 32px;
    padding: 0 10px;
  }
} */