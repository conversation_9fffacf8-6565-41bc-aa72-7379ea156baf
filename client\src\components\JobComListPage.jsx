// JobComListPage.jsx
import React, { useState } from 'react';
import '../css/JobComListPage.css';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faMapMarkerAlt, 
  faLayerGroup, 
  faEnvelope, 
  faBookmark, 
  faShareSquare,
  faStar
} from '@fortawesome/free-solid-svg-icons';

const JobComListPage = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const companiesPerPage = 9;

  // Sample company data
  const companies = [
    {
      id: 1,
      name: "Google Inc.",
      logo: "G",
      industry: "Technology",
      rating: 4.5,
      location: "Mountain View, CA",
      jobCount: 45,
      category: "Technology"
    },
    {
      id: 2,
      name: "Microsoft",
      logo: "M",
      industry: "Software Development",
      rating: 4.2,
      location: "Redmond, WA",
      jobCount: 38,
      category: "Technology"
    },
    {
      id: 3,
      name: "Amazon",
      logo: "A",
      industry: "E-commerce & Cloud",
      rating: 4.0,
      location: "Seattle, WA",
      jobCount: 52,
      category: "Technology"
    },
    {
      id: 4,
      name: "Apple Inc.",
      logo: "A",
      industry: "Consumer Electronics",
      rating: 4.3,
      location: "Cupertino, CA",
      jobCount: 27,
      category: "Technology"
    },
    {
      id: 5,
      name: "Facebook",
      logo: "F",
      industry: "Social Media",
      rating: 4.1,
      location: "Menlo Park, CA",
      jobCount: 32,
      category: "Technology"
    },
    {
      id: 6,
      name: "Tesla",
      logo: "T",
      industry: "Automotive & Energy",
      rating: 4.2,
      location: "Palo Alto, CA",
      jobCount: 24,
      category: "Automotive"
    },
    {
      id: 7,
      name: "JPMorgan",
      logo: "JP",
      industry: "Banking & Finance",
      rating: 3.9,
      location: "New York, NY",
      jobCount: 35,
      category: "Finance"
    },
    {
      id: 8,
      name: "IBM",
      logo: "IBM",
      industry: "Technology & Consulting",
      rating: 3.8,
      location: "Armonk, NY",
      jobCount: 41,
      category: "Technology"
    },
    {
      id: 9,
      name: "Netflix",
      logo: "N",
      industry: "Entertainment",
      rating: 4.4,
      location: "Los Gatos, CA",
      jobCount: 18,
      category: "Media"
    },
    {
      id: 10,
      name: "Oracle",
      logo: "O",
      industry: "Software & Database",
      rating: 3.7,
      location: "Redwood City, CA",
      jobCount: 29,
      category: "Technology"
    },
    {
      id: 11,
      name: "Salesforce",
      logo: "S",
      industry: "CRM Software",
      rating: 4.2,
      location: "San Francisco, CA",
      jobCount: 31,
      category: "Software"
    },
    {
      id: 12,
      name: "Uber",
      logo: "U",
      industry: "Transportation",
      rating: 3.8,
      location: "San Francisco, CA",
      jobCount: 23,
      category: "Transportation"
    }
  ];

  // Calculate pagination
  const indexOfLastCompany = currentPage * companiesPerPage;
  const indexOfFirstCompany = indexOfLastCompany - companiesPerPage;
  const currentCompanies = companies.slice(indexOfFirstCompany, indexOfLastCompany);
  const totalPages = Math.ceil(companies.length / companiesPerPage);

  // Render rating stars
  const renderStars = (rating) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 1; i <= 5; i++) {
      if (i <= fullStars) {
        stars.push(<FontAwesomeIcon key={i} icon={faStar} className="jobcom-star" />);
      } else if (i === fullStars + 1 && hasHalfStar) {
        stars.push(<FontAwesomeIcon key={i} icon={faStar} className="jobcom-star" style={{ opacity: 0.5 }} />);
      } else {
        stars.push(<FontAwesomeIcon key={i} icon={faStar} className="jobcom-star-inactive" />);
      }
    }

    return stars;
  };

  // Generate pagination buttons
  const renderPaginationButtons = () => {
    const buttons = [];
    
    // Previous button
    buttons.push(
      <button 
        key="prev" 
        className="jobcom-pagination-btn"
        onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
        disabled={currentPage === 1}
      >
        &laquo;
      </button>
    );
    
    // Page number buttons
    for (let i = 1; i <= totalPages; i++) {
      buttons.push(
        <button
          key={i}
          className={`jobcom-pagination-btn ${currentPage === i ? 'active' : ''}`}
          onClick={() => setCurrentPage(i)}
        >
          {i}
        </button>
      );
    }
    
    // Next button
    buttons.push(
      <button
        key="next"
        className="jobcom-pagination-btn"
        onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
        disabled={currentPage === totalPages}
      >
        &raquo;
      </button>
    );
    
    return buttons;
  };

  return (
    <div className="jobcom-container">
      {/* Sidebar */}
      <div className="jobcom-sidebar">
        {/* Job Reminder Section */}
        <div className="jobcom-section">
          <h3 className="jobcom-section-title">Job Reminder</h3>
          <p className="jobcom-section-description">Get notified about new job listings that match your interests.</p>
          <div className="jobcom-input-group">
            <FontAwesomeIcon icon={faEnvelope} className="jobcom-input-icon" />
            <input type="email" className="jobcom-input" placeholder="Enter your email" />
          </div>
          <button className="jobcom-btn">Subscribe</button>
        </div>

        {/* Location Filter Section */}
        <div className="jobcom-section">
          <h3 className="jobcom-section-title">Location</h3>
          <div className="jobcom-input-group">
            <FontAwesomeIcon icon={faMapMarkerAlt} className="jobcom-input-icon" />
            <input type="text" className="jobcom-input" placeholder="City or state" />
          </div>
          <div className="jobcom-filter-items">
            <div className="jobcom-filter-item">
              <input type="checkbox" id="loc1" />
              <label className="jobcom-filter-label" htmlFor="loc1">California</label>
              <span className="jobcom-filter-count">35</span>
            </div>
            <div className="jobcom-filter-item">
              <input type="checkbox" id="loc2" />
              <label className="jobcom-filter-label" htmlFor="loc2">New York</label>
              <span className="jobcom-filter-count">27</span>
            </div>
            <div className="jobcom-filter-item">
              <input type="checkbox" id="loc3" />
              <label className="jobcom-filter-label" htmlFor="loc3">Washington</label>
              <span className="jobcom-filter-count">18</span>
            </div>
            <div className="jobcom-filter-item">
              <input type="checkbox" id="loc4" />
              <label className="jobcom-filter-label" htmlFor="loc4">Texas</label>
              <span className="jobcom-filter-count">14</span>
        </div>
          </div>
        </div>

        {/* Category Filter Section */}
        <div className="jobcom-section">
          <h3 className="jobcom-section-title">Category</h3>
          <div className="jobcom-input-group">
            <FontAwesomeIcon icon={faLayerGroup} className="jobcom-input-icon" />
            <input type="text" className="jobcom-input" placeholder="Search category" />
          </div>
          <div className="jobcom-filter-items">
            <div className="jobcom-filter-item">
              <input type="checkbox" id="cat1" />
              <label className="jobcom-filter-label" htmlFor="cat1">Technology</label>
              <span className="jobcom-filter-count">48</span>
            </div>
            <div className="jobcom-filter-item">
              <input type="checkbox" id="cat2" />
              <label className="jobcom-filter-label" htmlFor="cat2">Finance</label>
              <span className="jobcom-filter-count">23</span>
        </div>
            <div className="jobcom-filter-item">
              <input type="checkbox" id="cat3" />
              <label className="jobcom-filter-label" htmlFor="cat3">Healthcare</label>
              <span className="jobcom-filter-count">19</span>
            </div>
            <div className="jobcom-filter-item">
              <input type="checkbox" id="cat4" />
              <label className="jobcom-filter-label" htmlFor="cat4">Education</label>
              <span className="jobcom-filter-count">15</span>
            </div>
            <div className="jobcom-filter-item">
              <input type="checkbox" id="cat5" />
              <label className="jobcom-filter-label" htmlFor="cat5">Marketing</label>
              <span className="jobcom-filter-count">12</span>
            </div>
          </div>
        </div>

        {/* Reset Filter Button */}
        <button className="jobcom-btn jobcom-btn-outline">Reset All Filters</button>
      </div>
          
      {/* Main Content */}
      <div className="jobcom-main-content">
        {/* Header */}
        <div className="jobcom-header">
          <h2 className="jobcom-title">Browse Companies</h2>
          <select className="jobcom-sort-select">
            <option>Sort by: Most Relevant</option>
            <option>Sort by: Highest Rated</option>
            <option>Sort by: Most Jobs</option>
            <option>Sort by: A-Z</option>
          </select>
        </div>

        {/* Company Grid */}
        <div className="jobcom-company-grid">
          {currentCompanies.map(company => (
            <div key={company.id} className="jobcom-company-card">
              <div className="jobcom-company-logo">
                <span>{company.logo}</span>
              </div>
              <div className="jobcom-company-info">
                <h3 className="jobcom-company-name">{company.name}</h3>
                <p className="jobcom-company-industry">{company.industry}</p>
                <div className="jobcom-company-meta">
                  <div className="jobcom-meta-item">
                    <FontAwesomeIcon icon={faMapMarkerAlt} className="jobcom-meta-icon" />
                    {company.location}
                  </div>
                </div>
                <div className="jobcom-company-rating">
                  {renderStars(company.rating)}
                  <span> {company.rating}</span>
                </div>
                <div className="jobcom-job-count">
                  {company.jobCount} open positions
                </div>
                <div className="jobcom-company-actions">
                  <button className="jobcom-action-btn">
                    <FontAwesomeIcon icon={faBookmark} className="jobcom-action-icon" />
                    Save
                  </button>
                  <button className="jobcom-action-btn">
                    <FontAwesomeIcon icon={faShareSquare} className="jobcom-action-icon" />
                    Share
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Pagination */}
        <div className="jobcom-pagination">
          {renderPaginationButtons()}
        </div>
      </div>
    </div>
  );
};

export default JobComListPage;
