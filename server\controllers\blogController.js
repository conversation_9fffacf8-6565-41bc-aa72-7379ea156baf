const Blog = require('../models/blogModel');
const fs = require('fs');
const path = require('path');
let cloudinary;

try {
  // Try to import cloudinary, but don't fail if it's not available
  cloudinary = require('../config/cloudinary');
} catch (error) {
  console.warn('Cloudinary configuration not found. Image upload will use local storage.');
  cloudinary = null;
}

// Check if Cloudinary is configured
const cloudinaryConfigured = cloudinary && (process.env.CLOUDINARY_URL || 
                            (process.env.CLOUDINARY_CLOUD_NAME && 
                             process.env.CLOUDINARY_API_KEY && 
                             process.env.CLOUDINARY_API_SECRET));

const blogController = {
  // Get all blogs
  getAllBlogs: (req, res) => {
    Blog.getAll((err, results) => {
      if (err) {
        console.error("Query error:", err);
        return res.status(500).json({ error: "Database error" });
      }
      res.json(results);
    });
  },

  // Get blog by ID
  getBlogById: (req, res) => {
    const blogId = req.params.blog_id;
    
    Blog.getById(blogId, (err, results) => {
      if (err) {
        console.error("Query error:", err);
        return res.status(500).json({ error: "Database error" });
      }
      if (results.length === 0) {
        return res.status(404).json({ error: "Blog not found" });
      }
      res.json(results[0]);
    });
  },

  // Create new blog
  createBlog: async (req, res) => {
    try {
      console.log('Blog creation request received:', req.body);
      
      // Extract blog data from request
      const { blog_title, blog_description, posted_date, category } = req.body;
      
      // Validate required fields
      if (!blog_title) {
        return res.status(400).json({ error: "Blog title is required" });
      }
      
      // Prepare blog data object
      const blogData = {
        blog_title,
        blog_description: blog_description || '',
        posted_date: posted_date || new Date().toISOString().split('T')[0],
        category: category || 'Uncategorized'
      };
      
      // Handle image upload
      if (req.file) {
        try {
          // If Cloudinary is configured, upload the image
          if (cloudinaryConfigured) {
            // Convert the file buffer to base64 for Cloudinary upload
            const fileBuffer = req.file.buffer;
            const base64String = `data:${req.file.mimetype};base64,${fileBuffer.toString('base64')}`;
            
            // Upload to Cloudinary
            try {
              const uploadResult = await cloudinary.uploader.upload(base64String, {
                folder: 'blog_images',
                upload_preset: process.env.CLOUDINARY_UPLOAD_PRESET || undefined
              });
              
              // Add the Cloudinary URL to the blog data
              blogData.image_url = uploadResult.secure_url;
            } catch (cloudinaryError) {
              console.error("Cloudinary upload error:", cloudinaryError);
              // Continue without image if Cloudinary upload fails
            }
          } else {
            console.log('Cloudinary not configured, storing image reference locally');
            // Store the filename or another reference in the database
            blogData.image_url = `/uploads/blogs/${req.file.filename}`;
          }
        } catch (fileError) {
          console.error("Error processing image file:", fileError);
          // Continue without image
        }
      }
      
      // Create blog in database
      Blog.create(blogData, (err, result) => {
        if (err) {
          console.error("Error creating blog:", err);
          return res.status(500).json({ error: "Database error" });
        }
        
        res.status(201).json({ 
          message: "Blog created successfully", 
          blog_id: result.insertId,
          ...blogData
        });
      });
    } catch (error) {
      console.error("Error in blog creation:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  },

  // Update blog
  updateBlog: async (req, res) => {
    try {
      const blogId = req.params.blog_id;
      
      // Check if blog exists
      Blog.getById(blogId, async (err, results) => {
        if (err) {
          console.error("Error checking blog existence:", err);
          return res.status(500).json({ error: "Database error" });
        }
        
        if (results.length === 0) {
          return res.status(404).json({ error: "Blog not found" });
        }
        
        const existingBlog = results[0];
        
        // Extract updated data from request
        const { blog_title, blog_description, category } = req.body;
        
        // Prepare blog data object
        const blogData = {
          blog_title: blog_title || existingBlog.blog_title,
          blog_description: blog_description || existingBlog.blog_description,
          category: category || existingBlog.category
        };
        
        // Handle image upload if present
        if (req.file) {
          try {
            // If Cloudinary is configured, upload the new image
            if (cloudinaryConfigured) {
              // Convert the file buffer to base64 for Cloudinary upload
              const fileBuffer = req.file.buffer;
              const base64String = `data:${req.file.mimetype};base64,${fileBuffer.toString('base64')}`;
              
              // Upload to Cloudinary
              try {
                const uploadResult = await cloudinary.uploader.upload(base64String, {
                  folder: 'blog_images',
                  upload_preset: process.env.CLOUDINARY_UPLOAD_PRESET || undefined
                });
                
                // Add the Cloudinary URL to the blog data
                blogData.image_url = uploadResult.secure_url;
                
                // If the existing blog has a Cloudinary image, delete it
                if (existingBlog.image_url && existingBlog.image_url.includes('cloudinary')) {
                  try {
                    // Extract public_id from Cloudinary URL
                    const urlParts = existingBlog.image_url.split('/');
                    const uploadIndex = urlParts.indexOf('upload');
                    if (uploadIndex !== -1 && uploadIndex < urlParts.length - 2) {
                      const publicId = urlParts.slice(uploadIndex + 2).join('/').split('.')[0];
                      
                      // Delete old image from Cloudinary
                      await cloudinary.uploader.destroy(publicId);
                    }
                  } catch (deleteError) {
                    console.error("Error deleting old Cloudinary image:", deleteError);
                    // Continue even if old image deletion fails
                  }
                }
              } catch (cloudinaryError) {
                console.error("Cloudinary upload error:", cloudinaryError);
                // Continue without updating image if Cloudinary upload fails
              }
            } else {
              console.log('Cloudinary not configured, storing image reference locally');
              // Store the filename or another reference in the database
              blogData.image_url = `/uploads/blogs/${req.file.filename}`;
              
              // If there was a previous local file, delete it
              if (existingBlog.image_url && existingBlog.image_url.startsWith('/uploads/')) {
                try {
                  const oldFilePath = path.join(__dirname, '..', 'public', existingBlog.image_url);
                  if (fs.existsSync(oldFilePath)) {
                    fs.unlinkSync(oldFilePath);
                  }
                } catch (deleteError) {
                  console.error("Error deleting old image file:", deleteError);
                  // Continue even if old file deletion fails
                }
              }
            }
          } catch (fileError) {
            console.error("Error processing image file:", fileError);
            // Continue without updating image
          }
        }
        
        // Update blog in database
        Blog.update(blogId, blogData, (updateErr, result) => {
          if (updateErr) {
            console.error("Error updating blog:", updateErr);
            return res.status(500).json({ error: "Database error" });
          }
          
          res.json({ 
            message: "Blog updated successfully", 
            blog_id: blogId
          });
        });
      });
    } catch (error) {
      console.error("Error in blog update:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  },

  // Delete blog
  deleteBlog: (req, res) => {
    try {
      const blogId = req.params.blog_id;
      
      // Check if blog exists and get its data
      Blog.getById(blogId, async (err, results) => {
        if (err) {
          console.error("Error checking blog existence:", err);
          return res.status(500).json({ error: "Database error" });
        }
        
        if (results.length === 0) {
          return res.status(404).json({ error: "Blog not found" });
        }
        
        const blog = results[0];
        
        // Function to extract public_id from Cloudinary URL
        const extractPublicId = (url) => {
          if (!url || typeof url !== 'string') return null;
          
          // Check if it's a Cloudinary URL
          if (url.includes('res.cloudinary.com')) {
            try {
              // Extract public_id from URL like: https://res.cloudinary.com/cloud_name/image/upload/v123456/folder/image_id.jpg
              const urlParts = url.split('/');
              const uploadIndex = urlParts.indexOf('upload');
              if (uploadIndex !== -1 && uploadIndex < urlParts.length - 1) {
                // Get everything after 'upload' and before file extension
                const pathAfterUpload = urlParts.slice(uploadIndex + 1).join('/');
                // Remove version if present (starts with 'v' followed by numbers)
                const withoutVersion = pathAfterUpload.replace(/^v\d+\//, '');
                // Remove file extension
                const publicId = withoutVersion.replace(/\.[^.]+$/, '');
                return publicId;
              }
            } catch (error) {
              console.error('Error extracting public_id from URL:', url, error);
            }
          }
          return null;
        };
        
        // Collect all Cloudinary public IDs to delete
        const publicIdsToDelete = [];
        
        // Check image_url
        if (blog.image_url) {
          const publicId = extractPublicId(blog.image_url);
          if (publicId) publicIdsToDelete.push(publicId);
        }
        
        // Delete images from Cloudinary if configured and there are images to delete
        if (cloudinaryConfigured && publicIdsToDelete.length > 0) {
          try {
            // Delete each image from Cloudinary
            const deletePromises = publicIdsToDelete.map(publicId => 
              cloudinary.uploader.destroy(publicId)
                .then(result => {
                  return result;
                })
                .catch(error => {
                  console.error(`Failed to delete image ${publicId} from Cloudinary:`, error);
                  // Don't throw error, continue with blog deletion even if image deletion fails
                  return { error: error.message };
                })
            );
            
            await Promise.all(deletePromises);
          } catch (cloudinaryError) {
            console.error('Error deleting images from Cloudinary:', cloudinaryError);
            // Continue with blog deletion even if Cloudinary deletion fails
          }
        }
        
        // If blog has a local image file, delete it
        if (blog.image_url && blog.image_url.startsWith('/uploads/')) {
          try {
            const filePath = path.join(__dirname, '..', 'public', blog.image_url);
            if (fs.existsSync(filePath)) {
              fs.unlinkSync(filePath);
            }
          } catch (fileError) {
            console.error("Error deleting image file:", fileError);
            // Continue with blog deletion even if file deletion fails
          }
        }
        
        // Delete blog from database
        Blog.delete(blogId, (deleteErr, result) => {
          if (deleteErr) {
            console.error("Error deleting blog:", deleteErr);
            return res.status(500).json({ error: "Database error" });
          }
          
          const message = publicIdsToDelete.length > 0 
            ? `Blog and ${publicIdsToDelete.length} associated image(s) deleted successfully`
            : "Blog deleted successfully";
          
          res.json({ message });
        });
      });
    } catch (error) {
      console.error('Error in deleteBlog controller:', error);
      res.status(500).json({ error: "Internal server error", details: error.message });
    }
  },
  
  // Increment view count for a blog
  incrementViewCount: (req, res) => {
    const blogId = req.params.blog_id;
    
    Blog.incrementViewCount(blogId, (err, result) => {
      if (err) {
        console.error("Error incrementing blog view count:", err);
        return res.status(500).json({ error: "Failed to update view count" });
      }
      
      if (result.affectedRows === 0) {
        return res.status(404).json({ error: "Blog not found" });
      }
      
      res.status(200).json({ message: "Blog view count incremented" });
    });
  }
};

module.exports = blogController;