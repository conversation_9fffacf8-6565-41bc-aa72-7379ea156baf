import React, { useEffect, useState } from 'react';
import { useParams, Link, useNavigate } from 'react-router-dom';
import ApiService from '../services/apiService';
import '../css/CompanyDetails.css';
import PageHelmet from './PageHelmet';

/**
 * CompanyDetails component displays detailed information about a company
 * and lists all jobs posted by that company
 */
const CompanyDetails = () => {
  const { companyId } = useParams();
  const navigate = useNavigate();
  const [company, setCompany] = useState(null);
  const [jobs, setJobs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  /**
   * Fetch company details and associated jobs
   */
  useEffect(() => {
    const fetchCompanyAndJobs = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Fetch company details
        console.log(`Fetching company with ID: ${companyId}`);
        const companyRes = await ApiService.companies.getById(companyId);
        console.log('Company API response:', companyRes.data);
        
        // Handle different response structures
        const companyData = companyRes.data.data || companyRes.data;
        
        if (!companyData) {
          throw new Error('Company data not found');
        }
        
        setCompany(companyData);
        
        // Fetch jobs for this company using company name
        console.log(`Fetching jobs for company: "${companyData.company_name}"`);
        const jobsRes = await ApiService.companies.getJobsByCompany(companyData.company_name);
        console.log('Jobs API response:', jobsRes.data);
        
        // Handle jobs response - the jobs API returns data directly
        const jobsData = Array.isArray(jobsRes.data) ? jobsRes.data : [];
        console.log(`Found ${jobsData.length} jobs for company "${companyData.company_name}"`);
        
        // Sort jobs by date - newest first
        const sortedJobs = jobsData.sort((a, b) => {
          const dateA = new Date(a.posted_date || a.start_date || a.created_at);
          const dateB = new Date(b.posted_date || b.start_date || b.created_at);
          return dateB - dateA; // Newest first
        });
        
        setJobs(sortedJobs);
        
      } catch (err) {
        console.error('Error fetching company or jobs:', err);
        setError(`Failed to load company or jobs: ${err.message}`);
      } finally {
        setLoading(false);
      }
    };

    if (companyId) {
      fetchCompanyAndJobs();
    } else {
      setError('Company ID is required');
      setLoading(false);
    }
  }, [companyId]);

  // Force update the document title when company data changes
  useEffect(() => {
    if (company && company.company_name) {
      document.title = `Job Page | ${company.company_name}`;
    } else if (loading) {
      document.title = 'Job Page | Loading Company';
    } else if (error || !company) {
      document.title = 'Job Page | Company Not Found';
    }
  }, [company, loading, error]);

  // Loading state
  if (loading) {
    return (
      <div className="company-details-loading">
        <PageHelmet title="Loading Company" />
        <div>Loading company details...</div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="company-details-error">
        <PageHelmet title="Company Not Found" />
        <div>{error}</div>
        <button onClick={() => navigate(-1)} className="back-button">
          ← Go Back
        </button>
      </div>
    );
  }

  // Company not found state
  if (!company) {
    console.log('Company is null:', company);
    return (
      <div className="company-details-error">
        <PageHelmet title="Company Not Found" />
        <div>Company not found.</div>
        <button onClick={() => navigate(-1)} className="back-button">
          ← Go Back
        </button>
      </div>
    );
  }

  return (
    <div className="company-details-container">
      <PageHelmet 
        title={company.company_name || "Company Details"} 
        description={`Explore job opportunities at ${company.company_name} and learn more about the company.`}
      />
      <div className="content-spacing"></div>
      
      {/* Back button */}
      {/* <div className="back-button-container">
        <button 
          className="back-button" 
          onClick={() => navigate(-1)}
          aria-label="Go back"
        >
          ← Back
        </button>
      </div> */}
      
      {/* Company header section */}
      <div className="company-header">
        <img 
          src={company.company_logo_url || 'https://via.placeholder.com/80'} 
          alt={company.company_name} 
          className="company-logo-large" 
          onError={(e) => {
            e.target.src = 'https://via.placeholder.com/80';
          }}
        />
        <div className="company-info-block">
          <h1>{company.company_name}</h1>
          <p className="company-description">
            {company.company_description || 'No description available.'}
          </p>
          {company.company_website && (
            <a 
              href={company.company_website} 
              target="_blank" 
              rel="noopener noreferrer" 
              className="company-website-link"
            >
              Visit Website
            </a>
          )}
        </div>
      </div>
      
      {/* Jobs section */}
      <div className="company-jobs-section">
        <h2>
          Jobs at {company.company_name} 
          <span className="jobs-count-badge">{jobs.length}</span>
        </h2>
        
        {jobs.length === 0 ? (
          <div className="no-jobs-message">
            <p>No jobs published by this company yet.</p>
          </div>
        ) : (
          <ul className="company-jobs-list">
            {jobs.map(job => (
              <Link to={`/job/${job.job_id}`} className="job-title-link">
              <li key={job.job_id} className="company-job-item">
                <div className="job-info">  
                    {job.job_title}
                  <div className="job-meta">
                    <span className="company-job-type">{job.job_type}</span>
                    <span className="company-job-category">{job.main_topics}</span>
                    {(job.hot === true || job.hot === 1 || job.hot === '1') && (
                      <span className="hot-badge">🔥 URGENT</span>
                    )}
                  </div>
                </div>
                <span className="company-job-date">
                  📅 {new Date(job.start_date).toLocaleDateString()}
                </span>
              </li>
              </Link>
            ))}
          </ul>
        )}
      </div>
    </div>
  );
};

export default CompanyDetails;
