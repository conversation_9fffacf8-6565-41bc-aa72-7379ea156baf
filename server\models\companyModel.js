const db = require('../config/db');

const Company = {
  // Get all companies
  getAll: (callback) => {
    const query = "SELECT * FROM companies";
    db.query(query, (err, results) => {
      if (err) {
        console.error("Database query failed:", err);
        return callback(err, null);
      }
      
      callback(null, results);
    });
  },

  // Get company by ID
  getById: (companyId, callback) => {
    const query = "SELECT * FROM companies WHERE company_id = ?";
    db.query(query, [companyId], callback);
  },

  // Create new company
  create: (companyData, callback) => {
    const newCompany = {
      company_name: companyData.company_name || '',
      company_logo_url: companyData.company_logo_url || '',
      company_description: companyData.company_description || '',
      company_website: companyData.company_website || '',
      created_at: new Date()
    };
    
    // Build query dynamically based on available fields
    const fields = Object.keys(newCompany);
    const placeholders = fields.map(() => '?').join(', ');
    const values = fields.map(field => newCompany[field]);
    
    const query = `INSERT INTO companies (${fields.join(', ')}) VALUES (${placeholders})`;
    
    db.query(query, values, (err, result) => {
      if (err) {
        console.error("Error in company creation SQL:", err);
        return callback(err, null);
      }
      callback(null, result);
    });
  },

  // Update company
  update: (companyId, companyData, callback) => {
    // Only update fields that are provided
    const updates = {};
    
    if (companyData.company_name !== undefined) updates.company_name = companyData.company_name;
    if (companyData.company_logo_url !== undefined) updates.company_logo_url = companyData.company_logo_url;
    if (companyData.company_description !== undefined) updates.company_description = companyData.company_description;
    if (companyData.company_website !== undefined) updates.company_website = companyData.company_website;
    updates.updated_at = new Date();
    
    // Only proceed if there are fields to update
    if (Object.keys(updates).length === 0) {
      return callback(null, { affectedRows: 0 });
    }
    
    // Build update query
    const setClause = Object.keys(updates)
      .map(key => `${key} = ?`)
      .join(', ');
    const values = [...Object.values(updates), companyId];
    
    const query = `UPDATE companies SET ${setClause} WHERE company_id = ?`;
    
    db.query(query, values, (err, result) => {
      if (err) {
        console.error("Error in company update SQL:", err);
        return callback(err, null);
      }
      callback(null, result);
    });
  },

  // Delete company
  delete: (companyId, callback) => {
    const query = "DELETE FROM companies WHERE company_id = ?";
    db.query(query, [companyId], (err, result) => {
      if (err) {
        console.error("Error in company deletion SQL:", err);
        return callback(err, null);
      }
      callback(null, result);
    });
  }
};

module.exports = Company;
