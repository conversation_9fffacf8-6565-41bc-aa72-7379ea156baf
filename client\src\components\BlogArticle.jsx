/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable no-unused-vars */
import React, { useState, useEffect } from 'react';
import { useLocation, useParams, useNavigate } from 'react-router-dom';
import '../css/BlogArticle.css';
import banner from "../assets/jobdetails.png";
import banneradd from "../assets/banneradd.png";
import ApiService from '../services/apiService';
import Navbar from './Navbar';
import PageHelmet from './PageHelmet';

const BlogArticle = () => {
  const location = useLocation();
  const { id } = useParams();
  const navigate = useNavigate();
  
  const [blogData, setBlogData] = useState(null);
  const [relatedBlogs, setRelatedBlogs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Categories for sidebar - updated to match JobBlog
  const categories = [
    { name: 'Recruitment News' },
    { name: 'Job Reviews' },
    { name: 'Job Tools' },
    { name: 'Full Time Job' },
    { name: 'Work From Home' },
    { name: 'Job Tips' }
  ];

  // Popular tags
  const popularTags = [
    'Figma', 'Adobe XD', 'PSD', 'Dribbble', 'Behance', 'Dark',
    'Design', 'Perquisites', 'Work', 'Remote', 'Career', 'Tips'
  ];

  useEffect(() => {
    // Check if blog data was passed via navigation state
    if (location.state && location.state.blogData) {
      setBlogData(location.state.blogData);
      setRelatedBlogs(location.state.relatedBlogs || []);
      setLoading(false);
    } else if (id) {
      // If no state data, fetch from API using the ID
      fetchBlogById(id);
    } else {
      setError("No blog data available");
      setLoading(false);
    }
    
    // Scroll to top when component mounts
    window.scrollTo(0, 0);
  }, [location.state, id]);

  // This effect specifically updates the document title when blogData changes
  // useEffect(() => {
  //   // Force document title update when blog data is loaded
  //   if (blogData && blogData.title) {
  //     document.title = `Job Page | ${blogData.title}`;
  //   } else if (loading) {
  //     document.title = 'Job Page | Loading Blog';
  //   } else if (error) {
  //     document.title = 'Job Page | Blog Not Found';
  //   }
  // }, [blogData, loading, error]);

  const fetchBlogById = async (blogId) => {
    try {
      setLoading(true);
      const response = await ApiService.blogs.getById(blogId);
      
      const blog = response.data;
      const formattedBlog = {
        id: blog.blog_id,
        title: blog.blog_title,
        author: blog.author || 'Admin',
        date: blog.posted_date ? new Date(blog.posted_date).toLocaleDateString('en-US', {
          day: '2-digit',
          month: 'long',
          year: 'numeric'
        }) : 'No date',
        timeAgo: blog.posted_date ? calculateTimeAgo(new Date(blog.posted_date)) : 'Unknown',
        image: blog.image_url || banner, // Use uploaded image or fallback to banner
        excerpt: blog.blog_description || '',
        content: blog.blog_content || blog.blog_description || '',
        tags: blog.tags ? blog.tags.split(',') : ['General'],
        views: blog.views || 0,
        posted_date: blog.posted_date,
        image_url: blog.image_url // Keep original image_url for reference
      };
      
      setBlogData(formattedBlog);
      
      // Fetch related blogs
      const allBlogsResponse = await ApiService.blogs.getAll();
      const related = allBlogsResponse.data
        .filter(b => b.blog_id !== parseInt(blogId))
        .sort((a, b) => new Date(b.posted_date) - new Date(a.posted_date))
        .slice(0, 5)
        .map(b => ({
          id: b.blog_id,
          title: b.blog_title,
          image: b.image_url || banner, // Use uploaded image or fallback to banner
          image_url: b.image_url,
          date: b.posted_date ? new Date(b.posted_date).toLocaleDateString('en-US', {
            day: '2-digit',
            month: 'long',
            year: 'numeric'
          }) : 'No date',
          posted_date: b.posted_date
        }));
      
      setRelatedBlogs(related);
      setError(null);
    } catch (err) {
      console.error("Error fetching blog:", err);
      setError("Failed to load blog article");
    } finally {
      setLoading(false);
    }
  };

  const calculateTimeAgo = (date) => {
    const now = new Date();
    const diffInMs = now - date;
    const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

    if (diffInHours < 1) {
      const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
      return diffInMinutes <= 1 ? '1 minute ago' : `${diffInMinutes} minutes ago`;
    } else if (diffInHours < 24) {
      return diffInHours === 1 ? '1 hour ago' : `${diffInHours} hours ago`;
    } else if (diffInDays === 1) {
      return '1 day ago';
    } else if (diffInDays < 7) {
      return `${diffInDays} days ago`;
    } else {
      const diffInWeeks = Math.floor(diffInDays / 7);
      return diffInWeeks === 1 ? '1 week ago' : `${diffInWeeks} weeks ago`;
    }
  };

  const handleRelatedBlogClick = (relatedBlog) => {
    navigate(`/blogs/${relatedBlog.id}`);
  };

  const formatContent = (content) => {
    if (!content) return '';
    
    // Split content into paragraphs
    const paragraphs = content.split('\n').filter(p => p.trim());
    
    return paragraphs.map((paragraph, index) => {
      if (index === 0) {
        // First paragraph with drop cap
        return (
          <p key={index} className="lead-paragraph">
            <span className="drop-cap">{paragraph.charAt(0)}</span>
            {paragraph.slice(1)}
          </p>
        );
      }
      return <p key={index}>{paragraph}</p>;
    });
  };

  if (loading) {
    return (
      <>
        <Navbar />
        <div className="blog-container">
          <PageHelmet title="Loading Blog" />
          <div style={{ 
            display: 'flex', 
            justifyContent: 'center', 
            alignItems: 'center', 
            height: '400px',
            flexDirection: 'column',
            gap: '1rem'
          }}>
            <div>Loading article...</div>
          </div>
        </div>
      </>
    );
  }

  if (error || !blogData) {
    return (
      <>
        <Navbar />
        <div className="blog-container">
          <PageHelmet title="Blog Not Found" />
          <div style={{ 
            textAlign: 'center', 
            padding: '3rem',
            color: '#666'
          }}>
            <h2>Article Not Found</h2>
            <p>{error || "The requested blog article could not be found."}</p>
            <button 
              onClick={() => navigate('/blogs')}
              style={{
                padding: '10px 20px',
                backgroundColor: '#007bff',
                color: 'white',
                border: 'none',
                borderRadius: '5px',
                cursor: 'pointer',
                marginTop: '1rem'
              }}
            >
              Back to Blog
            </button>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <Navbar />
      <div className="blog-container">
        <PageHelmet 
          title={blogData.title} 
          description={formatContent(blogData.excerpt || blogData.content)?.slice(0, 150)}
        />
        <div className="article-header">
          <h1>{blogData.title}</h1>
          <div className="author-info">
            <span className="post-date">{blogData.date}</span>
            <div className="post-stats">
              {blogData.views > 0 && (
                <span className="view-count">{blogData.views} views</span>
              )}
            </div>
          </div>
        </div>

        <div className="article-content">
          <div className="main-image">
            <img src={blogData.image} alt={blogData.title} className="author-avatar"/>
          </div>        
          <div className="article-text">
            {formatContent(blogData.content)}
            {blogData.content && blogData.content.length > 500 && (
              <div className="article-images">
              </div>
            )}
            {/*
            <blockquote className="article-quote">
              <p>Stay updated with the latest trends and insights in the job market. Your career growth is our priority.</p>
              <div className="quote-source">
                <span className="twitter-icon"></span>
                <span className="share-text">SHARE</span>
              </div>
            </blockquote>
            */}
            <div className="article-tags">
              {blogData.tags.map(tag => (
                <span key={tag} className="tag">{tag}</span>
              ))}
            </div>
          </div>

          <div className="related-posts-section">
            <h2 className="section-title">Related posts</h2>
            <div className="related-posts">
              {relatedBlogs.length > 0 ? (
                relatedBlogs.map(relatedBlog => (
                  <div 
                    key={relatedBlog.id} 
                    className="related-post"
                    onClick={() => handleRelatedBlogClick(relatedBlog)}
                    style={{ cursor: 'pointer' }}
                  >
                    <img src={relatedBlog.image} alt={relatedBlog.title} className="related-post-image" />
                    <h4 className="related-post-title">{relatedBlog.title}</h4>
                  </div>
                ))
              ) : (
                <>
                </>
              )}
            </div>
          </div>
        </div>

        <div className="sidebar">
          <div className="latest-news-section sidebar-section">
            <h3 className="sidebar-title">Latest news</h3>
            <div className="news-items">
              {relatedBlogs.length > 0 ? (
                relatedBlogs.map(blog => (
                  <div 
                    key={blog.id} 
                    className="news-item"
                    onClick={() => handleRelatedBlogClick(blog)}
                    style={{ cursor: 'pointer' }}
                  >
                    <img src={blog.image} alt={blog.title} className="news-image" />
                    <div className="news-content">
                      <h4 className="news-title">{blog.title}</h4>
                      <span className="post-date">{blog.date}</span>
                    </div>
                  </div>
                ))
              ) : (
                <>
                
                </>
              )}
            </div>
          </div>



          
          <div className="sidebar-ad1">
            <div className="ad-content1">
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default BlogArticle;