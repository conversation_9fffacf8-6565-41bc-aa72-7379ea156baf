import React, { useState } from 'react';
import '../css/FAQ.css';
import { Link } from 'react-router-dom';
import PageHelmet from './PageHelmet';

const FAQ = () => {
  const [expandedItems, setExpandedItems] = useState({});

  const toggleItem = (id) => {
    setExpandedItems(prev => ({
      ...prev,
      [id]: !prev[id]
    }));
  };

  // FAQ data for the left column
  const faqsLeft = [
    {
      id: 1,
      question: 'What types of jobs does jobpage.lk list?',
      answer: 'Jobpage.lk specializes in Government Jobs, Private Sector Jobs, Foreign Jobs, and Internship opportunities across Sri Lanka. We carefully curate listings to provide the most relevant opportunities for job seekers.'
    },
    {
      id: 2,
      question: 'How can I apply for jobs through jobpage.lk?',
      answer: 'You can apply directly through our platform by clicking "Apply Now" on any job listing. Some employers may redirect you to their application system. Make sure your profile and resume are complete to streamline the application process.'
    },
    {
      id: 3,
      question: 'Is there a fee to search or apply for jobs?',
      answer: 'No, job searching and applying is completely free for candidates. Our revenue comes from employers who pay to list their vacancies on our platform.'
    },
    {
      id: 4,
      question: 'How does the resume submission feature work?',
      answer: 'By submitting your resume to our database, our system and recruiters can match your qualifications with suitable job openings. We may also proactively contact you when we find matches that fit your profile.'
    },
    
  ];

  // FAQ data for the right column
  const faqsRight = [
    {
      id: 6,
      question: 'How can I publish a job vacancy on jobpage.lk?',
      answer: 'Employers can contact our sales team via the "Post a Job" link <NAME_EMAIL>. We offer premium visibility options for Government, Private, and Foreign job postings.'
    },
    {
      id: 7,
      question: 'What makes jobpage.lk different from other job sites?',
      answer: 'We specialize in the Sri Lankan job market with verified listings and dedicated support. Our platform offers personalized matching and direct recruiter access not found on generic job boards.'
    },
    {
      id: 8,
      question: 'How do you help candidates find the right job?',
      answer: 'Our algorithm analyzes your profile and preferences to suggest suitable positions. Registered users get personalized recommendations via email and dashboard notifications.'
    },
    {
      id: 9,
      question: 'What support do you offer foreign job seekers?',
      answer: 'We verify all foreign job listings and provide resources about work visas and overseas employment regulations. Our team assists with understanding international employer requirements.'
    },
    
  ];

  return (
    <div className="faq-page">
      <PageHelmet 
        title="FAQ" 
        description="Frequently asked questions about using Job Page for job seekers and employers."
      />
      {/* Header Section */}
      <div className="faq-header">
        <h1>Frequently Asked Questions</h1>
        <p>Find answers about using jobpage.lk - Sri Lanka's premier job platform for Government, Private, and Foreign employment opportunities.</p>
      </div>

      {/* Image Section */}
      <div className="faq-images">
        <div className="faq-image-main">
          <img 
            src="https://images.unsplash.com/photo-1521791136064-7986c2920216?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1169&q=80" 
            alt="Sri Lankan professionals at work"
          />
        </div>
        <div className="faq-image-side">
          <div>
            <img 
              src="https://images.unsplash.com/photo-1454165804606-c3d57bc86b40?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80" 
              alt="Job interview in progress" 
            />
          </div>
          <div>
            <img 
              src="https://images.unsplash.com/photo-1572025442646-866d16c84a54?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80" 
              alt="Team collaboration" 
            />
          </div>
        </div>
      </div>

      {/* FAQ Content Section */}
      <div className="faq-content">
        <div className="faq-title">
          <h2>Your Questions Answered</h2>
          <p>Everything you need to know about finding jobs or hiring through Sri Lanka's most trusted job platform.</p>
        </div>

        <div className="faq-columns">
          <div className="faq-column">
            {faqsLeft.map(faq => (
              <div 
                key={faq.id} 
                className={`faq-item ${expandedItems[faq.id] ? 'expanded' : ''}`}
              >
                <div className="faq-question" onClick={() => toggleItem(faq.id)}>
                  <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    {expandedItems[faq.id] ? (
                      <path d="M19 13H5V11H19V13Z" fill="currentColor" />
                    ) : (
                      <path d="M19 13H13V19H11V13H5V11H11V5H13V11H19V13Z" fill="currentColor" />
                    )}
                  </svg>
                  <h3>{faq.question}</h3>
                </div>
                {expandedItems[faq.id] && (
                  <div className="faq-answer">
                    <p>{faq.answer}</p>
                  </div>
                )}
              </div>
            ))}
          </div>

          <div className="faq-column">
            {faqsRight.map(faq => (
              <div 
                key={faq.id} 
                className={`faq-item ${expandedItems[faq.id] ? 'expanded' : ''}`}
              >
                <div className="faq-question" onClick={() => toggleItem(faq.id)}>
                  <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    {expandedItems[faq.id] ? (
                      <path d="M19 13H5V11H19V13Z" fill="currentColor" />
                    ) : (
                      <path d="M19 13H13V19H11V13H5V11H11V5H13V11H19V13Z" fill="currentColor" />
                    )}
                  </svg>
                  <h3>{faq.question}</h3>
                </div>
                {expandedItems[faq.id] && (
                  <div className="faq-answer">
                    <p>{faq.answer}</p>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Commitment Section */}
      <div className="commitment-section">
        <div className="commitment-content">
          <div className="commitment-text">
            <span className="subtitle">Our Promise</span>
            <h2>Connecting Sri Lankan Talent With Opportunities</h2>
            <p>Jobpage.lk is committed to bridging the gap between employers and job seekers across all sectors in Sri Lanka. We understand the local job market's unique needs and challenges.</p>
            <p>Our platform combines advanced technology with personalized service to deliver results. Whether you're a fresh graduate seeking your first opportunity or an established company looking for top talent, we're here to help.</p>
            <Link to="/about">
              <button className="learn-more-btn">Learn About Our Mission</button>
            </Link>
          </div>
          <div className="commitment-image">
            <img src="https://images.unsplash.com/photo-1522071820081-009f0129c71c?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80" alt="Diverse Sri Lankan professionals" />
          </div>
        </div>
      </div>

      {/* Call To Action Section */}
      {/* <div className="cta-section">
        <div className="cta-content">
          <h2>Need Personalized Assistance?</h2>
          <p>Our Colombo-based support team understands Sri Lanka's job market intricacies. We're here to help you navigate your career path or hiring needs.</p>
          <div className="cta-buttons">
            <Link to="/contact">
              <button className="contact-btn">Contact Our Team</button>
            </Link>
            <Link to="/resources">
              <button className="learn-more-link">Job Seeker Resources</button>
            </Link>
          </div>
        </div>
      </div> */}
    </div>
  );
};

export default FAQ;