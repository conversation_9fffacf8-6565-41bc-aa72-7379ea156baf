import api from './api';

/**
 * Service for handling all API calls in the application
 * Uses the centralized api instance with environment variable configuration
 */
export const ApiService = {
  // CV related API calls
  cvs: {
    getAll: () => api.get('/cvs'),
    getDashboard: () => api.get('/cvs/dashboard'),
    getById: (id) => api.get(`/cvs/${id}`),
    delete: (nationalId) => api.delete(`/cvs/${nationalId}`),
    submit: (formData) => api.post('/cvs/submit', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }),
  },
  
  // Jobs related API calls
  jobs: {
    getAll: (params = {}) => api.get('/jobs', { params }),
    getById: (id) => api.get(`/jobs/${id}`),
    getHotJobs: () => api.get('/jobs/hot'),
    getDashboard: () => api.get('/jobs/dashboard'),
    create: (formData) => api.post('/jobs', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }),
    update: (id, formData) => api.put(`/jobs/${id}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }),
    delete: (id) => api.delete(`/jobs/${id}`),
    recordView: (jobId) => api.post(`/jobs/${jobId}/view`),
    getByCategory: (category, limit) => api.get('/jobs', { 
      params: { category, limit } 
    }),
    // Get jobs by company name
    getByCompany: (companyName) => api.get('/jobs', {
      params: { company: companyName }
    }),
  },
  
  // Blogs related API calls
  blogs: {
    getAll: () => api.get('/blogs'),
    getById: (id) => api.get(`/blogs/${id}`),
    create: (formData) => api.post('/blogs', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }),
    update: (id, formData) => api.put(`/blogs/${id}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }),
    delete: (id) => api.delete(`/blogs/${id}`),
    incrementViews: (id) => api.post(`/blogs/${id}/view`),
  },
  
  // Companies related API calls
  companies: {
    getAll: () => api.get('/companies'),
    getDashboard: () => api.get('/companies/dashboard'),
    getById: (id) => api.get(`/companies/${id}`),
    create: (formData) => api.post('/companies', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }),
    update: (id, formData) => api.put(`/companies/${id}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }),
    delete: (id) => api.delete(`/companies/${id}`),
    // Get jobs by company name - delegates to jobs API
    getJobsByCompany: (companyName) => api.get('/jobs', {
      params: { company: companyName }
    }),
  },

  // Admin users related API calls
  adminUsers: {
    login: (credentials) => api.post('/admin-users/login', credentials),
    getCurrentUser: () => api.get('/admin-users/me'),
    getAll: () => api.get('/admin-users'),
    getById: (id) => api.get(`/admin-users/${id}`),
    create: (userData) => api.post('/admin-users', userData),
    update: (id, userData) => api.put(`/admin-users/${id}`, userData),
    delete: (id) => api.delete(`/admin-users/${id}`),
  }
};

export default ApiService;