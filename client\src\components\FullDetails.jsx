import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, Link } from 'react-router-dom';
import ApiService from '../services/apiService';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faArrowLeft, 
  faBriefcase, 
  faMapMarkerAlt, 
  faDollarSign, 
  faClock, 
  faCalendarAlt
} from '@fortawesome/free-solid-svg-icons';
import {
  faFacebookF,
  faWhatsapp,
  faInstagram,
  faTiktok
} from '@fortawesome/free-brands-svg-icons';
import '../css/FullDetails.css';
import jobdetails from "../assets/jobdetails.png";
import JobCard from './JobCard';
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";

const FullDetails = () => {
  const { jobId } = useParams();
  const [job, setJob] = useState(null);
  const [relatedJobs, setRelatedJobs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);

  // Utility functions
  const formatSalary = (job) => {
    if (job.salary_from && job.salary_to) {
      return `Rs. ${job.salary_from.toLocaleString()} - Rs. ${job.salary_to.toLocaleString()}`;
    } else if (job.salary_from) {
      return `Rs. ${job.salary_from.toLocaleString()}+`;
    } else {
      return 'Negotiable';
    }
  };

  const formatJobType = (job) => {
    return job.job_type || 'Full Time';
  };

  const formatDatePosted = (dateString) => {
    if (!dateString) return 'Recently';
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now - date);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 1) return '1 day ago';
    if (diffDays < 7) return `${diffDays} days ago`;
    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} weeks ago`;
    return `${Math.ceil(diffDays / 30)} months ago`;
  };

  const formatExpirationDate = (dateString) => {
    if (!dateString) return 'Open';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Slider settings for mobile carousel
  const sliderSettings = {
    dots: true,
    infinite: false,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
    arrows: false,
    swipeToSlide: true,
    adaptiveHeight: true
  };

  useEffect(() => {
    window.scrollTo({ top: 0, behavior: 'instant' });

    // Add window resize listener to detect mobile view
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 768);
    };
    
    window.addEventListener('resize', handleResize);
    
    const fetchJobDetails = async () => {
      try {
        setLoading(true);
        const response = await ApiService.jobs.getById(jobId);
        setJob(response.data);
        setError(null);
        // Fetch related jobs based on the same job type (main_topics)
        if (response.data.main_topics) {
          await fetchRelatedJobs(response.data.main_topics, response.data.id);
        }
      } catch (err) {
        setError("Failed to load job details");
      } finally {
        setLoading(false);
      }
    };

    const fetchRelatedJobs = async (jobType, currentJobId) => {
      try {
        const response = await ApiService.jobs.getByCategory(jobType, 6);
        // Filter out the current job and limit to 3 jobs
        // Check both 'id' and 'job_id' properties to handle different data formats
        const filtered = response.data.filter(job => {
          const jobId = job.id || job.job_id;
          const isCurrentJob = String(jobId) === String(currentJobId); // Convert both to strings for comparison
          return !isCurrentJob;
        }).slice(0, 3);
        
        setRelatedJobs(filtered);
      } catch (err) {
        // Error fetching related jobs
        setRelatedJobs([]);
      }
    };

    if (jobId) {
      fetchJobDetails();
    }
    
    // Clean up event listener
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [jobId]);

  if (loading) {
    return (
      <div className="job-single-container">
        <div className="loading-state">
          <h2>Loading job details...</h2>
        </div>
      </div>
    );
  }

  if (error || !job) {
    return (
      <div className="job-single-container">
        <div className="error-state">
          <h2>Error: {error || "Job not found"}</h2>
          <Link to="/">Return to homepage</Link>
        </div>
      </div>
    );
  }

  return (
    <div className="job-single-container">
      {/* Header Background */}
      <div className="job-header-bg">
        {/* Breadcrumb */}
        <div className="container">
          <div className="breadcrumb-nav">
            <Link to="/">Home</Link> / <Link to={`/job/${jobId}`}>Job Details</Link> / <span>Full Details</span>
          </div>
          
          {/* Job Title */}
          <div className="job-title-header">
            <h1>{job.job_title || "Job Details"}</h1>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container">
        <div className="job-content-wrapper">
          <div className="job-main-content">
            <Link to={`/job/${jobId}`} className="back-button">
              <FontAwesomeIcon icon={faArrowLeft} />
              Back to Job Details
            </Link>
            
            {job.main_topics && (
              <div className="job-topic">
                <h2>{job.main_topics}</h2>
              </div>
            )}
            
            {job.job_description && (
              <div className="job-description">
                <div dangerouslySetInnerHTML={{ __html: job.job_description }} />
              </div>
            )}

            {/* Related Jobs */}
            <div className="related-jobs-section">
              <div className="section-header">
                <h2>Related Jobs</h2>
                <div className="section-line"></div>
                <button className="btn-explore-more">Explore more</button>
              </div>
              
              {/* Related Job Cards - Grid for desktop, Slider for mobile */}
              {relatedJobs.length > 0 ? (
                isMobile ? (
                  <div className="related-jobs-slider-container">
                    <Slider {...sliderSettings} className="related-jobs-slider">
                      {relatedJobs.map((relatedJob) => {
                        const jobId = relatedJob.id || relatedJob.job_id;
                        return (
                          <div key={jobId} className="slider-job-card-wrapper">
                            <JobCard 
                              id={jobId}
                              hot={false}
                              image={relatedJob.job_post_image || jobdetails}
                              logo={relatedJob.company_logo || "https://via.placeholder.com/40"}
                              company={relatedJob.company_name}
                              position={relatedJob.job_title}
                              location={relatedJob.main_topics || "Sri Lanka"}
                              workTime={relatedJob.job_type || "Full Time"}
                              salary={formatSalary(relatedJob)}
                              postedTime={formatDatePosted(relatedJob.start_date)}
                            />
                          </div>
                        );
                      })}
                    </Slider>
                  </div>
                ) : (
                  <div className="related-jobs-grid">
                    {relatedJobs.map((relatedJob) => {
                      const jobId = relatedJob.id || relatedJob.job_id;
                      return (
                        <JobCard 
                          key={jobId}
                          id={jobId}
                          hot={false}
                          image={relatedJob.job_post_image || jobdetails}
                          logo={relatedJob.company_logo || "https://via.placeholder.com/40"}
                          company={relatedJob.company_name}
                          position={relatedJob.job_title}
                          location={relatedJob.main_topics || "Sri Lanka"}
                          workTime={relatedJob.job_type || "Full Time"}
                          salary={formatSalary(relatedJob)}
                          postedTime={formatDatePosted(relatedJob.start_date)}
                        />
                      );
                    })}
                  </div>
                )
              ) : (
                <div className="no-related-jobs">
                  <p>No related jobs found in this category.</p>
                </div>
              )}
            </div>
          </div>

          {/* Right Sidebar */}
          <div className="job-sidebar">
            {/* Job Details Card */}
            <div className="sidebar-card-card">
              <div className="card-content">
                {/* Job Type */}
                <div className="detail-item">
                  <div className="detail-icon">
                    <FontAwesomeIcon icon={faBriefcase} />
                  </div>
                  <div className="detail-content">
                    <span className="detail-label">Job Type</span>
                    <span className="detail-value">{formatJobType(job)}</span>
                  </div>
                </div>

                {/* Location */}
                <div className="detail-item">
                  <div className="detail-icon">
                    <FontAwesomeIcon icon={faMapMarkerAlt} />
                  </div>
                  <div className="detail-content">
                    <span className="detail-label">Location</span>
                    <span className="detail-value">{job.company_name ? `${job.company_name}, Sri Lanka` : 'Sri Lanka'}</span>
                  </div>
                </div>

                {/* Salary */}
                <div className="detail-item">
                  <div className="detail-icon">
                    <FontAwesomeIcon icon={faDollarSign} />
                  </div>
                  <div className="detail-content">
                    <span className="detail-label">Salary</span>
                    <span className="detail-value">{formatSalary(job)}</span>
                  </div>
                </div>

                {/* Date Posted */}
                <div className="detail-item">
                  <div className="detail-icon">
                    <FontAwesomeIcon icon={faClock} />
                  </div>
                  <div className="detail-content">
                    <span className="detail-label">Date posted</span>
                    <span className="detail-value">{formatDatePosted(job.start_date)}</span>
                  </div>
                </div>

                {/* Expiration Date */}
                <div className="detail-item">
                  <div className="detail-icon">
                    <FontAwesomeIcon icon={faCalendarAlt} />
                  </div>
                  <div className="detail-content">
                    <span className="detail-label">Expiration date</span>
                    <span className="detail-value">{formatExpirationDate(job.end_date)}</span>
                  </div>
                </div>
              </div>
              <div className="card-divider"></div>
            </div>

            {/* Follow Us Section */}
            <div className="follow-section">
              <h3 className="section-title">FOLLOW US</h3>
              <div className="section-line"></div>
              <div className="follow-social-row">
                <a
                  href="#" // TODO: Add your Facebook page link
                  className="follow-social-icon facebook"
                  target="_blank"
                  rel="noopener noreferrer"
                  aria-label="Facebook"
                >
                  <FontAwesomeIcon icon={faFacebookF} />
                </a>
                <a
                  href="#" // TODO: Add your WhatsApp link
                  className="follow-social-icon whatsapp"
                  target="_blank"
                  rel="noopener noreferrer"
                  aria-label="WhatsApp"
                >
                  <FontAwesomeIcon icon={faWhatsapp} />
                </a>
                <a
                  href="#" // TODO: Add your Instagram link
                  className="follow-social-icon instagram"
                  target="_blank"
                  rel="noopener noreferrer"
                  aria-label="Instagram"
                >
                  <FontAwesomeIcon icon={faInstagram} />
                </a>
                <a
                  href="#" // TODO: Add your TikTok link
                  className="follow-social-icon tiktok"
                  target="_blank"
                  rel="noopener noreferrer"
                  aria-label="TikTok"
                >
                  <FontAwesomeIcon icon={faTiktok} />
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FullDetails;