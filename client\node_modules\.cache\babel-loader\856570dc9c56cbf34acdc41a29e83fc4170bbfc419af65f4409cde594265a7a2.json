{"ast": null, "code": "var _jsxFileName = \"D:\\\\Thirmaa Office\\\\job_page\\\\client\\\\src\\\\App.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport './App.css';\nimport { BrowserRouter as Router, Route, Routes, useLocation } from 'react-router-dom';\nimport { useState, useEffect } from 'react';\nimport Banner from './components/Banner';\nimport Footer from './components/Footer';\nimport Navbar from './components/Navbar';\nimport SearchArea from './components/SearchArea';\nimport JobDetails from './components/JobDetails';\nimport FullDetails from './components/FullDetails';\nimport JobDetailsDis from './components/JobDetailsDis';\nimport ContactUs from './components/ContactUs';\nimport AdminLogin from './components/AdminLogin';\nimport JobBoard from './components/JobBoard';\nimport AdminPanel from './components/Admin/AdminPanel';\nimport ProtectedRoute from './components/ProtectedRoute';\nimport HomePage from './components/HomePage';\nimport Companies from './components/Companies';\nimport FAQ from './components/FAQ';\nimport AboutUs from './components/AboutUs';\nimport JobBlog from './components/JobBlog';\nimport BlogArticle from './components/BlogArticle';\nimport Cv from './components/Cv';\nimport CompanyDetails from './components/CompanyDetails';\nimport { LocationProvider } from './contexts/LocationContext';\nimport { JobFilterProvider } from './contexts/JobFilterContext';\nimport PrivacyPolicy from 'components/PrivacyPolicy';\nimport TermsAndConditions from 'components/TermsAndConditions';\nimport NotFound from './components/NotFound';\n\n// ScrollToTop component - scrolls to top when location changes\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction ScrollToTop() {\n  _s();\n  const {\n    pathname\n  } = useLocation();\n  useEffect(() => {\n    window.scrollTo(0, 0);\n  }, [pathname]);\n  return null;\n}\n_s(ScrollToTop, \"+8VPq4+XDMjo/kjL3WLkbwU2Amg=\", false, function () {\n  return [useLocation];\n});\n_c = ScrollToTop;\nfunction App() {\n  _s2();\n  // State management\n  const [jobs, setJobs] = useState([]);\n  const [isCvModalOpen, setIsCvModalOpen] = useState(false);\n\n  // Event handlers\n  const handleJobPost = newJob => {\n    setJobs([newJob, ...jobs]);\n    alert(\"Job posted successfully!\");\n  };\n  const handleApplyNowClick = () => {\n    setIsCvModalOpen(true);\n  };\n  const handleCloseCvModal = () => {\n    setIsCvModalOpen(false);\n  };\n\n  // Main layout wrapper component\n  const MainLayout = ({\n    children\n  }) => /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Navbar, {\n      onApplyNowClick: handleApplyNowClick\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"main-content-padded\",\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"App\",\n    children: /*#__PURE__*/_jsxDEV(Router, {\n      children: [/*#__PURE__*/_jsxDEV(ScrollToTop, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Routes, {\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/company/:companyId\",\n          element: /*#__PURE__*/_jsxDEV(MainLayout, {\n            children: /*#__PURE__*/_jsxDEV(CompanyDetails, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 79,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/\",\n          element: /*#__PURE__*/_jsxDEV(MainLayout, {\n            children: /*#__PURE__*/_jsxDEV(LocationProvider, {\n              children: /*#__PURE__*/_jsxDEV(JobFilterProvider, {\n                children: /*#__PURE__*/_jsxDEV(HomePage, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 87,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 86,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/job/:jobId\",\n          element: /*#__PURE__*/_jsxDEV(MainLayout, {\n            children: [/*#__PURE__*/_jsxDEV(JobDetails, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 95,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Banner, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 96,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/job/:jobId\",\n          element: /*#__PURE__*/_jsxDEV(MainLayout, {\n            children: [/*#__PURE__*/_jsxDEV(JobDetails, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Banner, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/job/:jobId/full-details\",\n          element: /*#__PURE__*/_jsxDEV(MainLayout, {\n            children: /*#__PURE__*/_jsxDEV(FullDetails, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/company\",\n          element: /*#__PURE__*/_jsxDEV(MainLayout, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"content-spacing\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Companies, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/job-dis\",\n          element: /*#__PURE__*/_jsxDEV(MainLayout, {\n            children: [/*#__PURE__*/_jsxDEV(JobDetailsDis, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Banner, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/contact\",\n          element: /*#__PURE__*/_jsxDEV(MainLayout, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"content-spacing\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ContactUs, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 11\n        }, this), \"          \", /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/browse\",\n          element: /*#__PURE__*/_jsxDEV(MainLayout, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"content-spacing\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(LocationProvider, {\n              children: /*#__PURE__*/_jsxDEV(JobFilterProvider, {\n                children: [/*#__PURE__*/_jsxDEV(SearchArea, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 137,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Banner, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 138,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(JobBoard, {\n                  jobs: jobs\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 139,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 136,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/faq\",\n          element: /*#__PURE__*/_jsxDEV(MainLayout, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"content-spacing\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FAQ, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/about\",\n          element: /*#__PURE__*/_jsxDEV(MainLayout, {\n            children: /*#__PURE__*/_jsxDEV(AboutUs, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/blogs\",\n          element: /*#__PURE__*/_jsxDEV(MainLayout, {\n            children: /*#__PURE__*/_jsxDEV(JobBlog, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/blogs/:id\",\n          element: /*#__PURE__*/_jsxDEV(MainLayout, {\n            children: /*#__PURE__*/_jsxDEV(BlogArticle, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/job-Article\",\n          element: /*#__PURE__*/_jsxDEV(MainLayout, {\n            children: /*#__PURE__*/_jsxDEV(BlogArticle, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/privacy\",\n          element: /*#__PURE__*/_jsxDEV(MainLayout, {\n            children: /*#__PURE__*/_jsxDEV(PrivacyPolicy, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/terms\",\n          element: /*#__PURE__*/_jsxDEV(MainLayout, {\n            children: /*#__PURE__*/_jsxDEV(TermsAndConditions, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/jp-admin\",\n          element: /*#__PURE__*/_jsxDEV(AdminLogin, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 44\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/dashboard\",\n          element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n            children: /*#__PURE__*/_jsxDEV(AdminPanel, {\n              onJobPost: handleJobPost\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Cv, {\n        isOpen: isCvModalOpen,\n        onClose: handleCloseCvModal\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 73,\n    columnNumber: 5\n  }, this);\n}\n_s2(App, \"oQe6IduRPHJXFCMkC4lkJgenlXs=\");\n_c2 = App;\nexport default App;\nvar _c, _c2;\n$RefreshReg$(_c, \"ScrollToTop\");\n$RefreshReg$(_c2, \"App\");", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Route", "Routes", "useLocation", "useState", "useEffect", "Banner", "Footer", "<PERSON><PERSON><PERSON>", "SearchArea", "JobDetails", "FullDetails", "JobDetailsDis", "ContactUs", "AdminLogin", "JobBoard", "AdminPanel", "ProtectedRoute", "HomePage", "Companies", "FAQ", "AboutUs", "JobBlog", "BlogArticle", "Cv", "CompanyDetails", "LocationProvider", "Job<PERSON>ilter<PERSON><PERSON><PERSON>", "PrivacyPolicy", "TermsAndConditions", "NotFound", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ScrollToTop", "_s", "pathname", "window", "scrollTo", "_c", "App", "_s2", "jobs", "setJobs", "isCvModalOpen", "setIsCvModalOpen", "handleJobPost", "new<PERSON>ob", "alert", "handleApplyNowClick", "handleCloseCvModal", "MainLayout", "children", "onApplyNowClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "path", "element", "onJobPost", "isOpen", "onClose", "_c2", "$RefreshReg$"], "sources": ["D:/Thirmaa Office/job_page/client/src/App.js"], "sourcesContent": ["import './App.css';\r\nimport { BrowserRouter as Router, Route, Routes, useLocation } from 'react-router-dom';\r\nimport { useState, useEffect } from 'react';\r\nimport Banner from './components/Banner';\r\nimport Footer from './components/Footer';\r\nimport Navbar from './components/Navbar';\r\nimport SearchArea from './components/SearchArea';\r\nimport JobDetails from './components/JobDetails';\r\nimport FullDetails from './components/FullDetails';\r\nimport JobDetailsDis from './components/JobDetailsDis';\r\nimport ContactUs from './components/ContactUs';\r\nimport AdminLogin from './components/AdminLogin';\r\nimport JobBoard from './components/JobBoard';\r\nimport AdminPanel from './components/Admin/AdminPanel';\r\nimport ProtectedRoute from './components/ProtectedRoute';\r\nimport HomePage from './components/HomePage';\r\nimport Companies from './components/Companies';\r\nimport FAQ from './components/FAQ';\r\n\r\nimport AboutUs from './components/AboutUs';\r\nimport JobBlog from './components/JobBlog';\r\nimport BlogArticle from './components/BlogArticle';\r\nimport Cv from './components/Cv';\r\nimport CompanyDetails from './components/CompanyDetails';\r\nimport { LocationProvider } from './contexts/LocationContext';\r\nimport { JobFilterProvider } from './contexts/JobFilterContext';\r\nimport PrivacyPolicy from 'components/PrivacyPolicy';\r\nimport TermsAndConditions from 'components/TermsAndConditions';\r\nimport NotFound from './components/NotFound';\r\n\r\n// ScrollToTop component - scrolls to top when location changes\r\nfunction ScrollToTop() {\r\n  const { pathname } = useLocation();\r\n  \r\n  useEffect(() => {\r\n    window.scrollTo(0, 0);\r\n  }, [pathname]);\r\n  \r\n  return null;\r\n}\r\n\r\nfunction App() {\r\n  // State management\r\n  const [jobs, setJobs] = useState([]);\r\n  const [isCvModalOpen, setIsCvModalOpen] = useState(false);\r\n\r\n  // Event handlers\r\n  const handleJobPost = (newJob) => {\r\n    setJobs([newJob, ...jobs]);\r\n    alert(\"Job posted successfully!\");\r\n  };\r\n\r\n  const handleApplyNowClick = () => {\r\n    setIsCvModalOpen(true);\r\n  };\r\n\r\n  const handleCloseCvModal = () => {\r\n    setIsCvModalOpen(false);\r\n  };\r\n\r\n  // Main layout wrapper component\r\n  const MainLayout = ({ children }) => (\r\n    <>\r\n      <Navbar onApplyNowClick={handleApplyNowClick} />\r\n      <div className=\"main-content-padded\">\r\n        {children}\r\n      </div>\r\n      <Footer />\r\n    </>\r\n  );\r\n\r\n  return (\r\n    <div className=\"App\">\r\n      <Router>\r\n        <ScrollToTop />\r\n        <Routes>\r\n          <Route path=\"/company/:companyId\" element={\r\n            <MainLayout>\r\n              <CompanyDetails />\r\n            </MainLayout>\r\n          } />\r\n          {/* Public Pages */}\r\n          <Route path=\"/\" element={\r\n            <MainLayout>\r\n              <LocationProvider>\r\n                <JobFilterProvider>\r\n                  <HomePage />\r\n                </JobFilterProvider>\r\n              </LocationProvider>\r\n            </MainLayout>\r\n          } />\r\n\r\n          <Route path=\"/job/:jobId\" element={\r\n            <MainLayout>\r\n              <JobDetails />\r\n              <Banner />\r\n            </MainLayout>\r\n          } />\r\n\r\n          <Route path=\"/job/:jobId\" element={\r\n            <MainLayout>\r\n              <JobDetails />\r\n              <Banner />\r\n            </MainLayout>\r\n          } />\r\n\r\n          <Route path=\"/job/:jobId/full-details\" element={\r\n            <MainLayout>\r\n              <FullDetails />\r\n            </MainLayout>\r\n          } />\r\n\r\n          <Route path=\"/company\" element={\r\n            <MainLayout>\r\n              <div className=\"content-spacing\"></div>\r\n              <Companies />\r\n            </MainLayout>\r\n          } />\r\n\r\n          <Route path=\"/job-dis\" element={\r\n            <MainLayout>\r\n              <JobDetailsDis />\r\n              <Banner />\r\n            </MainLayout>\r\n          } />\r\n\r\n          <Route path=\"/contact\" element={\r\n            <MainLayout>\r\n              <div className=\"content-spacing\"></div>\r\n              <ContactUs />\r\n            </MainLayout>\r\n          } />          <Route path=\"/browse\" element={\r\n            <MainLayout>\r\n              <div className=\"content-spacing\"></div>\r\n              <LocationProvider>\r\n                <JobFilterProvider>\r\n                  <SearchArea />\r\n                  <Banner />\r\n                  <JobBoard jobs={jobs} />\r\n                </JobFilterProvider>\r\n              </LocationProvider>\r\n            </MainLayout>\r\n          } />\r\n\r\n          <Route path=\"/faq\" element={\r\n            <MainLayout>\r\n              <div className=\"content-spacing\"></div>\r\n              <FAQ />\r\n            </MainLayout>\r\n          } />\r\n\r\n          {/* <Route path=\"/job-board\" element={\r\n            <MainLayout>  \r\n              <div className=\"content-spacing\"></div>\r\n              <LocationProvider>\r\n                <JobBoard jobs={jobs} />\r\n              </LocationProvider>\r\n              \r\n              <Banner />\r\n            </MainLayout>\r\n          } /> */}\r\n\r\n          <Route path=\"/about\" element={\r\n            <MainLayout>\r\n              <AboutUs />\r\n            </MainLayout>\r\n          } />\r\n\r\n          <Route path=\"/blogs\" element={\r\n            <MainLayout>\r\n              <JobBlog/>\r\n            </MainLayout>\r\n          } />\r\n\r\n          <Route path=\"/blogs/:id\" element={\r\n            <MainLayout>\r\n              <BlogArticle />\r\n            </MainLayout>\r\n          } />\r\n          \r\n          <Route path=\"/job-Article\" element={\r\n            <MainLayout>\r\n              <BlogArticle/>\r\n            </MainLayout>\r\n          } />\r\n\r\n          <Route path=\"/privacy\" element={\r\n            <MainLayout>\r\n              <PrivacyPolicy/>\r\n            </MainLayout>\r\n          } />\r\n\r\n          <Route path=\"/terms\" element={\r\n            <MainLayout>\r\n              <TermsAndConditions/>\r\n            </MainLayout>\r\n          } />\r\n\r\n          {/* Admin Routes */}\r\n          <Route path=\"/jp-admin\" element={<AdminLogin />} />\r\n          <Route path=\"/dashboard\" element={\r\n            <ProtectedRoute>\r\n              <AdminPanel onJobPost={handleJobPost} />\r\n            </ProtectedRoute>\r\n          } />\r\n        </Routes>\r\n\r\n        {/* CV Modal */}\r\n        <Cv isOpen={isCvModalOpen} onClose={handleCloseCvModal} />\r\n      </Router>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default App;\r\n"], "mappings": ";;;AAAA,OAAO,WAAW;AAClB,SAASA,aAAa,IAAIC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,WAAW,QAAQ,kBAAkB;AACtF,SAASC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,UAAU,MAAM,+BAA+B;AACtD,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,GAAG,MAAM,kBAAkB;AAElC,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,EAAE,MAAM,iBAAiB;AAChC,OAAOC,cAAc,MAAM,6BAA6B;AACxD,SAASC,gBAAgB,QAAQ,4BAA4B;AAC7D,SAASC,iBAAiB,QAAQ,6BAA6B;AAC/D,OAAOC,aAAa,MAAM,0BAA0B;AACpD,OAAOC,kBAAkB,MAAM,+BAA+B;AAC9D,OAAOC,QAAQ,MAAM,uBAAuB;;AAE5C;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,SAASC,WAAWA,CAAA,EAAG;EAAAC,EAAA;EACrB,MAAM;IAAEC;EAAS,CAAC,GAAGlC,WAAW,CAAC,CAAC;EAElCE,SAAS,CAAC,MAAM;IACdiC,MAAM,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;EACvB,CAAC,EAAE,CAACF,QAAQ,CAAC,CAAC;EAEd,OAAO,IAAI;AACb;AAACD,EAAA,CARQD,WAAW;EAAA,QACGhC,WAAW;AAAA;AAAAqC,EAAA,GADzBL,WAAW;AAUpB,SAASM,GAAGA,CAAA,EAAG;EAAAC,GAAA;EACb;EACA,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACyC,aAAa,EAAEC,gBAAgB,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;;EAEzD;EACA,MAAM2C,aAAa,GAAIC,MAAM,IAAK;IAChCJ,OAAO,CAAC,CAACI,MAAM,EAAE,GAAGL,IAAI,CAAC,CAAC;IAC1BM,KAAK,CAAC,0BAA0B,CAAC;EACnC,CAAC;EAED,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IAChCJ,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,MAAMK,kBAAkB,GAAGA,CAAA,KAAM;IAC/BL,gBAAgB,CAAC,KAAK,CAAC;EACzB,CAAC;;EAED;EACA,MAAMM,UAAU,GAAGA,CAAC;IAAEC;EAAS,CAAC,kBAC9BrB,OAAA,CAAAE,SAAA;IAAAmB,QAAA,gBACErB,OAAA,CAACxB,MAAM;MAAC8C,eAAe,EAAEJ;IAAoB;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAChD1B,OAAA;MAAK2B,SAAS,EAAC,qBAAqB;MAAAN,QAAA,EACjCA;IAAQ;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eACN1B,OAAA,CAACzB,MAAM;MAAAgD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA,eACV,CACH;EAED,oBACE1B,OAAA;IAAK2B,SAAS,EAAC,KAAK;IAAAN,QAAA,eAClBrB,OAAA,CAAChC,MAAM;MAAAqD,QAAA,gBACLrB,OAAA,CAACG,WAAW;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACf1B,OAAA,CAAC9B,MAAM;QAAAmD,QAAA,gBACLrB,OAAA,CAAC/B,KAAK;UAAC2D,IAAI,EAAC,qBAAqB;UAACC,OAAO,eACvC7B,OAAA,CAACoB,UAAU;YAAAC,QAAA,eACTrB,OAAA,CAACP,cAAc;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QACb;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAEJ1B,OAAA,CAAC/B,KAAK;UAAC2D,IAAI,EAAC,GAAG;UAACC,OAAO,eACrB7B,OAAA,CAACoB,UAAU;YAAAC,QAAA,eACTrB,OAAA,CAACN,gBAAgB;cAAA2B,QAAA,eACfrB,OAAA,CAACL,iBAAiB;gBAAA0B,QAAA,eAChBrB,OAAA,CAACd,QAAQ;kBAAAqC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT;QACb;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAEJ1B,OAAA,CAAC/B,KAAK;UAAC2D,IAAI,EAAC,aAAa;UAACC,OAAO,eAC/B7B,OAAA,CAACoB,UAAU;YAAAC,QAAA,gBACTrB,OAAA,CAACtB,UAAU;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACd1B,OAAA,CAAC1B,MAAM;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QACb;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAEJ1B,OAAA,CAAC/B,KAAK;UAAC2D,IAAI,EAAC,aAAa;UAACC,OAAO,eAC/B7B,OAAA,CAACoB,UAAU;YAAAC,QAAA,gBACTrB,OAAA,CAACtB,UAAU;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACd1B,OAAA,CAAC1B,MAAM;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QACb;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAEJ1B,OAAA,CAAC/B,KAAK;UAAC2D,IAAI,EAAC,0BAA0B;UAACC,OAAO,eAC5C7B,OAAA,CAACoB,UAAU;YAAAC,QAAA,eACTrB,OAAA,CAACrB,WAAW;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QACb;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAEJ1B,OAAA,CAAC/B,KAAK;UAAC2D,IAAI,EAAC,UAAU;UAACC,OAAO,eAC5B7B,OAAA,CAACoB,UAAU;YAAAC,QAAA,gBACTrB,OAAA;cAAK2B,SAAS,EAAC;YAAiB;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvC1B,OAAA,CAACb,SAAS;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QACb;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAEJ1B,OAAA,CAAC/B,KAAK;UAAC2D,IAAI,EAAC,UAAU;UAACC,OAAO,eAC5B7B,OAAA,CAACoB,UAAU;YAAAC,QAAA,gBACTrB,OAAA,CAACpB,aAAa;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjB1B,OAAA,CAAC1B,MAAM;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QACb;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAEJ1B,OAAA,CAAC/B,KAAK;UAAC2D,IAAI,EAAC,UAAU;UAACC,OAAO,eAC5B7B,OAAA,CAACoB,UAAU;YAAAC,QAAA,gBACTrB,OAAA;cAAK2B,SAAS,EAAC;YAAiB;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvC1B,OAAA,CAACnB,SAAS;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QACb;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,cAAU,eAAA1B,OAAA,CAAC/B,KAAK;UAAC2D,IAAI,EAAC,SAAS;UAACC,OAAO,eACzC7B,OAAA,CAACoB,UAAU;YAAAC,QAAA,gBACTrB,OAAA;cAAK2B,SAAS,EAAC;YAAiB;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvC1B,OAAA,CAACN,gBAAgB;cAAA2B,QAAA,eACfrB,OAAA,CAACL,iBAAiB;gBAAA0B,QAAA,gBAChBrB,OAAA,CAACvB,UAAU;kBAAA8C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACd1B,OAAA,CAAC1B,MAAM;kBAAAiD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACV1B,OAAA,CAACjB,QAAQ;kBAAC4B,IAAI,EAAEA;gBAAK;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT;QACb;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAEJ1B,OAAA,CAAC/B,KAAK;UAAC2D,IAAI,EAAC,MAAM;UAACC,OAAO,eACxB7B,OAAA,CAACoB,UAAU;YAAAC,QAAA,gBACTrB,OAAA;cAAK2B,SAAS,EAAC;YAAiB;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvC1B,OAAA,CAACZ,GAAG;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QACb;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAaJ1B,OAAA,CAAC/B,KAAK;UAAC2D,IAAI,EAAC,QAAQ;UAACC,OAAO,eAC1B7B,OAAA,CAACoB,UAAU;YAAAC,QAAA,eACTrB,OAAA,CAACX,OAAO;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QACb;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAEJ1B,OAAA,CAAC/B,KAAK;UAAC2D,IAAI,EAAC,QAAQ;UAACC,OAAO,eAC1B7B,OAAA,CAACoB,UAAU;YAAAC,QAAA,eACTrB,OAAA,CAACV,OAAO;cAAAiC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QACb;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAEJ1B,OAAA,CAAC/B,KAAK;UAAC2D,IAAI,EAAC,YAAY;UAACC,OAAO,eAC9B7B,OAAA,CAACoB,UAAU;YAAAC,QAAA,eACTrB,OAAA,CAACT,WAAW;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QACb;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAEJ1B,OAAA,CAAC/B,KAAK;UAAC2D,IAAI,EAAC,cAAc;UAACC,OAAO,eAChC7B,OAAA,CAACoB,UAAU;YAAAC,QAAA,eACTrB,OAAA,CAACT,WAAW;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QACb;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAEJ1B,OAAA,CAAC/B,KAAK;UAAC2D,IAAI,EAAC,UAAU;UAACC,OAAO,eAC5B7B,OAAA,CAACoB,UAAU;YAAAC,QAAA,eACTrB,OAAA,CAACJ,aAAa;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QACb;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAEJ1B,OAAA,CAAC/B,KAAK;UAAC2D,IAAI,EAAC,QAAQ;UAACC,OAAO,eAC1B7B,OAAA,CAACoB,UAAU;YAAAC,QAAA,eACTrB,OAAA,CAACH,kBAAkB;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX;QACb;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAGJ1B,OAAA,CAAC/B,KAAK;UAAC2D,IAAI,EAAC,WAAW;UAACC,OAAO,eAAE7B,OAAA,CAAClB,UAAU;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACnD1B,OAAA,CAAC/B,KAAK;UAAC2D,IAAI,EAAC,YAAY;UAACC,OAAO,eAC9B7B,OAAA,CAACf,cAAc;YAAAoC,QAAA,eACbrB,OAAA,CAAChB,UAAU;cAAC8C,SAAS,EAAEf;YAAc;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QACjB;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGT1B,OAAA,CAACR,EAAE;QAACuC,MAAM,EAAElB,aAAc;QAACmB,OAAO,EAAEb;MAAmB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpD;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV;AAAChB,GAAA,CA3KQD,GAAG;AAAAwB,GAAA,GAAHxB,GAAG;AA6KZ,eAAeA,GAAG;AAAC,IAAAD,EAAA,EAAAyB,GAAA;AAAAC,YAAA,CAAA1B,EAAA;AAAA0B,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}