/* eslint-disable no-unused-vars */
import React, { useState, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faPlus,
  faEdit,
  faTrash,
  faSearch,
  faExclamationCircle,
  faCheckCircle,
  faEye,
  faTimes,
  faSpinner,
  faCalendarAlt,
  faExclamationTriangle,
  faSync
} from '@fortawesome/free-solid-svg-icons';
import '../../css/BlogAdmin.css';
import '../../css/BlogFormModal.css';
import '../../css/shared-delete-dialog.css';
import axios from 'axios';
// Import search-fix.css last to ensure it takes precedence
import '../../css/searchfix.css';
import ApiService from '../../services/apiService';

const BlogAdmin = () => {
  const [blogs, setBlogs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedBlog, setSelectedBlog] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentUser, setCurrentUser] = useState(null);
  
  // Delete confirmation dialog state
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [blogToDelete, setBlogToDelete] = useState(null);
  
  // Get today's date in YYYY-MM-DD format for the date inputs
  const getTodayFormatted = () => new Date().toISOString().split('T')[0];

  const [newBlogForm, setNewBlogForm] = useState({
    blog_title: '',
    blog_description: '',
    posted_date: getTodayFormatted(),
    category: ''
  });
  
  // Blog categories
  const blogCategories = [
    'Recruitment News',
    'Job Reviews',
    'Job Tools',
    'Work From Home',
    'Job Tips'
  ];
  
  const [selectedImage, setSelectedImage] = useState(null);
  const [imagePreview, setImagePreview] = useState(null);
  
  // Fetch current user information
  useEffect(() => {
    const fetchCurrentUser = async () => {
      try {
        const response = await ApiService.adminUsers.getCurrentUser();
        setCurrentUser(response.data);
      } catch (err) {
        console.error("Error fetching current user:", err);
      }
    };

    fetchCurrentUser();
  }, []);

  // Fetch blogs from backend
  useEffect(() => {
    const fetchBlogs = async () => {
      try {
        setLoading(true);
        const response = await ApiService.blogs.getAll();
        
        // Transform backend data to match the format used in frontend
        const formattedBlogs = response.data.map(blog => ({
          id: blog.blog_id,
          title: blog.blog_title,
          description: blog.blog_description ? 
                      blog.blog_description.length > 100 ? 
                      blog.blog_description.substring(0, 100) + '...' : 
                      blog.blog_description : '',
          category: blog.category || 'Uncategorized',
          datePosted: blog.posted_date ? new Date(blog.posted_date).toISOString().split('T')[0] : '-',
          views: blog.views || 0,
          image_url: blog.image_url || null,
          posted_date: blog.posted_date // Keep original posted_date for sorting
        })).sort((a, b) => new Date(b.posted_date) - new Date(a.posted_date)); // Sort by newest first
        
        console.log("Fetched blogs with views:", formattedBlogs.map(blog => ({ id: blog.id, title: blog.title, views: blog.views })));
        setBlogs(formattedBlogs);
        setError(null);
      } catch (err) {
        console.error("Error fetching blogs:", err);
        setError("Failed to load blogs from server");
      } finally {
        setLoading(false);
      }
    };

    fetchBlogs();
  }, []);

  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
  };

  const handleEditBlog = async (blog) => {
    try {
      setLoading(true);
      // Fetch full blog details from backend
      const response = await ApiService.blogs.getById(blog.id);
      const fullBlogDetails = response.data;
      
      setSelectedBlog(blog);
      
      // Format date for the form (YYYY-MM-DD format required by date inputs)
      const formatDate = (dateString) => {
        if (!dateString) return '';
        try {
          // Parse the date and format it as YYYY-MM-DD
          const date = new Date(dateString);
          if (isNaN(date.getTime())) return ''; // Invalid date
          return date.toISOString().split('T')[0];
        } catch (err) {
          console.error("Error formatting date:", err);
          return '';
        }
      };
      
      setNewBlogForm({
        blog_title: fullBlogDetails.blog_title || '',
        blog_description: fullBlogDetails.blog_description || '',
        posted_date: formatDate(fullBlogDetails.posted_date),
        category: fullBlogDetails.category || '',
      });
      
      // Set image preview if blog has an image
      if (fullBlogDetails.image_url) {
        setImagePreview(fullBlogDetails.image_url);
      } else {
        setImagePreview(null);
      }
      setSelectedImage(null); // Reset selected image since we're just showing existing image
      
      setIsModalOpen(true);
      setError(null);
    } catch (err) {
      console.error("Error fetching blog details:", err);
      setError(`Failed to load blog details for ${blog.title}`);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteBlog = async (blogId) => {
    try {
      setLoading(true);
      await ApiService.blogs.delete(blogId);
      setBlogs(blogs.filter(blog => blog.id !== blogId));
      setError(null);
      
      // Reset delete confirmation dialog
      setShowDeleteConfirm(false);
      setBlogToDelete(null);
    } catch (err) {
      console.error("Error deleting blog:", err);
      setError("Failed to delete blog");
    } finally {
      setLoading(false);
    }
  };

  // Function to show delete confirmation
  const confirmDelete = (blog) => {
    setBlogToDelete(blog);
    setShowDeleteConfirm(true);
  };

  // Function to cancel delete
  const cancelDelete = () => {
    setShowDeleteConfirm(false);
    setBlogToDelete(null);
  };

  
  const openNewBlogModal = () => {
    setIsModalOpen(true);
    setSelectedBlog(null);
    // Reset form
    setNewBlogForm({
      blog_title: '',
      blog_description: '',
      posted_date: getTodayFormatted(),
      category: ''
    });
    // Reset image state
    setSelectedImage(null);
    setImagePreview(null);
  };
  
  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedBlog(null);
    // Reset form
    setNewBlogForm({
      blog_title: '',
      blog_description: '',
      posted_date: getTodayFormatted(),
      category: ''
    });
    // Reset image state
    setSelectedImage(null);
    setImagePreview(null);
  };
  
  const handleFormChange = (e) => {
    const { name, value, type, checked, files } = e.target;
    
    if (name === 'blog_image' && files && files.length > 0) {
      const file = files[0];
      setSelectedImage(file);
      
      // Create preview URL for the selected image
      const previewUrl = URL.createObjectURL(file);
      setImagePreview(previewUrl);
    } else {
      setNewBlogForm({
        ...newBlogForm,
        [name]: type === 'checkbox' ? checked : value
      });
    }
  };
  
  // Function to clear image selection
  const clearImageSelection = () => {
    setSelectedImage(null);
    setImagePreview(null);
    
    // Reset the file input by creating a new ref
    const fileInput = document.getElementById('blog_image');
    if (fileInput) fileInput.value = '';
  };
  
  const handleFormSubmit = async (e) => {
    e.preventDefault();
    
    try {
      setLoading(true);
      
      // Create FormData object for multipart/form-data (file upload)
      const formData = new FormData();
      formData.append('blog_title', newBlogForm.blog_title);
      formData.append('blog_description', newBlogForm.blog_description);
      formData.append('posted_date', newBlogForm.posted_date);
      formData.append('category', newBlogForm.category);
      
      // Add image file if selected
      if (selectedImage) {
        formData.append('blog_image', selectedImage);
      }
      
      if (selectedBlog) {
        // Update existing blog
        await ApiService.blogs.update(selectedBlog.id, formData);
      } else {
        // Create new blog
        await ApiService.blogs.create(formData);
      }
      
      // Refresh blogs list
      const response = await ApiService.blogs.getAll();
      const formattedBlogs = response.data.map(blog => ({
        id: blog.blog_id,
        title: blog.blog_title,
        description: blog.blog_description ? 
                    blog.blog_description.length > 100 ? 
                    blog.blog_description.substring(0, 100) + '...' : 
                    blog.blog_description : '',
        category: blog.category || 'Uncategorized',
        datePosted: blog.posted_date ? new Date(blog.posted_date).toISOString().split('T')[0] : '-',
        views: blog.views || 0,
        image_url: blog.image_url || null,
        posted_date: blog.posted_date // Keep original posted_date for sorting
      })).sort((a, b) => new Date(b.posted_date) - new Date(a.posted_date)); // Sort by newest first
      
      setBlogs(formattedBlogs);
      setError(null);
      
      // Close modal
      closeModal();
    } catch (err) {
      console.error("Error saving blog:", err);
      setError(selectedBlog ? "Failed to update blog" : "Failed to create blog");
    } finally {
      setLoading(false);
    }
  };

  // Function to manually refresh blog data - useful for debugging
  const refreshBlogData = async () => {
    try {
      setLoading(true);
      const response = await ApiService.blogs.getAll();
      
      const formattedBlogs = response.data.map(blog => ({
        id: blog.blog_id,
        title: blog.blog_title,
        description: blog.blog_description ? 
                    blog.blog_description.length > 100 ? 
                    blog.blog_description.substring(0, 100) + '...' : 
                    blog.blog_description : '',
        category: blog.category || 'Uncategorized',
        datePosted: blog.posted_date ? new Date(blog.posted_date).toISOString().split('T')[0] : '-',
        views: blog.views || 0,
        image_url: blog.image_url || null,
        posted_date: blog.posted_date
      })).sort((a, b) => new Date(b.posted_date) - new Date(a.posted_date));
      
      console.log("Refreshed blogs with views:", formattedBlogs.map(blog => ({ id: blog.id, title: blog.title, views: blog.views })));
      setBlogs(formattedBlogs);
      setError(null);
    } catch (err) {
      console.error("Error refreshing blog data:", err);
      setError("Failed to refresh blog data");
    } finally {
      setLoading(false);
    }
  };

  const filteredBlogs = blogs.filter(blog => {
    const matchesSearch = blog.title && blog.title.toLowerCase().includes(searchTerm.toLowerCase());
    
    return matchesSearch;
  });

  return (
    <div className="blog-admin-container">
      {/* Delete Confirmation Dialog */}
      {showDeleteConfirm && (
        <div className="delete-confirm-overlay">
          <div className="delete-confirm-dialog">
            <div className="delete-confirm-header">
              <FontAwesomeIcon icon={faExclamationTriangle} className="delete-icon" />
              <h3>Confirm Deletion</h3>
            </div>
            <div className="delete-confirm-content">
              <p>Are you sure you want to delete this blog post?</p>
              <p><strong>{blogToDelete?.title}</strong></p>
              <p>This action cannot be undone.</p>
            </div>
            <div className="delete-confirm-actions">
              <button 
                className="cancel-delete-btn" 
                onClick={cancelDelete}
              >
                Cancel
              </button>
              <button 
                className="confirm-delete-btn" 
                onClick={() => handleDeleteBlog(blogToDelete.id)}
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
      
      <div className="blog-header">
        <h1 className="blog-title">Blog Posts</h1>
        <div className="blog-header-actions">
          <button className="refresh-button" onClick={refreshBlogData} disabled={loading}>
            <FontAwesomeIcon icon={faSync} />
            <span>Refresh</span>
          </button>
          <button className="add-blog-button" onClick={openNewBlogModal} disabled={loading}>
            <FontAwesomeIcon icon={faPlus} />
            <span>Add New Blog</span>
          </button>
        </div>
      </div>

      {error && (
        <div className="error-message">
          <FontAwesomeIcon icon={faExclamationCircle} />
          <span>{error}</span>
        </div>
      )}

      <div className="filters-container">
        <div className="search-container">
          <div className="search-input-wrapper">
            <FontAwesomeIcon icon={faSearch} className="search-icon" />
            <input
              type="text"
              placeholder="Search by title..."
              value={searchTerm}
              onChange={handleSearchChange}
              className="search-input"
              style={{ paddingLeft: '40px' }}
            />
          </div>
        </div>
      </div>

      <div className="table-container">
        {loading ? (
          <div className="loading-container">
            <FontAwesomeIcon icon={faSpinner} spin size="2x" />
            <span>Loading blogs...</span>
          </div>
        ) : (
          <table className="blogs-table">
            <thead>
              <tr>
                <th>Blog</th>
                <th>Category</th>
                <th>Posted Date</th>
                <th>Views</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {filteredBlogs.length > 0 ? (
                filteredBlogs.map(blog => (
                  <tr key={blog.id}>
                    <td>
                      <div className="blog-content-container" style={{ display: 'flex', alignItems: 'center' }}>
                        {blog.image_url && (
                          <div className="blog-image-thumbnail" style={{ marginRight: '15px' }}>
                            <img 
                              src={blog.image_url} 
                              alt={blog.title} 
                              style={{ width: '60px', height: '60px', objectFit: 'cover', borderRadius: '4px' }}
                            />
                          </div>
                        )}
                        <div className="blog-text-content">
                          <div className="blog-title-row">{blog.title}</div>
                          <div className="blog-description">{blog.description}</div>
                        </div>
                      </div>
                    </td>
                    <td>
                      {blog.category || 'Uncategorized'}
                    </td>
                    <td>{blog.datePosted}</td>
                    <td>
                      <div className="views-container">
                        <FontAwesomeIcon icon={faEye} className="views-icon" />
                        {blog.views || 0}
                      </div>
                    </td>
                    <td>
                      <div style={{ display: 'flex' }}>
                        <button 
                          className="action-button edit-button" 
                          onClick={() => handleEditBlog(blog)}
                          title="Edit blog"
                          disabled={loading}
                        >
                          <FontAwesomeIcon icon={faEdit} />
                        </button>

                        {currentUser && (currentUser.role === 'Admin' || currentUser.role === 'Superadmin') && (
                          <button 
                            className="action-button delete-button" 
                            onClick={() => confirmDelete(blog)}
                            title="Delete blog"
                            disabled={loading}
                          >
                            <FontAwesomeIcon icon={faTrash} />
                          </button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan="5" className="no-blogs-message">
                    {searchTerm ? 
                      "No blogs match your search criteria" : 
                      "No blogs available"
                    }
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        )}
      </div>

      {/* Blog Modal */}
      {isModalOpen && (
        <div className="modal-overlay" onClick={(e) => {
          // Close modal when clicking outside
          if (e.target.className === 'modal-overlay') {
            closeModal();
          }
        }}>
          <div className="blog-modal">
            <div className="modal-header">
              <h2>{selectedBlog ? 'Edit Blog' : 'Add New Blog'}</h2>
              <button className="close-button" onClick={closeModal}>
                <FontAwesomeIcon icon={faTimes} />
              </button>
            </div>
            <div className="modal-content">
              <form onSubmit={handleFormSubmit}>
                <div className="form-row">
                  <div className="form-group full-width">
                    <label htmlFor="blog_title">Blog Title</label>
                    <input
                      type="text"
                      id="blog_title"
                      name="blog_title"
                      value={newBlogForm.blog_title}
                      onChange={handleFormChange}
                      required
                      placeholder="Enter blog title"
                    />
                  </div>
                </div>

                <div className="form-row">
                  <div className="form-group">
                    <label htmlFor="posted_date">Posted Date</label>
                    <input
                      type="date"
                      id="posted_date"
                      name="posted_date"
                      value={newBlogForm.posted_date}
                      onChange={handleFormChange}
                      required
                    />
                  </div>
                  <div className="form-group">
                    <label htmlFor="category">Category</label>
                    <select
                      id="category"
                      name="category"
                      value={newBlogForm.category}
                      onChange={handleFormChange}
                      required
                    >
                      <option value="">Select a category</option>
                      {blogCategories.map((category, index) => (
                        <option key={index} value={category}>{category}</option>
                      ))}
                    </select>
                  </div>
                </div>
                
                <div className="form-row">
                  <div className="form-group full-width">
                    <label htmlFor="blog_image">Blog Image (Optional)</label>
                    <div className="modern-file-upload">
                      <div 
                        className={`file-drop-zone ${imagePreview ? 'has-image' : ''}`}
                        onClick={() => document.getElementById('blog_image').click()}
                      >
                        {!imagePreview && (
                          <div className="upload-placeholder">
                            <FontAwesomeIcon icon={faPlus} className="upload-icon" />
                            <span>Click to upload image</span>
                            <span className="upload-hint">JPG, PNG or GIF up to 5MB</span>
                          </div>
                        )}
                        {imagePreview && (
                          <div className="image-preview-wrapper">
                            <img 
                              src={imagePreview} 
                              alt="Blog preview" 
                              className="image-preview"
                            />
                            <div className="image-overlay">
                              <button 
                                type="button" 
                                onClick={(e) => {
                                  e.stopPropagation();
                                  clearImageSelection();
                                }}
                                className="image-remove-button"
                                title="Remove image"
                              >
                                <FontAwesomeIcon icon={faTimes} />
                              </button>
                            </div>
                          </div>
                        )}
                      </div>
                      <input
                        type="file"
                        id="blog_image"
                        name="blog_image"
                        accept="image/*"
                        onChange={handleFormChange}
                        className="hidden-file-input"
                      />
                    </div>
                  </div>
                </div>

                <div className="form-row">
                  <div className="form-group full-width">
                    <label htmlFor="blog_description">Blog Content</label>
                    <textarea
                      id="blog_description"
                      name="blog_description"
                      rows="8"
                      value={newBlogForm.blog_description}
                      onChange={handleFormChange}
                      required
                      placeholder="Enter blog content"
                    ></textarea>
                  </div>
                </div>

                <div className="modal-footer">
                  <button type="button" className="cancel-button" onClick={closeModal}>
                    Cancel
                  </button>
                  <button type="submit" className="submit-button">
                    {selectedBlog ? 'Update Blog' : 'Create Blog'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default BlogAdmin;