.urgent-job-card {
  background: linear-gradient(135deg, #fff5f5 0%, #ffffff 100%);
  border: 1px solid #f86b77;
  border-radius: 12px;
  padding: 8px;
  margin-bottom: 12px;
  width: 250px;
  height: 280px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(255, 71, 87, 0.15);
  display: flex;
  flex-direction: column;
}

.urgent-job-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 71, 87, 0.25);
  border-color: #ff3742;
}

.urgent-tag {
  position: absolute;
  top: 8px;
  right: 8px;
  background: linear-gradient(45deg, #ff4757, #ff3742);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: bold;
  z-index: 2;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.urgent-job-image {
  width: 100%;
  height: 140px;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 6px;
  position: relative;
}

.urgent-job-image img {
  width: 100%;
  /* height: 140px; */
  object-fit: cover;
  object-position: center;
  display: block;
  margin: 0 auto;
  transition: transform 0.3s ease;
}

.urgent-job-card:hover .urgent-job-image img {
  transform: scale(1.05);
}

.urgent-job-content {
  padding: 0;
}

.company-info-urgent {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.company-logo-urgent {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  overflow: hidden;
  margin-right: 8px;
  flex-shrink: 0;
}

.company-logo-urgent img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.company-details-urgent {
  flex: 1;
  min-width: 0;
}

.company-name-urgent {
  font-size: 12px;
  font-weight: 600;
  color: #2d3436;
  margin: 0;
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.verified-badge {
  font-size: 9px;
  color: #00b894;
  font-weight: 500;
  display: block;
  line-height: 1;
}

.job-position-urgent {
  font-size: 13px;
  font-weight: 700;
  color: #2d3436;
  margin: 4px 0;
  line-height: 1.2;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.job-meta-urgent {
  display: flex;
  flex-direction: column;
  gap: 2px;
  margin-bottom: 4px;
}

.job-meta-item-urgent {
  font-size: 10px;
  color: #636e72;
  display: flex;
  align-items: center;
  gap: 4px;
}

.job-meta-item-urgent svg {
  width: 10px;
  height: 10px;
  color: #ff4757;
}

.job-footer-urgent {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
  margin-top: auto;
}

.job-salary-urgent {
  font-size: 12px;
  font-weight: 700;
  color: #00b894;
}

.job-posted-urgent {
  font-size: 9px;
  color: #636e72;
}

.job-stats-urgent {
  display: none;
}

.job-stats-urgent span {
  display: flex;
  align-items: center;
  gap: 3px;
}

.job-stats-urgent svg {
  width: 9px;
  height: 9px;
}

/* Responsive design for smaller screens */
@media (max-width: 768px) {
  .urgent-job-card {
    width: 100%;
    max-width: 320px;
    height: 290px;
    padding: 12px;
    margin: 15px auto;
    align-self: center;
  }
  
  .urgent-job-image {
    height: 150px;
    margin-bottom: 10px;
  }
  
  .urgent-job-image img {
    height: 150px;
    width: 100%;
    object-fit: cover;
  }
  
  .job-position-urgent {
    font-size: 14px;
  }
  
  .company-name-urgent {
    font-size: 12px;
  }
}

/* Extra small screens (phones) */
@media (max-width: 576px) {
  .urgent-job-card {
    width: 100%;
    max-width: 320px;
    height: 280px;
    padding: 10px;
    margin: 12px auto;
    align-self: center;
  }
  
  .urgent-job-image {
    height: 140px;
    margin-bottom: 8px;
  }
  
  .urgent-job-image img {
    height: 140px;
    width: 100%;
    object-fit: cover;
  }
  
  .job-position-urgent {
    font-size: 13px;
  }
  
  .company-name-urgent {
    font-size: 11px;
  }
}