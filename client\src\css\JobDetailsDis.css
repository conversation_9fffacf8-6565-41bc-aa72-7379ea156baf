.job-details{
    padding: 80px 220px 50px 220px;
}

/* JobDetails.css */

/* Global styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Inter', sans-serif;
  }
  
  body {
    background-color: #f7f7f7;
  }
  
  a {
    text-decoration: none;
    color: inherit;
  }
  
  ul {
    list-style: none;
  }
  
  button {
    cursor: pointer;
    border: none;
    border-radius: 4px;
    padding: 8px 16px;
    font-weight: 500;
  }
  
  /* Layout */
  .job-details-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 16px;
  }
  
  /* Header */
  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 0;
    border-bottom: 1px solid #f0f0f0;
  }
  
  .logo {
    display: flex;
    align-items: center;
    font-weight: 700;
    font-size: 20px;
  }
  
  .logo-icon {
    color: #8844ee;
    margin-right: 4px;
  }
  
  .main-nav ul {
    display: flex;
    gap: 24px;
  }
  
  .main-nav a {
    color: #333;
    font-weight: 500;
  }
  
  .main-nav a.active {
    color: #8844ee;
  }
  
  .header-buttons {
    display: flex;
    gap: 10px;
  }
  
  .btn-apply {
    background-color: #ff4757;
    color: white;
  }
  
  .btn-vacancy {
    background-color: #8844ee;
    color: white;
  }
  
  /* Job Title Section */
  .job-title-section {
    background-color: #ffefef;
    padding: 32px 16px;
    margin-bottom: 24px;
  }
  
  .job-title-section h1 {
    font-size: 28px;
    margin-bottom: 8px;
    color: #333;
  }
  
  .breadcrumb {
    color: #777;
    font-size: 14px;
  }
  
  .breadcrumb a {
    color: #555;
  }
  
  /* Main Content */
  .main-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 24px;
    margin-bottom: 32px;
  }
  
  /* Job Listing */
  .job-featured {
    background-color: white;
    border-radius: 8px;
    padding: 24px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  }
  
  .job-featured h2 {
    font-size: 22px;
    margin-bottom: 8px;
    color: #333;
  }
  
  .company-image {
    margin: 24px 0;
    border-radius: 8px;
    overflow: hidden;
  }
  
  .company-image img {
    width: 100%;
    height: auto;
    object-fit: cover;
  }
  
  .job-section-title {
    font-size: 20px;
    margin: 24px 0 16px;
    color: #333;
  }
  
  .job-info-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
    margin-bottom: 24px;
  }
  
  .job-info-item h4 {
    color: #777;
    font-size: 15px;
    margin-bottom: 8px;
  }
  
  .job-info-item p {
    color: #333;
    font-weight: 500;
  }
  
  .btn-details {
    background-color: #8844ee;
    color: white;
  }
  
  .btn-apply-now {
    background-color: #ff4757;
    color: white;
  }
  
  .btn-join-group {
    background-color: #2ecc71;
    color: white;
  }
  
  .job-description {
    margin: 24px 0;
    line-height: 1.6;
    color: #555;
  }
  
  .share-buttons {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    margin-top: 24px;
  }
  
  .btn-share {
    background-color: #f0f0f0;
    color: #555;
  }
  
  .social-share {
    display: flex;
    gap: 8px;
  }
  
  .btn-facebook {
    background-color: #3b5998;
    color: white;
  }
  
  .btn-whatsapp {
    background-color: #25d366;
    color: white;
  }
  
  .btn-twitter {
    background-color: #1da1f2;
    color: white;
  }
  
  .btn-pinterest {
    background-color: #e60023;
    color: white;
  }
  
  /* Sidebar */
  .job-sidebar {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }
  
  .job-details-card {
    background-color: white;
    border-radius: 8px;
    padding: 24px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  }
  
  .job-meta li {
    display: flex;
    gap: 16px;
    padding: 16px 0;
    border-bottom: 1px solid #f0f0f0;
  }
  
  .job-meta li:last-child {
    border-bottom: none;
  }
  
  .job-meta strong {
    display: block;
    color: #777;
    font-size: 14px;
    margin-bottom: 4px;
  }
  
  .job-meta p {
    color: #333;
    font-weight: 500;
  }
  
  .follow-section {
    background-color: white;
    border-radius: 8px;
    padding: 24px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  }
  
  .follow-section h3 {
    margin-bottom: 16px;
    font-size: 18px;
    color: #333;
  }
  
  .social-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }
  
  .social-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 12px;
    border-radius: 4px;
    color: white;
    text-align: center;
    font-size: 14px;
  }
  
  .social-item .count {
    font-weight: bold;
    font-size: 16px;
  }
  
  .social-item .label {
    font-size: 12px;
  }
  
  .facebook {
    background-color: #3b5998;
  }
  
  .youtube {
    background-color: #ff0000;
  }
  
  .twitter {
    background-color: #1da1f2;
  }
  
  .website {
    background-color: #555;
  }
  
  .whatsapp {
    background-color: #25d366;
  }
  
  .instagram {
    background-color: #e1306c;
  }
  
  .apply-widget {
    background-color: white;
    border-radius: 8px;
    padding: 24px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  }
  
  .apply-widget h3 {
    margin-bottom: 16px;
    font-size: 18px;
    color: #333;
  }
  
  .btn-apply-any {
    background-color: #8844ee;
    color: white;
    width: 100%;
    padding: 12px;
  }
  
  /* Responsive adjustments */
  @media (max-width: 768px) {
    .main-content {
      grid-template-columns: 1fr;
    }
    
    .header {
      flex-direction: column;
      gap: 16px;
    }
    
    .main-nav ul {
      overflow-x: auto;
      padding-bottom: 8px;
    }
    
    .job-info-grid {
      grid-template-columns: 1fr;
    }
  }