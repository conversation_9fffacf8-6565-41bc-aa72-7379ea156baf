import React from 'react';
import { Navigate } from 'react-router-dom';

/**
 * ProtectedRoute component that checks for authentication before allowing access
 * Redirects to admin login page if user is not authenticated
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components to render if authenticated
 * @returns {React.ReactNode} Protected component or redirect
 */
const ProtectedRoute = ({ children }) => {
  // Check if user is authenticated by verifying token and user data in localStorage
  const isAuthenticated = () => {
    const token = localStorage.getItem('token');
    const user = localStorage.getItem('user');
    
    // Return true only if both token and user data exist
    return token && user;
  };

  // If user is authenticated, render the protected component
  // Otherwise, redirect to admin login page
  return isAuthenticated() ? children : <Navigate to="/jp-admin" replace />;
};

export default ProtectedRoute;