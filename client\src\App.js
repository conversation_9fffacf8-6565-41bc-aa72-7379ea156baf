import './App.css';
import { BrowserRouter as Router, Route, Routes, useLocation } from 'react-router-dom';
import { useState, useEffect } from 'react';
import Banner from './components/Banner';
import Footer from './components/Footer';
import Navbar from './components/Navbar';
import SearchArea from './components/SearchArea';
import JobDetails from './components/JobDetails';
import FullDetails from './components/FullDetails';
import JobDetailsDis from './components/JobDetailsDis';
import ContactUs from './components/ContactUs';
import AdminLogin from './components/AdminLogin';
import JobBoard from './components/JobBoard';
import AdminPanel from './components/Admin/AdminPanel';
import ProtectedRoute from './components/ProtectedRoute';
import HomePage from './components/HomePage';
import Companies from './components/Companies';
import FAQ from './components/FAQ';

import AboutUs from './components/AboutUs';
import JobBlog from './components/JobBlog';
import BlogArticle from './components/BlogArticle';
import Cv from './components/Cv';
import CompanyDetails from './components/CompanyDetails';
import { LocationProvider } from './contexts/LocationContext';
import { JobFilterProvider } from './contexts/JobFilterContext';
import PrivacyPolicy from 'components/PrivacyPolicy';
import TermsAndConditions from 'components/TermsAndConditions';
import NotFound from './components/NotFound';

// ScrollToTop component - scrolls to top when location changes
function ScrollToTop() {
  const { pathname } = useLocation();
  
  useEffect(() => {
    window.scrollTo(0, 0);
  }, [pathname]);
  
  return null;
}

function App() {
  // State management
  const [jobs, setJobs] = useState([]);
  const [isCvModalOpen, setIsCvModalOpen] = useState(false);

  // Event handlers
  const handleJobPost = (newJob) => {
    setJobs([newJob, ...jobs]);
    alert("Job posted successfully!");
  };

  const handleApplyNowClick = () => {
    setIsCvModalOpen(true);
  };

  const handleCloseCvModal = () => {
    setIsCvModalOpen(false);
  };

  // Main layout wrapper component
  const MainLayout = ({ children }) => (
    <>
      <Navbar onApplyNowClick={handleApplyNowClick} />
      <div className="main-content-padded">
        {children}
      </div>
      <Footer />
    </>
  );

  return (
    <div className="App">
      <Router>
        <ScrollToTop />
        <Routes>
          <Route path="/company/:companyId" element={
            <MainLayout>
              <CompanyDetails />
            </MainLayout>
          } />
          {/* Public Pages */}
          <Route path="/" element={
            <MainLayout>
              <LocationProvider>
                <JobFilterProvider>
                  <HomePage />
                </JobFilterProvider>
              </LocationProvider>
            </MainLayout>
          } />

          <Route path="/job/:jobId" element={
            <MainLayout>
              <JobDetails />
              <Banner />
            </MainLayout>
          } />

          <Route path="/job/:jobId" element={
            <MainLayout>
              <JobDetails />
              <Banner />
            </MainLayout>
          } />

          <Route path="/job/:jobId/full-details" element={
            <MainLayout>
              <FullDetails />
            </MainLayout>
          } />

          <Route path="/company" element={
            <MainLayout>
              <div className="content-spacing"></div>
              <Companies />
            </MainLayout>
          } />

          <Route path="/job-dis" element={
            <MainLayout>
              <JobDetailsDis />
              <Banner />
            </MainLayout>
          } />

          <Route path="/contact" element={
            <MainLayout>
              <div className="content-spacing"></div>
              <ContactUs />
            </MainLayout>
          } />          <Route path="/browse" element={
            <MainLayout>
              <div className="content-spacing"></div>
              <LocationProvider>
                <JobFilterProvider>
                  <SearchArea />
                  <Banner />
                  <JobBoard jobs={jobs} />
                </JobFilterProvider>
              </LocationProvider>
            </MainLayout>
          } />

          <Route path="/faq" element={
            <MainLayout>
              <div className="content-spacing"></div>
              <FAQ />
            </MainLayout>
          } />

          {/* <Route path="/job-board" element={
            <MainLayout>  
              <div className="content-spacing"></div>
              <LocationProvider>
                <JobBoard jobs={jobs} />
              </LocationProvider>
              
              <Banner />
            </MainLayout>
          } /> */}

          <Route path="/about" element={
            <MainLayout>
              <AboutUs />
            </MainLayout>
          } />

          <Route path="/blogs" element={
            <MainLayout>
              <JobBlog/>
            </MainLayout>
          } />

          <Route path="/blogs/:id" element={
            <MainLayout>
              <BlogArticle />
            </MainLayout>
          } />
          
          <Route path="/job-Article" element={
            <MainLayout>
              <BlogArticle/>
            </MainLayout>
          } />

          <Route path="/privacy" element={
            <MainLayout>
              <PrivacyPolicy/>
            </MainLayout>
          } />

          <Route path="/terms" element={
            <MainLayout>
              <TermsAndConditions/>
            </MainLayout>
          } />

          {/* Admin Routes */}
          <Route path="/jp-admin" element={<AdminLogin />} />
          <Route path="/dashboard" element={
            <ProtectedRoute>
              <AdminPanel onJobPost={handleJobPost} />
            </ProtectedRoute>
          } />
          
          {/* 404 Not Found - Catch all unmatched routes */}
          <Route path="*" element={
            <MainLayout>
              <NotFound />
            </MainLayout>
          } />
        </Routes>

        {/* CV Modal */}
        <Cv isOpen={isCvModalOpen} onClose={handleCloseCvModal} />
      </Router>
    </div>
  );
}

export default App;
