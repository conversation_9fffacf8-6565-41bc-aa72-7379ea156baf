import React, { useState } from 'react';
import { Link, NavLink } from 'react-router-dom'; // Import NavLink
import '../css/Navbar.css';
import jobpagelogo from '../assets/job-page-logo.png';

const Navbar = ({ onApplyNowClick }) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <nav className="navbar">
      <div className='navbar-container'>
        {/* Logo */}
        <div className="logo">
          <Link to="/">
            <img src={jobpagelogo} alt="Logo" />
          </Link>
        </div>

        {/* Hamburger Menu */}
        <button
          className={`hamburger ${isMenuOpen ? 'active' : ''}`}
          onClick={() => setIsMenuOpen(!isMenuOpen)}
        >
          <span className="bar"></span>
          <span className="bar"></span>
          <span className="bar"></span>
        </button>

        {/* Navigation Links and Buttons */}
        <div className={`navbar-collapse ${isMenuOpen ? 'active' : ''}`}>
          <ul className="nav-links">
            <li>
              <NavLink
                to="/"
                className={({ isActive }) => isActive ? 'active' : ''} // Add active class when active
                onClick={() => setIsMenuOpen(false)}
              >
                Home
              </NavLink>
            </li>
            <li>
              <NavLink
                to="/browse"
                className={({ isActive }) => isActive ? 'active' : ''} // Add active class when active
                onClick={() => setIsMenuOpen(false)}
              >
                Browse&nbsp;Jobs
              </NavLink>
            </li>
            <li>
              <NavLink
                to="/company"
                className={({ isActive }) => isActive ? 'active' : ''} // Add active class when active
                onClick={() => setIsMenuOpen(false)}
              >
                Top Companies
              </NavLink>
            </li>
            <li>
              <NavLink
                to="/blogs"
                className={({ isActive }) => isActive ? 'active' : ''} // Add active class when active
                onClick={() => setIsMenuOpen(false)}
              >
                Blogs
              </NavLink>
            </li>
          </ul>
          <div className="nav-buttons">
            <button 
              className="apply-btn" 
              onClick={() => {
                setIsMenuOpen(false);
                onApplyNowClick();
              }}
            >
              Apply Now
            </button>
            <Link to="/contact" className="post-btn" onClick={() => setIsMenuOpen(false)}>
              Contact Us
            </Link>
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
