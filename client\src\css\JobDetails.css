/* Job Single Page CSS - Matching Figma Design */

.job-single-container {
  background-color: #ffffff;
  min-height: 100vh;
  padding-top: 80px;
  padding-left: 210px; /* Add horizontal padding to replace main-content-padded */
  padding-right: 210px;
  box-sizing: border-box;
}

.container {
  max-width: 100%;
  margin: 0 auto;
  padding: 0 40px;
}

/* Loading and Error States */
.loading-state,
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 100px 0;
}

.loading-state h2,
.error-state h2 {
  color: #1F2938;
  font-weight: 700;
  margin-bottom: 20px;
}

.error-state a {
  color: #9777FA;
  text-decoration: none;
  padding: 10px 20px;
  border: 1px solid #9777FA;
  border-radius: 5px;
  transition: all 0.2s;
}

.error-state a:hover {
  background-color: #9777FA;
  color: white;
}

/* Header Background */
.job-header-bg {
  background: #FFF4E9;
  min-height: 180px;
  position: relative;
  overflow: hidden;
  border-radius: 20px;

}

.breadcrumb-nav {
  padding-top: 30px;
  font-size: 16px;
  color: #A0ABB8;
  text-align: left;
  font-family: 'Open Sans', sans-serif;
  letter-spacing: 0.5%;
}

.breadcrumb-nav a {
  color: #A0ABB8;
  text-decoration: none;
}

.breadcrumb-nav a:hover {
  color: #9777FA;
}

/* Job Title Header */
.job-title-header {
  padding-top: 20px;
  padding-bottom: 40px;
}

.job-title-header h1 {
  font-family: 'Montserrat', sans-serif;
  font-weight: 700;
  font-size: 38px;
  line-height: 1.22;
  color: #000000;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Content Wrapper */
.job-content-wrapper {
  display: grid;
  grid-template-columns: 4fr 1fr;
  gap: 60px;
  padding: 40px 0;
  max-width: 1400px;
  margin: 0 auto;
}

/* Main Content */
.job-main-content {
  background: white;
}

.job-hero-image {
  width: 100%;
  max-width: 800px;
  height: auto;
  min-height: 300px;
  max-height: 450px;
  border-radius: 15px;
  overflow: hidden;
  margin: 0 auto 40px auto;
  display: flex;
  align-items: center;
  justify-content: center;
}

.job-hero-image img {
  width: 100%;
  height: auto;
  max-height: 450px;
  object-fit: contain;
  object-position: center;
}

/* Job Content Section */
.job-content-section {
  padding: 0;
}

.job-main-title {
  font-family: 'Montserrat', sans-serif;
  font-weight: 700;
  font-size: 32px;
  line-height: 1.1;
  color: #37404E;
  margin: 0 0 30px 0;
}


/* Job Info with Actions */
.job-info-with-actions {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 40px;
}

.info-row {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 400px;
  padding: 15px 0;
}

.info-row .label-title {
  font-family: 'Montserrat', sans-serif;
  font-weight: 700;
  font-size: 18px;
  line-height: 1.273;
  color: #828282;
  min-width: 180px;
  text-align: left;
}

.info-row .value-text {
  font-family: 'Open Sans', sans-serif;
  font-weight: 600;
  font-size: 16px;
  line-height: 1.5;
  color: #333333;
  text-align: left;
}

/* Inline Action Buttons */
.info-row .btn-full-details,
.info-row .btn-apply-now,
.info-row .btn-join-group {
  padding: 10px 20px;
  border-radius: 8px;
  border: none;
  font-family: 'Open Sans', sans-serif;
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  min-width: 120px;
}

.info-row .btn-full-details {
  background: #9777FA;
  color: white;
}

.info-row .btn-apply-now {
  background: #FF2D55;
  color: white;
}

.info-row .btn-join-group {
  background: #00C070;
  color: white;
}

.info-row .btn-full-details:hover,
.info-row .btn-apply-now:hover,
.info-row .btn-join-group:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Job Description */

.job-description {
  margin: 40px 0;
}

.job-single-container .job-description-large p {
  font-family: 'Open Sans', sans-serif !important;
  font-weight: 400 !important;
  font-size: 22px !important;
  line-height: 2.2 !important;
  color: #37404E !important;
  margin: 0 !important;
}

/* Action Buttons */
.job-action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 40px;
}

.btn-save-job {
  background: linear-gradient(135deg, #0D5D52 0%, #2C9A8B 100%);
  border: 1px solid rgba(151, 119, 250, 0.5);
  border-radius: 10px;
  padding: 12px 25px;
  font-family: 'Open Sans', sans-serif;
  font-weight: 400;
  font-size: 16px;
  color: #37404E;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-full-details {
  background: #9777FA;
  border: none;
  border-radius: 10px;
  padding: 15px 28px;
  font-family: 'Open Sans', sans-serif;
  font-weight: 400;
  font-size: 18px;
  color: white;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-apply-now {
  background: #FF2D55;
  border: none;
  border-radius: 10px;
  padding: 15px 28px;
  font-family: 'Open Sans', sans-serif;
  font-weight: 400;
  font-size: 18px;
  color: white;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-join-group {
  background: #00C070;
  border: none;
  border-radius: 10px;
  padding: 15px 28px;
  font-family: 'Open Sans', sans-serif;
  font-weight: 400;
  font-size: 18px;
  color: white;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-save-job:hover,
.btn-full-details:hover,
.btn-apply-now:hover,
.btn-join-group:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Social Share Section */
.social-share-section {
  margin: 40px 0;
}

.share-buttons-row {
  display: flex;
  gap: 15px;
  justify-content: flex-start;
}


.share-btn {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 5px 18px;
  border: none;
  border-radius: 10px;
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  font-size: 18px;
  cursor: pointer;
  transition: transform 0.18s, box-shadow 0.18s, background 0.18s, color 0.18s;
  box-shadow: 0 2px 8px rgba(0,0,0,0.07);
  text-decoration: none;
  outline: none;
}

.share-btn-icon {
  font-size: 22px;
  margin-right: 6px;
}

.share-btn-modern.whatsapp {
  background:#25d366;
  color: #fff;
  border: none;
}
.share-btn-modern.tiktok {
  background: #000;
  color: #fff;
  border: none;
}
.share-btn-modern.facebook {
  background: #3b5998;
  color: #fff;
  border: none;
}
.share-btn-modern.instagram {
  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
  color: #fff;
  border: none;
}
.share-btn-modern:hover {
  transform: translateY(-3px) scale(1.04);
  box-shadow: 0 6px 18px rgba(0,0,0,0.13);
  filter: brightness(1.08);
}

/* Related Jobs Section */
.related-jobs-section {
  margin-top: 80px;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 60px;
}

.section-header h2 {
  font-family: 'Montserrat', sans-serif;
  font-weight: 700;
  font-size: 28px;
  line-height: 1.22;
  color: #1F2938;
  margin: 0;
  white-space: nowrap;
}

.section-line {
  flex: 1;
  height: 1px;
  background: rgba(6, 18, 36, 0.1);
}

.btn-explore-more {
  background: #9777FA;
  border: none;
  border-radius: 10px;
  padding: 15px 28px;
  font-family: 'Open Sans', sans-serif;
  font-weight: 400;
  font-size: 18px;
  color: white;
  cursor: pointer;
  transition: all 0.2s;
  white-space: nowrap;
}

.btn-explore-more:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(151, 119, 250, 0.3);
}

/* Related Jobs Grid */
.related-jobs-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
}

/* Swipeable Carousel for Mobile */
.related-jobs-slider-container {
  margin: 0 -15px;
  padding: 0 15px;
}

.related-jobs-slider .slick-track {
  display: flex;
  gap: 10px;
}

.slider-job-card-wrapper {
  padding: 10px;
  box-sizing: border-box;
}

.related-jobs-slider .slick-dots {
  bottom: -30px;
}

.related-jobs-slider .slick-dots li button:before {
  font-size: 12px;
  color: #9777FA;
  opacity: 0.3;
}

.related-jobs-slider .slick-dots li.slick-active button:before {
  opacity: 1;
  color: #9777FA;
}

/* Touch indicator for mobile */
@media (max-width: 768px) {
  .related-jobs-slider-container:after {
    content: "← Swipe →";
    display: block;
    text-align: center;
    color: #999;
    font-size: 14px;
    margin-top: 15px;
    font-style: italic;
    opacity: 0.7;
  }
}

/* Removed custom job card styling as we're using the JobCard component */

.no-related-jobs {
  grid-column: 1 / -1;
  text-align: center;
  padding: 40px;
  background: #f9f9f9;
  border-radius: 10px;
  color: #666;
  font-family: 'Open Sans', sans-serif;
}

.no-related-jobs {
  grid-column: 1 / -1;
  text-align: center;
  padding: 40px;
  background: #f9f9f9;
  border-radius: 10px;
  color: #666;
  font-family: 'Open Sans', sans-serif;
}

/* Sidebar */
.job-sidebar {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.sidebar-card {
  background: white;
  border-radius: 10px;
  box-shadow: 5px 4px 12px rgba(0, 0, 0, 0.25);
  overflow: hidden;
}

.job-details-card .card-content {
  padding: 32px 24px 24px;
}

.detail-item {
  display: flex;
  gap: 12px;
  margin-bottom: 30px;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-icon {
  width: 22px;
  height: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 2px;
}

.detail-icon svg {
  color: #A0ABB8;
  font-size: 14px;
}

.detail-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-label {
  font-family: 'Open Sans', sans-serif;
  font-weight: 400;
  font-size: 16px;
  color: #727272;
}

.detail-value {
  font-family: 'Montserrat', sans-serif;
  font-weight: 700;
  font-size: 16px;
  line-height: 1.22;
  color: #1F2938;
}

.card-divider {
  height: 1px;
  background: rgba(6, 18, 36, 0.1);
  margin: 0;
}

/* Follow Section */

.follow-section {
  padding: 0 20px;
}

.follow-social-row {
  display: flex;
  gap: 16px;
  margin-top: 10px;
}

.follow-social-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 38px;
  height: 38px;
  border-radius: 50%;
  font-size: 20px;
  color: #fff;
  background: #bbb;
  transition: background 0.2s, color 0.2s;
  text-decoration: none;
}
.follow-social-icon.facebook { background: #3b5998; }
.follow-social-icon.whatsapp { background: #25d366; }
.follow-social-icon.instagram { background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%); }
.follow-social-icon.tiktok { background: #000; }
.follow-social-icon:hover {
  opacity: 0.85;
  color: #fff;
}

.section-title {
  font-family: 'Montserrat', sans-serif;
  font-weight: 700;
  font-size: 28px;
  line-height: 1.22;
  color: #1F2938;
  margin: 0 0 20px 0;
}

.follow-section .section-line {
  height: 1px;
  background: rgba(6, 18, 36, 0.1);
  margin-bottom: 30px;
}

/* Responsive Design */
@media (max-width: 1440px) {
  .container {
    padding: 0 60px;
  }
  
  .job-content-wrapper {
    max-width: 1200px;
  }
}

@media (max-width: 1200px) {
  .container {
    padding: 0 40px;
  }
  
  .job-content-wrapper {
    grid-template-columns: 1fr;
    gap: 40px;
    max-width: 100%;
  }
  
  .job-sidebar {
    order: -1;
  }
  
  .pattern-circles {
    right: 100px;
  }
  
  .pattern-elements {
    right: 50px;
  }
}

@media (max-width: 992px) {
  .container {
    padding: 0 30px;
  }
  
  .job-title-header h1 {
    font-size: 36px;
  }
  
  .job-content-wrapper {
    gap: 30px;
  }
  
  .related-jobs-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }
  
  .job-hero-image {
    max-width: 100%;
    max-height: 350px;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 20px;
  }

  .job-content-wrapper {
    display: block !important;
    gap: 0 !important;
    padding: 0 !important;
    width: 100% !important;
    max-width: 100% !important;
  }

  .job-sidebar {
    margin-top: 30px;
    width: 100% !important;
  }

  .job-single-container {
    padding-top: 60px;
  }
  
  .job-title-header {
    padding-top: 40px;
    padding-bottom: 30px;
  }
  
  .job-title-header h1 {
    font-size: 28px;
    line-height: 1.3;
  }
  
  .job-main-title {
    font-size: 22px;
    line-height: 1.4;
    margin-bottom: 20px;
  }
  
  .related-jobs-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .info-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    padding: 20px 0;
  }
  
  .info-row .value-text {
    text-align: left;
    margin-bottom: 10px;
  }
  
  .info-row .btn-full-details,
  .info-row .btn-apply-now,
  .info-row .btn-join-group {
    width: 100%;
    min-width: auto;
    padding: 12px 20px;
    font-size: 16px;
  }
  
  .share-buttons-row {
    flex-direction: column;
    gap: 12px;
  }
  
  .share-btn {
    width: 100%;
    justify-content: center;
    padding: 12px 24px;
    font-size: 16px;
  }
  
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .btn-explore-more {
    width: 100%;
    text-align: center;
  }
  
  .job-hero-image {
    max-height: 300px;
    margin-bottom: 30px;
  }
  
  .pattern-circles,
  .pattern-elements {
    display: none;
  }
  
  .breadcrumb-nav {
    text-align: left;
    padding-top: 30px;
  }
}

@media (max-width: 576px) {
  .container {
    padding: 0 15px;
  }
  
  .job-title-header h1 {
    font-size: 24px;
  }
  
  .job-main-title {
    font-size: 20px;
  }
  
  .info-row .label-title {
    font-size: 16px;
  }
  
  .info-row .value-text {
    font-size: 14px;
  }
  
  .job-description p {
    font-size: 14px;
    line-height: 1.6;
  }
  
  .card-content {
    padding: 16px;
  }
  
  .job-details-card .card-content {
    padding: 20px 16px 16px;
  }
  
  .detail-item {
    margin-bottom: 20px;
  }
  
  .detail-label,
  .detail-value {
    font-size: 14px;
  }
    .section-title {
    font-size: 24px;
  }
  
  .job-hero-image {
    max-height: 250px;
    margin-bottom: 25px;
  }
  
  .job-content-wrapper {
    padding: 15px 0;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 12px;
  }
  
  .job-title-header h1 {
    font-size: 22px;
  }
  
  .job-main-title {
    font-size: 18px;
  }
  
  .job-hero-image {
    max-height: 220px;
    margin-bottom: 20px;
  }
  
  .info-row {
    padding: 15px 0;
  }
  
  .info-row .btn-full-details,
  .info-row .btn-apply-now,
  .info-row .btn-join-group {
    padding: 10px 16px;
    font-size: 14px;
  }
  
  .share-btn {
    padding: 10px 20px;
    font-size: 14px;
  }
  
  .card-job-title {
    font-size: 18px;
  }
  
  .section-header h2 {
    font-size: 22px;
  }
}

/* Responsive Design for Related Jobs */
@media (max-width: 1200px) {
  .related-jobs-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 992px) {
  .related-jobs-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .section-header {
    margin-bottom: 40px;
  }
}

@media (max-width: 768px) {
  .related-jobs-grid {
    grid-template-columns: 1fr;
  }
  
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
    margin-bottom: 30px;
  }
  
  .btn-explore-more {
    width: 100%;
    text-align: center;
    padding: 12px 20px;
    font-size: 16px;
  }
}

/* Responsive padding for JobDetails */
@media (max-width: 1400px) {
  .job-single-container {
    padding-left: 80px;
    padding-right: 80px;
  }
}

@media (max-width: 768px) {
  .job-single-container {
    padding-left: 30px;
    padding-right: 30px;
  }
}

@media (max-width: 480px) {
  .job-single-container {
    padding-left: 15px;
    padding-right: 15px;
  }
}
