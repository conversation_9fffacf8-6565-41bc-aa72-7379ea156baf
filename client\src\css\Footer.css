.footer {
    background-color: #ffffff;
    margin-top: 0;
    padding-top: 30px;
  }
  
  .footer-container {
    display: flex;
    justify-content: space-between;
    max-width: 1200px;
    margin: 0 auto;
    
  }
  
  /* Brand and Description */
  .footer-brand {
    flex: 1;
    max-width: 300px;
    margin-right: 40px;
  }
  
  .footer-brand .logo {
    margin-bottom: 20px;
  }
  
  .footer-brand .logo img {
    height: 40px;
  }
  
  .footer-description {
    font-size: 14px;
    line-height: 1.6;
    color: #666;
  }
  
  /* Footer Links */
  .footer-links {
    display: flex;
    flex: 2;
    justify-content: space-between;
  }
  
  .footer-column {
    flex: 1;
    padding: 0 15px;
  }
  
  .footer-column h3 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 20px;
    color: #333;
  }
  
  .footer-column ul {
    list-style: none;
    padding: 0;
    margin: 0;
  }
  
  .footer-column ul li {
    margin-bottom: 10px;
  }
  
  .footer-column ul li a {
    text-decoration: none;
    color: #666;
    font-size: 14px;
    transition: color 0.3s ease;
  }
  
  .footer-column ul li a:hover {
    color: #0066cc;
  }
  
  /* Footer Bottom */
  .footer-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 40px auto 0;
    margin-bottom: 10px;
    padding: 20px 20px 0;
    border-top: 1px solid #eee;
  }
  
  .copyright {
    font-size: 14px;
    color: #666;
  }
  
  .copyright a {
    color: #0066cc;
    text-decoration: none;
  }
  
  .social-media {
    display: flex;
  }
    .social-media a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: transparent;
    border: 1px solid #aca9a9;
    margin-left: 10px;
    color: #969696;
    transition: all 0.3s ease;
  }
  
  .social-media a:hover {
    background-color: #666;
    border-color: #666;
    color: white;
  }
  
  /* Responsive Design */
  @media (max-width: 768px) {
    .footer-container {
      flex-direction: column;
    }
    
    .footer-brand {
      max-width: 100%;
      margin-right: 0;
      margin-bottom: 30px;
    }
    
    .footer-links {
      flex-wrap: wrap;
    }
    
    .footer-column {
      flex-basis: 50%;
      margin-bottom: 30px;
    }
  }
  
  @media (max-width: 480px) {
    .footer-column {
      flex-basis: 100%;
    }
    
    .footer-bottom {
      flex-direction: column;
    }
    
    .copyright {
      margin-bottom: 20px;
    }
  }