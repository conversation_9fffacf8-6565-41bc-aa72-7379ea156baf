/* FullDetails.css - Styles for FullDetails component */

.job-single-container {
  background-color: #ffffff;
  min-height: 100vh;
  padding-top: 80px;
  padding-left: 210px;
  padding-right: 210px;
  box-sizing: border-box;
}

.container {
  max-width: 100%;
  margin: 0 auto;
  padding: 0 40px;
}

/* Loading and Error States */
.loading-state,
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 100px 0;
}

.loading-state h2,
.error-state h2 {
  color: #1F2938;
  font-weight: 700;
  margin-bottom: 20px;
}

.error-state a {
  color: #9777FA;
  text-decoration: none;
  padding: 10px 20px;
  border: 1px solid #9777FA;
  border-radius: 5px;
  transition: all 0.2s;
}

.error-state a:hover {
  background-color: #9777FA;
  color: white;
}

/* Header Background */
.job-header-bg {
  background: linear-gradient(135deg, #f9f2ff 0%, #ffefef 100%);
  min-height: 180px;
  position: relative;
  overflow: hidden;
  margin-top: 20px;
  border-radius: 20px;
}

.job-header-bg::before {
  content: "";
  position: absolute;
  top: -50px;
  right: -50px;
  width: 200px;
  height: 200px;
  background: rgba(134, 87, 243, 0.1);
  border-radius: 50%;
  z-index: 0;
}

.job-header-bg::after {
  content: "";
  position: absolute;
  bottom: -100px;
  left: -100px;
  width: 300px;
  height: 300px;
  background: rgba(255, 107, 107, 0.1);
  border-radius: 50%;
  z-index: 0;
}


.breadcrumb-nav {
  padding-top: 30px;
  font-size: 16px;
  color: #A0ABB8;
  text-align: left;
  font-family: 'Open Sans', sans-serif;
  letter-spacing: 0.5%;
}

.breadcrumb-nav a {
  color: #A0ABB8;
  text-decoration: none;
}

.breadcrumb-nav a:hover {
  color: #9777FA;
}

/* Job Title Header */
.job-title-header {
  padding-top: 20px;
  padding-bottom: 40px;
}

.job-title-header h1 {
  font-weight: 700;
  font-size: 30px;
  line-height: 1.22;
  color: #000000;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Content Wrapper */
.job-content-wrapper {
  display: grid;
  grid-template-columns: 4fr 1fr;
  gap: 50px;
  padding: 50px 0;
  max-width: 1400px;
  margin: 0 auto;
}

/* Main Content */
.job-main-content {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

/* Job Hero Image */
.job-hero-image {
  width: 100%;
  max-width: 100%;
  height: auto;
  max-height: 400px;
  object-fit: cover;
  border-radius: 15px;
  margin-bottom: 30px;
}

/* Job Topic */
.job-topic {
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  font-size: 24px;
  color: #9777FA;
  margin-bottom: 20px;
  text-align: center;
}

/* Job Description */
.job-description {
  font-family: 'Open Sans', sans-serif;
  font-size: 16px;
  line-height: 1.8;
  color: #4A5568;
  margin-bottom: 30px;
}

.job-description p {
  margin-bottom: 20px;
}

.job-description p:last-child {
  margin-bottom: 0;
}

/* Back Button */
.back-button {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  background: #9777FA;
  color: white;
  text-decoration: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-family: 'Open Sans', sans-serif;
  font-weight: 600;
  font-size: 16px;
  transition: all 0.2s;
  margin-bottom: 30px;

}

.back-button:hover {
  background: #8366E8;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(151, 119, 250, 0.3);
  color: white;
  text-decoration: none;
}

/* Sidebar */
.job-sidebar {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.sidebar-card {
  background: white;
  border-radius: 10px;
  box-shadow: 5px 4px 12px rgba(0, 0, 0, 0.25);
  overflow: hidden;
}

.job-details-card .card-content {
  padding: 32px 24px 24px;
}

.detail-item {
  display: flex;
  gap: 12px;
  margin-bottom: 30px;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-icon {
  width: 22px;
  height: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 2px;
}

.detail-icon svg {
  color: #A0ABB8;
  font-size: 14px;
}

.detail-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-label {
  font-family: 'Open Sans', sans-serif;
  font-weight: 400;
  font-size: 16px;
  color: #727272;
}

.detail-value {
  font-family: 'Montserrat', sans-serif;
  font-weight: 700;
  font-size: 16px;
  line-height: 1.22;
  color: #1F2938;
}

.card-divider {
  height: 1px;
  background: rgba(6, 18, 36, 0.1);
  margin: 0;
}

/* Follow Section */
.follow-section {
  padding: 0 20px;
}

.follow-social-row {
  display: flex;
  gap: 16px;
  margin-top: 10px;
}

.follow-social-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 38px;
  height: 38px;
  border-radius: 50%;
  font-size: 20px;
  color: #fff;
  background: #bbb;
  transition: background 0.2s, color 0.2s;
  text-decoration: none;
}

.follow-social-icon.facebook { background: #3b5998; }
.follow-social-icon.whatsapp { background: #25d366; }
.follow-social-icon.instagram { background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%); }
.follow-social-icon.tiktok { background: #000; }

.follow-social-icon:hover {
  opacity: 0.85;
  color: #fff;
}

.section-title {
  font-family: 'Montserrat', sans-serif;
  font-weight: 700;
  font-size: 28px;
  line-height: 1.22;
  color: #1F2938;
  margin: 0 0 20px 0;
}

.follow-section .section-line {
  height: 1px;
  background: rgba(6, 18, 36, 0.1);
  margin-bottom: 30px;
}

/* Responsive Design */
@media (max-width: 1440px) {
  .container {
    padding: 0 60px;
  }
  
  .job-content-wrapper {
    max-width: 1200px;
  }
}

@media (max-width: 1200px) {
  .container {
    padding: 0 40px;
  }
  
  .job-content-wrapper {
    grid-template-columns: 1fr;
    gap: 40px;
    max-width: 100%;
  }
  
  .job-sidebar {
    order: -1;
  }
}

@media (max-width: 992px) {
  .container {
    padding: 0 30px;
  }
  
  .job-title-header h1 {
    font-size: 36px;
  }
  
  .job-content-wrapper {
    gap: 30px;
  }
  
  .job-hero-image {
    max-width: 100%;
    max-height: 350px;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 20px;
  }

  .job-header-bg {
    margin-bottom: 20px;
  }

  .job-content-wrapper {
    display: block !important;
    gap: 0 !important;
    padding: 0 !important;
    width: 100% !important;
    max-width: 100% !important;
  }

  .job-sidebar {
    margin-top: 30px;
    width: 100% !important;
  }

  .job-single-container {
    padding-top: 60px;
  }
  
  .job-title-header {
    padding-top: 40px;
    padding-bottom: 30px;
  }
  
  .job-title-header h1 {
    font-size: 28px;
    line-height: 1.3;
  }
  
  .job-topic {
    font-size: 20px;
  }
  
  .job-hero-image {
    max-height: 300px;
    margin-bottom: 30px;
  }
}

@media (max-width: 576px) {
  .container {
    padding: 0 15px;
  }
  
  .job-title-header h1 {
    font-size: 24px;
  }
  
  .job-topic {
    font-size: 18px;
  }
  
  .job-description p {
    font-size: 14px;
    line-height: 1.6;
  }
  
  .card-content {
    padding: 16px;
  }
  
  .job-details-card .card-content {
    padding: 20px 16px 16px;
  }
  
  .detail-item {
    margin-bottom: 20px;
  }
  
  .detail-label,
  .detail-value {
    font-size: 14px;
  }
  
  .section-title {
    font-size: 24px;
  }
  
  .job-hero-image {
    max-height: 250px;
    margin-bottom: 25px;
  }
  
  .job-content-wrapper {
    padding: 15px 0;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 12px;
  }
  
  .job-title-header h1 {
    font-size: 22px;
  }
  
  .job-topic {
    font-size: 16px;
  }
  
  .job-hero-image {
    max-height: 220px;
    margin-bottom: 20px;
  }
  
  .back-button {
    padding: 10px 16px;
    font-size: 14px;
  }
}

/* Responsive padding for FullDetails */
@media (max-width: 1400px) {
  .job-single-container {
    padding-left: 80px;
    padding-right: 80px;
  }
}

@media (max-width: 768px) {
  .job-single-container {
    padding-left: 30px;
    padding-right: 30px;
  }
  /* Back Button */
.back-button {
margin-top: 20px;

}
}

@media (max-width: 480px) {
  .job-single-container {
    padding-left: 15px;
    padding-right: 15px;
  }
}