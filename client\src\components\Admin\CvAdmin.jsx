/* eslint-disable no-unused-vars */
import React, { useState, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faDownload, faTrash, faEye, faSync, faFilter, faSearch, faExclamationTriangle, faFilePdf } from '@fortawesome/free-solid-svg-icons';
import { ApiService } from '../../services/apiService';
import '../../css/CvAdmin.css';
import '../../css/shared-delete-dialog.css';

const CvAdmin = () => {
  const [cvs, setCvs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterOptions, setFilterOptions] = useState({
    gender: ''
  });
  const [selectedCv, setSelectedCv] = useState(null);
  const [showModal, setShowModal] = useState(false);
  
  // Delete confirmation dialog state
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [cvToDelete, setCvToDelete] = useState(null);
  
  const [analytics, setAnalytics] = useState({
    totalSubmissions: 0,
    recentSubmissions: 0,
    genderDistribution: []
  });

  // Fetch CVs from API
  const fetchCvs = async () => {
    setLoading(true);
    try {
      const response = await ApiService.cvs.getAll();
      if (response.data.success) {
        setCvs(response.data.data);
      } else {
        setError('Failed to fetch CV data');
      }
    } catch (err) {
      console.error('Error fetching CVs:', err);
      setError('Network error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Fetch analytics data
  const fetchAnalytics = async () => {
    try {
      const response = await ApiService.cvs.getDashboard();
      if (response.data.success) {
        setAnalytics(response.data.data);
      }
    } catch (err) {
      console.error('Error fetching analytics:', err);
    }
  };

  // Load data on component mount
  useEffect(() => {
    fetchCvs();
    fetchAnalytics();
  }, []);

  // Handle CV deletion
  const handleDeleteCv = async (nationalId) => {
    try {
      const response = await ApiService.cvs.delete(nationalId);
      if (response.data.success) {
        // Remove from state
        setCvs(cvs.filter(cv => cv.national_id !== nationalId));
        alert('CV deleted successfully');
        // Refresh analytics
        fetchAnalytics();
        
        // Reset delete confirmation dialog
        setShowDeleteConfirm(false);
        setCvToDelete(null);
      } else {
        alert('Failed to delete CV');
      }
    } catch (err) {
      console.error('Error deleting CV:', err);
      alert('Network error. Please try again.');
    }
  };

  // Function to show delete confirmation
  const confirmDelete = (cv) => {
    setCvToDelete(cv);
    setShowDeleteConfirm(true);
  };

  // Function to cancel delete
  const cancelDelete = () => {
    setShowDeleteConfirm(false);
    setCvToDelete(null);
  };

  // Handle view CV details
  const handleViewCv = (cv) => {
    setSelectedCv(cv);
    setShowModal(true);
  };

  // Handle search
  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
  };

  // Handle filter change
  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilterOptions({
      ...filterOptions,
      [name]: value
    });
  };

  // Filter CVs based on search term and filters
  const filteredCvs = cvs.filter(cv => {
    // Search term filter
    const searchMatch = 
      cv.full_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      cv.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      cv.national_id?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      cv.phone?.includes(searchTerm) ||
      cv.position?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      cv.company_name?.toLowerCase().includes(searchTerm.toLowerCase());
    
    // Gender filter
    const genderMatch = filterOptions.gender === '' || cv.gender === filterOptions.gender;
    
    return searchMatch && genderMatch;
  });

  return (
    <div className="cv-admin-container">
      {/* Delete Confirmation Dialog */}
      {showDeleteConfirm && (
        <div className="delete-confirm-overlay">
          <div className="delete-confirm-dialog">
            <div className="delete-confirm-header">
              <FontAwesomeIcon icon={faExclamationTriangle} className="delete-icon" />
              <h3>Confirm Deletion</h3>
            </div>
            <div className="delete-confirm-content">
              <p>Are you sure you want to delete this CV?</p>
              <p><strong>{cvToDelete?.full_name}</strong> - {cvToDelete?.national_id}</p>
              <p>This action cannot be undone.</p>
            </div>
            <div className="delete-confirm-actions">
              <button 
                className="cancel-delete-btn" 
                onClick={cancelDelete}
              >
                Cancel
              </button>
              <button 
                className="confirm-delete-btn" 
                onClick={() => handleDeleteCv(cvToDelete.national_id)}
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
      
      <h2>CV Management</h2>
      
      {/* Analytics Cards */}
      <div className="analytics-cards">
        <div className="analytics-card">
          <h3>Total Submissions</h3>
          <p className="analytics-number">{analytics.totalSubmissions}</p>
        </div>
        <div className="analytics-card">
          <h3>Recent Submissions (7 days)</h3>
          <p className="analytics-number">{analytics.recentSubmissions}</p>
        </div>
        <div className="analytics-card">
          <h3>Gender Distribution</h3>
          <div className="analytics-distribution">
            {analytics.genderDistribution.map((item, index) => (
              <div key={index} className="distribution-item">
                <span>{item.gender || 'Unknown'}: </span>
                <span>{item.count}</span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Search and Filter */}
      <div className="cv-controls">
        <div className="search-box">
          <FontAwesomeIcon icon={faSearch} className="search-icon" />
          <input 
            type="text" 
            placeholder="Search by name, email, ID, position, company..." 
            value={searchTerm}
            onChange={handleSearch}
          />
        </div>
        
        <div className="filter-controls">
          <div className="filter-group">
            <label>Gender:</label>
            <select 
              name="gender" 
              value={filterOptions.gender} 
              onChange={handleFilterChange}
            >
              <option value="">All</option>
              <option value="male">Male</option>
              <option value="female">Female</option>
              <option value="other">Other</option>
            </select>
          </div>
          
          <button className="refresh-btn" onClick={fetchCvs}>
            <FontAwesomeIcon icon={faSync} /> Refresh
          </button>
        </div>
      </div>

      {/* CV Table */}
      {loading ? (
        <div className="loading-spinner">Loading...</div>
      ) : error ? (
        <div className="error-message">{error}</div>
      ) : (
        <div className="cv-table-container">
          <table className="cv-table">
            <thead>
              <tr>
                <th>Name</th>
                <th>National ID</th>
                <th>Email</th>
                <th>Phone</th>
                <th>Position</th>
                <th>Company</th>
                <th>Gender</th>
                <th>Submission Date</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {filteredCvs.length > 0 ? (
                filteredCvs.map((cv) => (
                  <tr key={cv.national_id}>
                    <td>{cv.full_name}</td>
                    <td>{cv.national_id}</td>
                    <td>{cv.email}</td>
                    <td>{cv.phone}</td>
                    <td>{cv.position || 'Not specified'}</td>
                    <td>{cv.company_name || 'Not specified'}</td>
                    <td>{cv.gender || 'Not specified'}</td>
                    <td>{new Date(cv.created_at).toLocaleDateString()}</td>
                    <td className="action-buttons">
                      <button 
                        className="view-btn" 
                        onClick={() => handleViewCv(cv)}
                        title="View Details"
                      >
                        <FontAwesomeIcon icon={faEye} />
                      </button>
                      {cv.cv_file_url && (
                        <>
                          <button
                            className="pdf-view-btn"
                            onClick={() => {
                              // Modify URL to prevent download and ensure public access without authentication
                              const viewUrl = cv.cv_file_url.includes('cloudinary.com') 
                                ? cv.cv_file_url.replace('/upload/', '/upload/fl_attachment/')
                                : cv.cv_file_url;
                              window.open(viewUrl, '_blank');
                            }}
                            title="View PDF"
                          >
                            <FontAwesomeIcon icon={faFilePdf} />
                          </button>
                          <a 
                            href={cv.cv_file_url.includes('cloudinary.com')
                              ? cv.cv_file_url.replace('/upload/', '/upload/fl_attachment/')
                              : cv.cv_file_url} 
                            target="_blank" 
                            rel="noopener noreferrer"
                            className="download-btn"
                            title="Download CV"
                          >
                            <FontAwesomeIcon icon={faDownload} />
                          </a>
                        </>
                      )}
                      <button 
                        className="delete-btn" 
                        onClick={() => confirmDelete(cv)}
                        title="Delete CV"
                      >
                        <FontAwesomeIcon icon={faTrash} />
                      </button>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan="9" className="no-data">
                    No CV submissions found
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      )}

      {/* CV Details Modal */}
      {showModal && selectedCv && (
        <div className="modal-backdrop">
          <div className="cv-modal">
            <div className="modal-header">
              <h3>CV Details</h3>
              <button className="close-btn" onClick={() => setShowModal(false)}>×</button>
            </div>
            <div className="modal-content">
              <div className="cv-detail-section">
                <h4>Personal Information</h4>
                <div className="cv-detail-grid">
                  <div className="cv-detail-item">
                    <span className="label">Full Name:</span>
                    <span className="value">{selectedCv.full_name}</span>
                  </div>
                  <div className="cv-detail-item">
                    <span className="label">National ID:</span>
                    <span className="value">{selectedCv.national_id}</span>
                  </div>
                  <div className="cv-detail-item">
                    <span className="label">Gender:</span>
                    <span className="value">{selectedCv.gender || 'Not specified'}</span>
                  </div>

                </div>
              </div>
              
              <div className="cv-detail-section">
                <h4>Contact Information</h4>
                <div className="cv-detail-grid">
                  <div className="cv-detail-item">
                    <span className="label">Email:</span>
                    <span className="value">{selectedCv.email}</span>
                  </div>
                  <div className="cv-detail-item">
                    <span className="label">Phone:</span>
                    <span className="value">{selectedCv.phone}</span>
                  </div>

                </div>
              </div>
              
              <div className="cv-detail-section">
                <h4>Professional Information</h4>
                <div className="cv-detail-grid">
                  <div className="cv-detail-item">
                    <span className="label">Position:</span>
                    <span className="value">{selectedCv.position || 'Not specified'}</span>
                  </div>
                  <div className="cv-detail-item">
                    <span className="label">Company:</span>
                    <span className="value">{selectedCv.company_name || 'Not specified'}</span>
                  </div>
                  <div className="cv-detail-item full-width">
                    <span className="label">Skills:</span>
                    <span className="value">{selectedCv.skills || 'Not provided'}</span>
                  </div>
                </div>
              </div>
              
              <div className="cv-detail-section">
                <h4>CV File</h4>
                <div className="cv-detail-grid">
                  <div className="cv-detail-item full-width">
                    {selectedCv.cv_file_url ? (
                      <div className="cv-file-info">
                        <span className="file-name">{selectedCv.cv_file_name || 'CV File'}</span>
                        <a 
                          href={selectedCv.cv_file_url.includes('cloudinary.com')
                            ? selectedCv.cv_file_url.replace('/upload/', '/upload/fl_attachment/')
                            : selectedCv.cv_file_url} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="download-link"
                        >
                          <FontAwesomeIcon icon={faDownload} /> Download CV
                        </a>
                        <button
                          className="view-pdf-link"
                          onClick={() => {
                            const viewUrl = selectedCv.cv_file_url.includes('cloudinary.com') 
                              ? selectedCv.cv_file_url.replace('/upload/', '/upload/fl_attachment/')
                              : selectedCv.cv_file_url;
                            window.open(viewUrl, '_blank');
                          }}
                        >
                          <FontAwesomeIcon icon={faFilePdf} /> View PDF
                        </button>
                      </div>
                    ) : (
                      <span className="no-file">No CV file uploaded</span>
                    )}
                  </div>
                </div>
              </div>
              
              <div className="cv-detail-section">
                <h4>Additional Information</h4>
                <div className="cv-detail-grid">
                  <div className="cv-detail-item full-width">
                    <span className="label">Notes:</span>
                    <span className="value">{selectedCv.additional_notes || 'No additional notes'}</span>
                  </div>
                  <div className="cv-detail-item">
                    <span className="label">Submitted:</span>
                    <span className="value">{new Date(selectedCv.created_at).toLocaleString()}</span>
                  </div>
                  <div className="cv-detail-item">
                    <span className="label">Last Updated:</span>
                    <span className="value">{new Date(selectedCv.updated_at).toLocaleString()}</span>
                  </div>
                </div>
              </div>
            </div>
            <div className="modal-footer">
              <button className="close-btn" onClick={() => setShowModal(false)}>Close</button>
              <button 
                className="delete-btn" 
                onClick={() => {
                  setShowModal(false);
                  confirmDelete(selectedCv);
                }}
              >
                Delete CV
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CvAdmin;