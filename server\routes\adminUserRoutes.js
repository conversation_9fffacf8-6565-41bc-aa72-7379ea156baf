const express = require('express');
const router = express.Router();
const adminUserController = require('../controllers/adminUserController');
const { authenticate, authorize } = require('../middleware/authMiddleware');

// Public route - Login
router.post('/login', adminUserController.login);

// Protected routes - require authentication

// Get current user profile
router.get('/me', authenticate, adminUserController.getCurrentUser);

// Admin and Superadmin only routes

// Get all users - Admin or Superadmin required
router.get('/', authenticate, authorize('Admin'), adminUserController.getAllUsers);

// Get user by ID - Admin or Superadmin required
router.get('/:admin_id', authenticate, authorize('Admin'), adminUserController.getUserById);

// Create new user - Admin or Superadmin
router.post('/', authenticate, authorize('Admin'), adminUserController.createUser);

// Update user - Admin can update Users/Editors, Superadmin can update anyone
router.put('/:admin_id', authenticate, authorize('Admin'), adminUserController.updateUser);

// Delete user - Superadmin only
router.delete('/:admin_id', authenticate, authorize('Superadmin'), adminUserController.deleteUser);

module.exports = router;