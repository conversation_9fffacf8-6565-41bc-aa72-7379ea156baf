import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import '../css/Cv.css';
import { ApiService } from '../services/apiService';
import PageHelmet from './PageHelmet';

const CvComponent = ({ isOpen, onClose }) => {
  const [form, setForm] = useState({
    whatsapp: '',
    phone: '',
    email: '',
    fullname: '',
    nationalId: '',
    age: '',
    passport: '',
    experience: '',
    gender: '',
    english: 0,
    cv: null,
    company: '',
    position: '',
  });
  const [cvName, setCvName] = useState('');
  const [submitting, setSubmitting] = useState(false);
  const [submitMessage, setSubmitMessage] = useState('');
  const [submitError, setSubmitError] = useState('');
  const [companies, setCompanies] = useState([]);
  const [showCompanyDropdown, setShowCompanyDropdown] = useState(false);
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [connectionChecked, setConnectionChecked] = useState(false);

  // Fetch companies for search functionality
  React.useEffect(() => {
    const fetchCompanies = async () => {
      try {
        const response = await ApiService.companies.getAll();
        if (response.data.success) {
          setCompanies(response.data.data);
        }
      } catch (error) {
        console.error('Error fetching companies:', error);
        // Don't show error to user for company fetch failure
        // Just continue without company suggestions
        setCompanies([]);
      }
    };
    
    // Add a small delay to prevent immediate API call on component mount
    const timer = setTimeout(fetchCompanies, 100);
    
    return () => clearTimeout(timer);
  }, []);

  // Monitor network connectivity
  React.useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      setConnectionChecked(true);
    };
    
    const handleOffline = () => {
      setIsOnline(false);
      setConnectionChecked(true);
    };
    
    // Check initial connection
    setConnectionChecked(true);
    
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const handleChange = (e) => {
    const { name, value, type, files } = e.target;
    if (type === 'file') {
      const file = files[0];
      if (file) {
        // Check file size (5MB = 5 * 1024 * 1024 bytes)
        const maxSize = 5 * 1024 * 1024;
        if (file.size > maxSize) {
          setSubmitError('File size too large. Please upload a CV file smaller than 5MB.');
          // Clear the file input
          e.target.value = '';
          return;
        }
        
        // Check file type
        const allowedTypes = ['.pdf', '.doc', '.docx'];
        const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
        if (!allowedTypes.includes(fileExtension)) {
          setSubmitError('Invalid file type. Please upload a PDF, DOC, or DOCX file.');
          // Clear the file input
          e.target.value = '';
          return;
        }
        
        // Clear any previous error messages
        setSubmitError('');
        setForm({ ...form, cv: file });
        setCvName(file.name);
      } else {
        setForm({ ...form, cv: null });
        setCvName('');
      }
    } else {
      setForm({ ...form, [name]: value });
      
      // Handle company search
      if (name === 'company') {
        setShowCompanyDropdown(value.length > 0);
      }
    }
  };

  const handleCompanySelect = (companyName) => {
    setForm({ ...form, company: companyName });
    setShowCompanyDropdown(false);
  };

  const getFilteredCompanies = () => {
    if (!form.company) return companies;
    return companies.filter(company => 
      company.company_name.toLowerCase().includes(form.company.toLowerCase())
    );
  };

  const handleRadioChange = (name, value) => {
    setForm({ ...form, [name]: value });
  };

  const handleSlider = (e) => {
    setForm({ ...form, english: Number(e.target.value) });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSubmitting(true);
    setSubmitMessage('');
    setSubmitError('');

    try {
      // Check network connectivity
      if (!isOnline) {
        setSubmitError('No internet connection. Please check your network and try again.');
        setSubmitting(false);
        return;
      }
      
      // Validate required fields
      if (!form.fullname || !form.email || !form.phone || !form.nationalId || !form.gender || !form.position) {
        setSubmitError('Please fill in all required fields.');
        setSubmitting(false);
        return;
      }

      // Validate CV file if uploaded
      if (form.cv) {
        // Check file size (5MB = 5 * 1024 * 1024 bytes)
        const maxSize = 5 * 1024 * 1024;
        if (form.cv.size > maxSize) {
          setSubmitError('File size too large. Please upload a CV file smaller than 5MB.');
          setSubmitting(false);
          return;
        }
        
        // Check file type
        const allowedTypes = ['.pdf', '.doc', '.docx'];
        const fileExtension = '.' + form.cv.name.split('.').pop().toLowerCase();
        if (!allowedTypes.includes(fileExtension)) {
          setSubmitError('Invalid file type. Please upload a PDF, DOC, or DOCX file.');
          setSubmitting(false);
          return;
        }
      }

      // Create FormData for file upload
      const formData = new FormData();
      
      // Map form fields to backend expected format
      formData.append('national_id', form.nationalId);
      formData.append('full_name', form.fullname);
      formData.append('email', form.email);
      formData.append('phone', form.phone);
      formData.append('date_of_birth', form.age ? new Date(new Date().getFullYear() - parseInt(form.age), 0, 1).toISOString().split('T')[0] : '');
      formData.append('gender', form.gender);
      formData.append('years_of_experience', form.experience === 'yes' ? 1 : 0);
      formData.append('skills', `English Level: ${form.english}/5`);
      formData.append('additional_notes', `WhatsApp: ${form.whatsapp || 'Not provided'}, Passport: ${form.passport || 'Not specified'}`);
      formData.append('company_name', form.company || '');
      formData.append('position', form.position || '');
      
      // Add CV file if uploaded
      if (form.cv) {
        formData.append('cv_file', form.cv);
      }

      // Submit to backend API
      const response = await ApiService.cvs.submit(formData);

      if (response.data.success) {
        setSubmitMessage(response.data.message);
        // Reset form after successful submission
        setForm({
          whatsapp: '',
          phone: '',
          email: '',
          fullname: '',
          nationalId: '',
          age: '',
          passport: '',
          experience: '',
          gender: '',
          english: 0,
          cv: null,
          company: '',
          position: '',
        });
        setCvName('');
        
        // Close modal after 2 seconds
        setTimeout(() => {
          onClose();
          setSubmitMessage('');
        }, 2000);
      } else {
        setSubmitError(response.data.message || 'Submission failed. Please try again.');
      }
    } catch (error) {
      console.error('CV submission error:', error);
      
      // Handle different types of errors
      if (error.response) {
        // Server responded with error status
        const status = error.response.status;
        const message = error.response.data?.message;
        
        if (status === 413) {
          setSubmitError('File size too large. Please upload a smaller CV file (max 5MB).');
        } else if (status === 400) {
          setSubmitError(message || 'Invalid data submitted. Please check your information.');
        } else if (status === 500) {
          setSubmitError('Server error. Please try again later.');
        } else {
          setSubmitError(message || `Error ${status}: Please try again.`);
        }
      } else if (error.request) {
        // Network error - no response received
        if (error.code === 'ECONNABORTED') {
          setSubmitError('Request timeout. Please check your internet connection and try again.');
        } else {
          setSubmitError('Network error. Please check your internet connection and try again.');
        }
      } else {
        // Other error
        setSubmitError('An unexpected error occurred. Please try again.');
      }
    } finally {
      setSubmitting(false);
    }
  };

  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="cv-modal-backdrop" onClick={handleBackdropClick}>
      <div className="cv-modal-container">
        <PageHelmet 
          title="Apply Now" 
          description="Create a professional CV with our easy-to-use CV builder tool."
        />
        <form className="cv-form-container" onSubmit={handleSubmit}>
          <div className="cv-form-header">
            <div className="cv-form-title">APPLY FOR JOBS</div>
            <button type="button" className="cv-modal-close" onClick={onClose}>
              ×
            </button>
          </div>

          {/* Personal Information Section */}
          <div className="cv-form-section">
            <h3 className="cv-form-section-title">Personal Information</h3>
            
            <div className="cv-form-row">
              <div className="cv-form-group">
                <label className="cv-form-label" htmlFor="fullname">
                  Full Name <span className="required">*</span>
                </label>
                <input
                  className="cv-form-input"
                  type="text"
                  id="fullname"
                  name="fullname"
                  value={form.fullname}
                  onChange={handleChange}
                  placeholder="Enter your full name"
                  required
                />
              </div>
              <div className="cv-form-group">
                <label className="cv-form-label" htmlFor="age">
                  Age <span className="required">*</span>
                </label>
                <input
                  className="cv-form-input"
                  type="number"
                  id="age"
                  name="age"
                  value={form.age}
                  onChange={handleChange}
                  placeholder="Enter your age"
                  min="18"
                  max="65"
                  required
                />
              </div>
            </div>

            <div className="cv-form-group">
              <label className="cv-form-label" htmlFor="nationalId">
                National ID Number <span className="required">*</span>
              </label>
              <input
                className="cv-form-input"
                type="text"
                id="nationalId"
                name="nationalId"
                value={form.nationalId}
                onChange={handleChange}
                placeholder="Enter your national ID number"
                required
              />
            </div>

            <div className="cv-form-group">
              <label className="cv-form-label">
                Gender <span className="required">*</span>
              </label>
              <div className="cv-form-radio-group">
                <div 
                  className={`cv-form-radio-option ${form.gender === 'male' ? 'selected' : ''}`}
                  onClick={() => handleRadioChange('gender', 'male')}
                >
                  <input
                    className="cv-form-radio"
                    type="radio"
                    name="gender"
                    value="male"
                    checked={form.gender === 'male'}
                    onChange={handleChange}
                    required
                  />
                  Male
                </div>
                <div 
                  className={`cv-form-radio-option ${form.gender === 'female' ? 'selected' : ''}`}
                  onClick={() => handleRadioChange('gender', 'female')}
                >
                  <input
                    className="cv-form-radio"
                    type="radio"
                    name="gender"
                    value="female"
                    checked={form.gender === 'female'}
                    onChange={handleChange}
                  />
                  Female
                </div>
              </div>
            </div>
          </div>

          {/* Contact Information Section */}
          <div className="cv-form-section">
            <h3 className="cv-form-section-title">Contact Information</h3>
            
            <div className="cv-form-group">
              <label className="cv-form-label" htmlFor="email">
                Email Address <span className="required">*</span>
              </label>
              <input
                className="cv-form-input"
                type="email"
                id="email"
                name="email"
                value={form.email}
                onChange={handleChange}
                placeholder="Enter your email address"
                required
              />
            </div>

            <div className="cv-form-row">
              <div className="cv-form-group">
                <label className="cv-form-label" htmlFor="phone">
                  Phone Number <span className="required">*</span>
                </label>
                <input
                  className="cv-form-input"
                  type="tel"
                  id="phone"
                  name="phone"
                  value={form.phone}
                  onChange={handleChange}
                  placeholder="Enter your phone number"
                  required
                />
              </div>
              <div className="cv-form-group">
                <label className="cv-form-label" htmlFor="whatsapp">
                  WhatsApp Number
                </label>
                <input
                  className="cv-form-input"
                  type="tel"
                  id="whatsapp"
                  name="whatsapp"
                  value={form.whatsapp}
                  onChange={handleChange}
                  placeholder="Enter your WhatsApp number"
                />
              </div>
            </div>
          </div>

          {/* Job Information Section */}
          <div className="cv-form-section">
            <h3 className="cv-form-section-title">Job Information</h3>
            
            <div className="cv-form-group">
              <label className="cv-form-label" htmlFor="position">
                Position <span className="required">*</span>
              </label>
              <input
                className="cv-form-input"
                type="text"
                id="position"
                name="position"
                value={form.position}
                onChange={handleChange}
                placeholder="Enter the position you're applying for"
                required
              />
            </div>

            <div className="cv-form-group">
              <label className="cv-form-label" htmlFor="company">
                Company (Optional)
              </label>
              <div style={{ position: 'relative' }}>
                <input
                  className="cv-form-input"
                  type="text"
                  id="company"
                  name="company"
                  value={form.company}
                  onChange={handleChange}
                  placeholder="Search for a company or leave blank"
                  onFocus={() => setShowCompanyDropdown(form.company.length > 0)}
                  onBlur={() => setTimeout(() => setShowCompanyDropdown(false), 200)}
                />
                {showCompanyDropdown && getFilteredCompanies().length > 0 && (
                  <div className="cv-company-dropdown">
                    {getFilteredCompanies().slice(0, 5).map((company) => (
                      <div
                        key={company.company_id}
                        className="cv-company-option"
                        onClick={() => handleCompanySelect(company.company_name)}
                      >
                        {company.company_name}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Additional Information Section */}
          <div className="cv-form-section">
            <h3 className="cv-form-section-title">Additional Information</h3>
            
            <div className="cv-form-group">
              <label className="cv-form-label">
                Do you have a passport? <span className="required">*</span>
              </label>
              <div className="cv-form-radio-group">
                <div 
                  className={`cv-form-radio-option ${form.passport === 'yes' ? 'selected' : ''}`}
                  onClick={() => handleRadioChange('passport', 'yes')}
                >
                  <input
                    className="cv-form-radio"
                    type="radio"
                    name="passport"
                    value="yes"
                    checked={form.passport === 'yes'}
                    onChange={handleChange}
                    required
                  />
                  Yes
                </div>
                <div 
                  className={`cv-form-radio-option ${form.passport === 'no' ? 'selected' : ''}`}
                  onClick={() => handleRadioChange('passport', 'no')}
                >
                  <input
                    className="cv-form-radio"
                    type="radio"
                    name="passport"
                    value="no"
                    checked={form.passport === 'no'}
                    onChange={handleChange}
                  />
                  No
                </div>
              </div>
            </div>

            <div className="cv-form-group">
              <label className="cv-form-label">
                Do you have work experience related to this job? <span className="required">*</span>
              </label>
              <div className="cv-form-radio-group">
                <div 
                  className={`cv-form-radio-option ${form.experience === 'yes' ? 'selected' : ''}`}
                  onClick={() => handleRadioChange('experience', 'yes')}
                >
                  <input
                    className="cv-form-radio"
                    type="radio"
                    name="experience"
                    value="yes"
                    checked={form.experience === 'yes'}
                    onChange={handleChange}
                    required
                  />
                  Yes
                </div>
                <div 
                  className={`cv-form-radio-option ${form.experience === 'no' ? 'selected' : ''}`}
                  onClick={() => handleRadioChange('experience', 'no')}
                >
                  <input
                    className="cv-form-radio"
                    type="radio"
                    name="experience"
                    value="no"
                    checked={form.experience === 'no'}
                    onChange={handleChange}
                  />
                  No
                </div>
              </div>
            </div>

            <div className="cv-form-group">
              <label className="cv-form-label">English Knowledge Level</label>
              <div className="cv-form-slider-section">
                <div className="cv-form-slider-group">
                  <span className="cv-form-slider-label">0</span>
                  <input
                    className="cv-form-slider"
                    type="range"
                    name="english"
                    min="0"
                    max="5"
                    step="1"
                    value={form.english}
                    onChange={handleSlider}
                  />
                  <span className="cv-form-slider-label">5</span>
                  <span className="cv-form-slider-value">{form.english}</span>
                </div>
              </div>
            </div>
          </div>

          {/* CV Upload Section */}
          <div className="cv-form-upload-section">
            <div className="cv-form-upload-icon">📄</div>
            <label className="cv-form-upload-label">Upload Your CV</label>
            <span className="cv-form-upload-desc">
              Accepted formats: PDF, DOC, DOCX (Max. File size: 5MB)
            </span>
            <input
              style={{ display: 'none' }}
              type="file"
              id="cv"
              name="cv"
              accept=".pdf,.doc,.docx"
              onChange={handleChange}
            />
            <label htmlFor="cv" className="cv-form-upload-btn">
              {cvName ? 'Change File' : 'Browse Files'}
            </label>
            {cvName && (
              <div className="cv-form-file-name">
                ✅ {cvName}
              </div>
            )}
          </div>

          {/* Network Status Indicator */}
          {connectionChecked && !isOnline && (
            <div className="cv-form-warning-message">
              ⚠️ No internet connection detected. Please check your network before submitting.
            </div>
          )}
          
          {/* Success/Error Messages */}
          {submitMessage && (
            <div className="cv-form-success-message">
              ✅ {submitMessage}
            </div>
          )}
          {submitError && (
            <div className="cv-form-error-message">
              ❌ {submitError}
            </div>
          )}

          <button
            className="cv-form-submit-btn"
            type="submit"
            disabled={submitting || !isOnline}
          >
            {submitting ? (
              <span className="cv-form-loading">Sending Application...</span>
            ) : !isOnline ? (
              'No Internet Connection'
            ) : (
              'Send Application'
            )}
          </button>
        </form>
      </div>
    </div>
  );
};

export default CvComponent;
