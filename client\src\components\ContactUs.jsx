import React, { useState } from "react";
import "../css/ContactUs.css";
import PageHelmet from "./PageHelmet";

const ContactUs = () => {

 const [isSubmitting, setIsSubmitting] = useState(false);
  const [sendError, setSendError] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    enquiry_type: '',
    message: ''
  });

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSendError(false);
    setIsSubmitting(true);

    try {
      const response = await fetch(e.target.action, {
        method: 'POST',
        body: new FormData(e.target),
      });
      
      const result = await response.json();
      
      if (!response.ok || !result.success) {
        throw new Error(result.message || 'Failed to send message');
      }
      
      // Clear form on success
      setFormData({
        name: '',
        email: '',
        phone: '',
        enquiry_type: '',
        message: ''
      });
      
    } catch (error) {
      setSendError(true);
      console.error('Error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };


  return (
    <div className="contact-container">
      <PageHelmet 
        title="Contact Us" 
        description="Get in touch with our team for job postings, support, and inquiries about our job portal services."
      />
      <div className="companies-hero">
        <div className="hero-content">
          <h1>Get in Touch</h1>
          <p>We're here to assist you with any inquiries or partnership opportunities</p>
        </div>
      </div>

      <div className="contact-main">
        <div className="contact-info-panel">
          <div className="info-section">
            <h2 className="info-section-title">Contact Information</h2>
            <p className="info-section-description">Reach out through any of these channels</p>
            
            <div className="info-card">
              <div className="info-icon">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                  <path d="M20.487 17.14l-4.065-3.696a1.001 1.001 0 0 0-1.391.043l-2.393 2.461c-.576-.11-1.734-.471-2.926-1.66-1.192-1.193-1.553-2.354-1.66-2.926l2.459-2.394a1 1 0 0 0 .043-1.391L6.859 3.513a1 1 0 0 0-1.391-.087l-2.17 1.861a1 1 0 0 0-.29.649c-.015.25-.301 6.172 4.291 10.766C11.305 20.707 16.323 21 17.705 21c.203 0 .326-.006.359-.008a.992.992 0 0 0 .648-.291l1.86-2.171a.997.997 0 0 0-.085-1.39z"/>
                </svg>
              </div>
              <div className="info-content">
                <h3>Phone</h3>
                <p>+94 74 016 4763</p>
              </div>
            </div>

            <div className="info-card">
              <div className="info-icon">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                  <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
                </svg>
              </div>
              <div className="info-content">
                <h3>Email</h3>
                <p><EMAIL></p>
              </div>
            </div>

            <div className="info-card">
              <div className="info-icon">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                  <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
                </svg>
              </div>
              <div className="info-content">
                <h3>Corporate Office</h3>
                <p>365 Galle Road,</p>
                <p>Colombo 00300,</p>
                <p>Sri Lanka.</p>
              </div>
            </div>
          </div>

          <div className="business-hours">
            <h3>Business Hours</h3>
            <div className="hours-item">
              <span>Monday - Friday</span>
              <span>9:00 AM - 6:00 PM</span>
            </div>
            <div className="hours-item">
              <span>Saturday</span>
              <span>10:00 AM - 4:00 PM</span>
            </div>
            <div className="hours-item">
              <span>Sunday</span>
              <span>Closed</span>
            </div>
          </div>
        </div>

        <div className="contact-form-panel">
          <div className="form-header">
            <h2>Send Us a Message</h2>
            <p>Complete the form below and our team will respond promptly</p>
          </div>

          <form 
          action="https://api.web3forms.com/submit" 
          method="POST" 
          className="elegant-form"
          onSubmit={handleSubmit}
        >
          {/* Web3Forms required fields */}
          <input type="hidden" name="access_key" value="61991938-411f-488b-ad85-b4c86bef8c04" />
          <input type="hidden" name="subject" value="New message Form jobpage.lk contct us" />
          <input type="checkbox" name="botcheck" className="hidden" style={{display: 'none'}} />

          <div className="form-row">
            <div className="form-group">
              <label htmlFor="full-name">Full Name <span className="required">*</span></label>
              <input 
                type="text" 
                id="full-name" 
                name="name"
                placeholder="Enter your full name" 
                value={formData.name}
                onChange={handleChange}
                required 
              />
              <div className="form-underline"></div>
            </div>
            <div className="form-group">
              <label htmlFor="email">Email Address <span className="required">*</span></label>
              <input 
                type="email" 
                id="email" 
                name="email"
                value={formData.email}
                onChange={handleChange}
                placeholder="<EMAIL>" 
                required 
              />
              <div className="form-underline"></div>
            </div>
          </div>

          <div className="form-row">
            <div className="form-group">
              <label htmlFor="phone">Phone Number</label>
              <input 
                type="tel" 
                id="phone" 
                name="phone"
                value={formData.phone}
                onChange={handleChange}
                placeholder="076 xxx xxxx" 
              />
              <div className="form-underline"></div>
            </div>
            <div className="form-group">
              <label htmlFor="subject">Subject <span className="required">*</span></label>
              <select id="subject" name="enquiry_type" required>
                <option value="">Select a subject</option>
                <option value="genePost a New Job">Post a New Job</option>
                <option value="Partnership Opportunity">Partnership Opportunity</option>
                <option value="Jobpage SMS Alert Service">Jobpage SMS Alert Service</option>
                <option value="Technical Support">Technical Support</option>
                <option value="Careers at jobpage.lk">Careers at jobpage.lk</option>
                <option value="other">Other</option>
              </select>
              <div className="form-underline"></div>
            </div>
          </div>

          <div className="form-group">
            <label htmlFor="message">Your Message <span className="required">*</span></label>
            <textarea 
              id="message" 
              name="message"
              placeholder="Please provide details about your inquiry..." 
              rows="5" 
              value={formData.message}
              onChange={handleChange}
              required
            ></textarea>
            <div className="form-underline"></div>
          </div>

          <div className="form-footer">
            <button 
              type="submit" 
              className={`submit-button ${sendError ? 'error-button' : ''}`}
              disabled={isSubmitting}
            >
              <span>
                {isSubmitting ? 'Sending...' : sendError ? 'Try Again' : 'Send Message'}
              </span>
              {!isSubmitting && !sendError && (
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                  <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                </svg>
              )}
            </button>
            {sendError && (
              <p className="error-message" style={{marginTop: '10px'}}>Failed to send message. Please try again.</p>
            )}
            <p className="form-disclaimer">
              By submitting this form, you agree to our <a href="/privacy">privacy policy</a>.
            </p>
          </div>
        </form>
        </div>
      </div>
    </div>
  );
};

export default ContactUs;