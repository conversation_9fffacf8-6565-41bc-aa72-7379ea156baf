const AdminUser = require('../models/adminUserModel');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');

// Get all admin users
const getAllUsers = (req, res) => {
  AdminUser.getAll((err, users) => {
    if (err) {
      console.error('Error fetching admin users:', err);
      return res.status(500).json({ error: 'Database error' });
    }
    res.json(users);
  });
};

// Get admin user by ID
const getUserById = (req, res) => {
  const adminId = req.params.admin_id;
  
  AdminUser.getById(adminId, (err, user) => {
    if (err) {
      console.error('Error fetching admin user:', err);
      return res.status(500).json({ error: 'Database error' });
    }
    
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }
    
    res.json(user);
  });
};

// Create a new admin user
const createUser = async (req, res) => {
  try {
    const { admin_username, admin_password, admin_email, role } = req.body;
    
    // Validate input
    if (!admin_username || !admin_password || !admin_email) {
      return res.status(400).json({ error: 'Please provide username, password, and email' });
    }
    
    // Check if username already exists
    AdminUser.getByUsername(admin_username, async (err, existingUser) => {
      if (err) {
        console.error('Error checking username:', err);
        return res.status(500).json({ error: 'Database error' });
      }
      
      if (existingUser) {
        return res.status(400).json({ error: 'Username already exists' });
      }
      
      // Hash password
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash(admin_password, salt);
      
      // Create user with hashed password
      const userData = {
        admin_username,
        admin_password: hashedPassword,
        admin_email,
        role: role || 'User' // Default role if not specified
      };
      
      AdminUser.create(userData, (err, newUser) => {
        if (err) {
          console.error('Error creating user:', err);
          return res.status(500).json({ error: 'Could not create user' });
        }
        
        // Don't return the password
        const { admin_password, ...userWithoutPassword } = newUser;
        res.status(201).json(userWithoutPassword);
      });
    });
  } catch (error) {
    console.error('Server error:', error);
    res.status(500).json({ error: 'Server error' });
  }
};

// Update an admin user
const updateUser = async (req, res) => {
  try {
    const adminId = req.params.admin_id;
    const { admin_username, admin_password, admin_email, role } = req.body;
    
    // Prepare update data
    const updateData = {};
    
    if (admin_username) updateData.admin_username = admin_username;
    if (admin_email) updateData.admin_email = admin_email;
    if (role) updateData.role = role;
    
    // If password is provided, hash it
    if (admin_password) {
      const salt = await bcrypt.genSalt(10);
      updateData.admin_password = await bcrypt.hash(admin_password, salt);
    }
    
    // Update user
    AdminUser.update(adminId, updateData, (err, updatedUser) => {
      if (err) {
        console.error('Error updating user:', err);
        return res.status(500).json({ error: 'Could not update user' });
      }
      
      if (!updatedUser) {
        return res.status(404).json({ error: 'User not found' });
      }
      
      // Don't return the password
      const { admin_password, ...userWithoutPassword } = updatedUser;
      res.json(userWithoutPassword);
    });
  } catch (error) {
    console.error('Server error:', error);
    res.status(500).json({ error: 'Server error' });
  }
};

// Delete an admin user
const deleteUser = (req, res) => {
  const adminId = req.params.admin_id;
  
  // Prevent deleting your own account
  if (req.user && req.user.admin_id.toString() === adminId) {
    return res.status(400).json({ error: 'Cannot delete your own account' });
  }
  
  AdminUser.delete(adminId, (err, result) => {
    if (err) {
      console.error('Error deleting user:', err);
      return res.status(500).json({ error: 'Could not delete user' });
    }
    
    if (!result.deleted) {
      return res.status(404).json({ error: 'User not found' });
    }
    
    res.json({ message: 'User deleted successfully' });
  });
};

// User login
const login = (req, res) => {
  const { username, password } = req.body;
  
  // Validate input
  if (!username || !password) {
    return res.status(400).json({ error: 'Please provide username and password' });
  }
  
  // Check if user exists (by username or email)
  AdminUser.getByUsernameOrEmail(username, async (err, user) => {
    if (err) {
      console.error('Login error:', err);
      return res.status(500).json({ error: 'Database error' });
    }
    
    if (!user) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }
    
    // Check password
    const isMatch = await bcrypt.compare(password, user.admin_password);
    
    if (!isMatch) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }
    
    // Create JWT payload
    const payload = {
      admin_id: user.admin_id,
      username: user.admin_username,
      role: user.role
    };
    
    // Sign token
    jwt.sign(
      payload,
      process.env.JWT_SECRET || 'your_jwt_secret',
      { expiresIn: '1d' },
      (err, token) => {
        if (err) throw err;
        res.json({
          token,
          user: {
            admin_id: user.admin_id,
            admin_username: user.admin_username,
            admin_email: user.admin_email,
            role: user.role
          }
        });
      }
    );
  });
};

// Get current user profile
const getCurrentUser = (req, res) => {
  AdminUser.getById(req.user.admin_id, (err, user) => {
    if (err) {
      console.error('Error fetching current user:', err);
      return res.status(500).json({ error: 'Database error' });
    }
    
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }
    
    // Don't return the password
    const { admin_password, ...userWithoutPassword } = user;
    res.json(userWithoutPassword);
  });
};

module.exports = {
  getAllUsers,
  getUserById,
  createUser,
  updateUser,
  deleteUser,
  login,
  getCurrentUser
};