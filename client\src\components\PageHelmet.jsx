import React, { useEffect } from 'react';
import { Helmet } from 'react-helmet-async';

const PageHelmet = ({ title, description }) => {
  const siteTitle = 'Job Page';
  const fullTitle = title ? `${siteTitle} | ${title}` : siteTitle;
  
  // Force update the document title with the current title prop
  useEffect(() => {
    document.title = fullTitle;
  }, [fullTitle]);
  
  return (
    <Helmet>
      <title>{fullTitle}</title>
      {description && <meta name="description" content={description} />}
    </Helmet>
  );
};

export default PageHelmet; 