/* Delete Confirmation Dialog Styles */
.delete-confirm-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(2px);
}

.delete-confirm-dialog {
  background-color: white;
  border-radius: 8px;
  width: 90%;
  max-width: 450px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  animation: dialog-appear 0.3s ease;
}

.delete-confirm-header {
  background-color: #f8d7da;
  color: #842029;
  padding: 16px 20px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #f5c2c7;
}

.delete-confirm-header .delete-icon {
  font-size: 1.5rem;
  margin-right: 10px;
  color: #dc3545;
}

.delete-confirm-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.delete-confirm-content {
  padding: 20px;
  text-align: center;
}

.delete-confirm-content p {
  margin: 8px 0;
  color: #333;
}

.delete-confirm-content p:first-child {
  font-weight: 500;
}

.delete-confirm-content strong {
  color: #212529;
  font-weight: 600;
}

.delete-confirm-actions {
  display: flex;
  justify-content: flex-end;
  padding: 16px 20px;
  background-color: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.delete-confirm-actions button {
  padding: 8px 16px;
  margin-left: 10px;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
}

.cancel-delete-btn {
  background-color: #e9ecef;
  color: #495057;
}

.cancel-delete-btn:hover {
  background-color: #dee2e6;
}

.confirm-delete-btn {
  background-color: #dc3545;
  color: white;
}

.confirm-delete-btn:hover {
  background-color: #c82333;
}

@keyframes dialog-appear {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
} 