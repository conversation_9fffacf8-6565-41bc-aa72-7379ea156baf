/* CV Admin Styles */

.cv-admin-container {
  padding: 20px;
}

.cv-admin-container h2 {
  margin-bottom: 20px;
  color: #333;
  font-size: 24px;
}

/* Analytics Cards */
.analytics-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.analytics-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s;
}

.analytics-card:hover {
  transform: translateY(-5px);
}

.analytics-card h3 {
  font-size: 16px;
  color: #666;
  margin-bottom: 10px;
}

.analytics-number {
  font-size: 28px;
  font-weight: bold;
  color: #2c3e50;
}

.analytics-distribution {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.distribution-item {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
}

/* Search and Filter Controls */
.cv-controls {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  gap: 15px;
}

.search-box {
  position: relative;
  flex: 1;
  min-width: 250px;
}

.search-box input {
  width: 100%;
  padding: 10px 15px 10px 40px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.search-icon {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
}

.filter-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  align-items: center;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-group label {
  font-size: 14px;
  color: #666;
}

.filter-group select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
  font-size: 14px;
}

.refresh-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background-color: #f8f9fa;
  color: #495057;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.refresh-btn:hover {
  background-color: #e9ecef;
}

.refresh-btn:active {
  background-color: #dae0e5;
}

.refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Add a spin animation for the refresh icon when loading */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.refresh-btn svg {
  transition: transform 0.2s ease;
}

.refresh-btn:active svg {
  animation: spin 1s linear infinite;
}

/* CV Table */
.cv-table-container {
  overflow-x: auto;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.cv-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.cv-table th,
.cv-table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.cv-table th {
  background-color: #f8f9fa;
  color: #333;
  font-weight: 600;
}

.cv-table tr:hover {
  background-color: #f8f9fa;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.view-btn,
.download-btn,
.delete-btn,
.pdf-view-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.view-btn {
  color: #3498db;
}

.download-btn {
  color: #27ae60;
}

.pdf-view-btn {
  color: #f39c12; /* Changed from red to orange/amber */
}

.delete-btn {
  color: #e74c3c;
}

.view-btn:hover,
.download-btn:hover,
.delete-btn:hover,
.pdf-view-btn:hover {
  background-color: #f1f1f1;
}

.no-data {
  text-align: center;
  color: #999;
  padding: 30px 0;
}

/* Loading and Error States */
.loading-spinner,
.error-message {
  text-align: center;
  padding: 30px;
  color: #666;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.error-message {
  color: #e74c3c;
}

/* Modal Styles */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.cv-modal {
  background-color: #fff;
  border-radius: 8px;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
}

.modal-header h3 {
  margin: 0;
  color: #333;
}

.modal-content {
  padding: 20px;
  overflow-y: auto;
}

.modal-footer {
  padding: 15px 20px;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.close-btn {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #999;
  transition: color 0.2s;
}

.close-btn:hover {
  color: #333;
}

.modal-footer .close-btn {
  font-size: 14px;
  padding: 8px 15px;
  background-color: #f8f9fa;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.modal-footer .delete-btn {
  font-size: 14px;
  padding: 8px 15px;
  background-color: #e74c3c;
  color: white;
  border: none;
  border-radius: 4px;
  width: auto;
  height: auto;
}

.modal-footer .delete-btn:hover {
  background-color: #c0392b;
}

/* CV Detail Sections */
.cv-detail-section {
  margin-bottom: 25px;
}

.cv-detail-section h4 {
  margin-bottom: 15px;
  color: #2c3e50;
  font-size: 18px;
  border-bottom: 1px solid #eee;
  padding-bottom: 8px;
}

.cv-detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
}

.cv-detail-item {
  display: flex;
  flex-direction: column;
}

.cv-detail-item.full-width {
  grid-column: 1 / -1;
}

.cv-detail-item .label {
  font-size: 12px;
  color: #666;
  margin-bottom: 5px;
}

.cv-detail-item .value {
  font-size: 14px;
  color: #333;
}

.cv-file-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #f8f9fa;
  padding: 10px 15px;
  border-radius: 4px;
  border: 1px solid #eee;
  flex-wrap: wrap;
  gap: 10px;
}

.file-name {
  font-size: 14px;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 300px;
}

.download-link {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #3498db;
  text-decoration: none;
  font-size: 14px;
  white-space: nowrap;
}

.download-link:hover {
  text-decoration: underline;
}

.view-pdf-link {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #f39c12;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 14px;
  white-space: nowrap;
  padding: 0;
}

.view-pdf-link:hover {
  text-decoration: underline;
}

.no-file {
  color: #999;
  font-style: italic;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .cv-controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-box {
    margin-bottom: 10px;
  }
  
  .filter-controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .filter-group {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .filter-group select {
    width: 100%;
  }
  
  .refresh-btn {
    width: 100%;
    justify-content: center;
  }
  
  .cv-detail-grid {
    grid-template-columns: 1fr;
  }
  
  .cv-file-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .file-name {
    max-width: 100%;
  }
}