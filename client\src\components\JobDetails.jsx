/* eslint-disable jsx-a11y/anchor-is-valid */
import React, { useState, useEffect } from "react";
import { useParams, Link } from "react-router-dom";
import ApiService from "../services/apiService";
import "../css/JobDetails.css";
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faBriefcase, 
  faMapMarkerAlt, 
  faDollarSign, 
  faClock,
  faCalendarAlt,
  faBookmark,
  faShieldAlt
} from '@fortawesome/free-solid-svg-icons';
import { faFacebookF, faWhatsapp, faInstagram, faTiktok } from '@fortawesome/free-brands-svg-icons';
import jobdetails from "../assets/jobdetails.png";
import JobCard from './JobCard';
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import PageHelmet from './PageHelmet';

const JobDetails = () => {
  const { jobId } = useParams();
  const [job, setJob] = useState(null);
  const [relatedJobs, setRelatedJobs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);

  useEffect(() => {
    // Updated to scroll to top immediately when component mounts
    window.scrollTo({ top: 0, behavior: 'instant' });

    // Add window resize listener to detect mobile view
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 768);
    };
    
    window.addEventListener('resize', handleResize);
    
    const fetchJobDetails = async () => {
      try {
        setLoading(true);
        const response = await ApiService.jobs.getById(jobId);
        setJob(response.data);
        setError(null);
          // Fetch related jobs based on the same job type (main_topics)
        if (response.data.main_topics) {
          await fetchRelatedJobs(response.data.main_topics, response.data.id);
        }
      } catch (err) {
        // Error fetching job details
        setError("Failed to load job details");
      } finally {
        setLoading(false);
      }
    };    const fetchRelatedJobs = async (jobType, currentJobId) => {
      try {
        const response = await ApiService.jobs.getByCategory(jobType, 6);
          // Filter out the current job and limit to 3 jobs
        // Check both 'id' and 'job_id' properties to handle different data formats
        const filtered = response.data.filter(job => {
          const jobId = job.id || job.job_id;
          const isCurrentJob = String(jobId) === String(currentJobId); // Convert both to strings for comparison
          return !isCurrentJob;
        }).slice(0, 3);
        
        setRelatedJobs(filtered);
      } catch (err) {
        // Error fetching related jobs
        setRelatedJobs([]);
      }
    };

    if (jobId) {
      fetchJobDetails();
    }
    
    // Clean up event listener
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [jobId]);
  const formatSalary = (job) => {
    if (job.min_salary && job.max_salary) {
      return `$${job.min_salary} - $${job.max_salary}`;
    } else if (job.min_salary) {
      return `$${job.min_salary}+`;
    } else if (job.max_salary) {
      return `Up to $${job.max_salary}`;
    }
    return 'Negotiable';
  };

  const formatJobType = (job) => {
    const jobType = job.job_type || 'Full Time';
    const mainTopic = job.main_topics || '';
    return `${jobType}${mainTopic ? ` / ${mainTopic}` : ''}`;
  };

  const formatDatePosted = (startDate) => {
    if (!startDate) return 'Recently posted';
    
    const posted = new Date(startDate);
    const now = new Date();
    const diffTime = Math.abs(now - posted);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 1) return '1 day ago';
    if (diffDays < 7) return `${diffDays} days ago`;
    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} weeks ago`;
    return posted.toLocaleDateString();
  };

  const formatExpirationDate = (endDate) => {
    if (!endDate) return 'Open until filled';
    
    const expiration = new Date(endDate);
    return expiration.toLocaleDateString();
  };

  // Slider settings for mobile carousel
  const sliderSettings = {
    dots: true,
    infinite: false,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
    arrows: false,
    swipeToSlide: true,
    adaptiveHeight: true
  };

  if (loading) {
    return (
      <div className="job-single-container">
        <PageHelmet title="Loading Job Details" />
        <div className="loading-state">
          <h2>Loading job details...</h2>
        </div>
      </div>
    );
  }

  if (error || !job) {
    return (
      <div className="job-single-container">
        <PageHelmet title="Job Not Found" />
        <div className="error-state">
          <h2>Error: {error || "Job not found"}</h2>
          <Link to="/">Return to homepage</Link>
        </div>
      </div>
    );
  }

  return (
    <div className="job-single-container">
      <PageHelmet 
        title={job.job_title} 
        description={`${job.job_title} at ${job.company_name} - ${job.location || 'Various Locations'}`}
      />
      {/* Header Background */}
      <div className="job-header-bg">
        {/* Breadcrumb */}
        <div className="container">
          <div className="breadcrumb-nav">
            <Link to="/">Home</Link> / <span>Jobs listing</span>
          </div>
          
          {/* Job Title */}
          <div className="job-title-header">
            <h1>{job.job_title || "Senior UI / UX Designer"}</h1>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container">
        <div className="job-content-wrapper">
          {/* Left Content */}
          <div className="job-main-content">
            {/* Job Hero Image */}
            <div className="job-hero-image">
              <img src={job.job_post_image || jobdetails} alt="Job Details" />
            </div>
            {/* Job Content */}            <div className="job-content-section">
              <h2 className="job-main-title">
                {job.job_title} - {job.company_name} Job Vacancies {new Date(job.start_date).getFullYear()}
              </h2>
              {/* Job Info with Inline Actions */}
              <div className="job-info-with-actions">
                <div className="info-row">
                  <span className="label-title">Job Title</span>
                  <span className="value-text">{job.job_title}</span>
                </div>
                
                <div className="info-row">
                  <span className="label-title">Company/Institution</span>
                  <span className="value-text">{job.company_name}, Sri Lanka.</span>
                </div>
                
                <div className="info-row">
                  <span className="label-title">Full details</span>
                  <Link to={`/job/${jobId}/full-details`} className="btn-full-details" style={{ textDecoration: 'none', display: 'inline-block' }}>
                    Full Details
                  </Link>
                </div>
                
                <div className="info-row">
                  <span className="label-title">Send CV</span>
                  {job.send_cv_email ? (
                    <a 
                      href={
                        job.send_cv_email.includes('@') 
                          ? `mailto:${job.send_cv_email}?subject=Application for ${job.job_title} - ${job.company_name}&body=Dear Hiring Manager,%0D%0A%0D%0AI am writing to express my interest in the ${job.job_title} position at ${job.company_name}.%0D%0A%0D%0APlease find my CV attached.%0D%0A%0D%0AThank you for your consideration.%0D%0A%0D%0ABest regards`
                          : job.send_cv_email.startsWith('http') 
                            ? job.send_cv_email 
                            : `https://${job.send_cv_email}`
                      }
                      className="btn-apply-now"
                      style={{ textDecoration: 'none', display: 'inline-block' }}
                      target={job.send_cv_email.includes('@') ? '_self' : '_blank'}
                      rel={job.send_cv_email.includes('@') ? '' : 'noopener noreferrer'}
                    >
                      Apply now
                    </a>
                  ) : (
                    <button className="btn-apply-now" disabled>
                      Apply now
                    </button>
                  )}
                </div>
                
                <div className="info-row">
                  <span className="label-title">Whatsapp Group</span>
                  <a 
                    href={(() => {
                      // Determine WhatsApp channel based on job type
                      const jobType = job.main_topics ? job.main_topics.toLowerCase() : '';
                      
                      if (jobType.includes('government') || jobType.includes('govt')) {
                        return "https://whatsapp.com/channel/0029Vb268Am9WtCBjjcHaZ0D";
                      } else if (jobType.includes('intern') || jobType.includes('internship')) {
                        return "https://whatsapp.com/channel/0029Vb7JjeP3WHTS1NMbnt2s";
                      } else if (jobType.includes('private')) {
                        return "https://whatsapp.com/channel/0029Vb6PMrQIyPtZoQfkuM2z";
                      } else if (jobType.includes('foreign') || jobType.includes('abroad')) {
                        return "https://whatsapp.com/channel/0029Vb6zfPK4tRroHoqVJ73n";
                      } else {
                        // Default channel if no match
                        return "https://whatsapp.com/channel/0029Vb7JjeP3WHTS1NMbnt2s";
                      }
                    })()}
                    className="btn-join-group"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    Join Group
                  </a>
                </div>
              </div>

              {/* Social Share */}
              <div className="social-share-section">
                <div className="share-buttons-row">
                  <a
                    href="https://whatsapp.com/channel/0029Vb7JjeP3WHTS1NMbnt2s"
                    className="share-btn share-btn-modern whatsapp"
                    target="_blank"
                    rel="noopener noreferrer"
                    aria-label="Share on WhatsApp"
                  >
                    <FontAwesomeIcon icon={faWhatsapp} className="share-btn-icon" />
                    WhatsApp
                  </a>
                  <a
                    href="https://www.tiktok.com/@jobpage.lk?is_from_webapp=1&sender_device=pc"
                    className="share-btn share-btn-modern tiktok"
                    target="_blank"
                    rel="noopener noreferrer"
                    aria-label="Share on TikTok"
                  >
                    <FontAwesomeIcon icon={faTiktok} className="share-btn-icon" />
                    TikTok
                  </a>
                  <a
                    href="https://www.facebook.com/profile.php?id=61564913969342"
                    className="share-btn share-btn-modern facebook"
                    target="_blank"
                    rel="noopener noreferrer"
                    aria-label="Share on Facebook"
                  >
                    <FontAwesomeIcon icon={faFacebookF} className="share-btn-icon" />
                    Facebook
                  </a>
                  <a
                    href="https://www.instagram.com/jobpagelk/"
                    className="share-btn share-btn-modern instagram"
                    target="_blank"
                    rel="noopener noreferrer"
                    aria-label="Share on Instagram"
                  >
                    <FontAwesomeIcon icon={faInstagram} className="share-btn-icon" />
                    Instagram
                  </a>
                </div>
              </div>
            </div>

            {/* Related Jobs */}
            <div className="related-jobs-section">
              {/* <div className="section-header">
                <h2>Related Jobs</h2>
                <div className="section-line"></div>
                <button className="btn-explore-more">Explore more</button>
              </div>
               */}
              {/* Related Job Cards - Grid for desktop, Slider for mobile */}
              {relatedJobs.length > 0 ? (
                isMobile ? (
                  <div className="related-jobs-slider-container">
                    <Slider {...sliderSettings} className="related-jobs-slider">
                      {relatedJobs.map((relatedJob) => {
                        const jobId = relatedJob.id || relatedJob.job_id;
                        console.log('Rendering slider job:', jobId, relatedJob.job_title);
                        return (
                          <div key={jobId} className="slider-job-card-wrapper">
                            <JobCard 
                              id={jobId}
                              hot={false}
                              image={relatedJob.job_post_image || jobdetails}
                              logo={relatedJob.company_logo || "https://via.placeholder.com/40"}
                              company={relatedJob.company_name}
                              position={relatedJob.job_title}
                              location={relatedJob.main_topics || "Sri Lanka"}
                              workTime={relatedJob.job_type || "Full Time"}
                              salary={formatSalary(relatedJob)}
                              postedTime={formatDatePosted(relatedJob.start_date)}
                            />
                          </div>
                        );
                      })}
                    </Slider>
                  </div>
                ) : (
                  <div className="related-jobs-grid">
                    {relatedJobs.map((relatedJob) => {
                      const jobId = relatedJob.id || relatedJob.job_id;
                      console.log('Rendering grid job:', jobId, relatedJob.job_title);
                      return (
                        <JobCard 
                          key={jobId}
                          id={jobId}
                          hot={false}
                          image={relatedJob.job_post_image || jobdetails}
                          logo={relatedJob.company_logo || "https://via.placeholder.com/40"}
                          company={relatedJob.company_name}
                          position={relatedJob.job_title}
                          location={relatedJob.main_topics || "Sri Lanka"}
                          workTime={relatedJob.job_type || "Full Time"}
                          salary={formatSalary(relatedJob)}
                          postedTime={formatDatePosted(relatedJob.start_date)}
                        />
                      );
                    })}
                  </div>
                )
              ) : (
                <div className="no-related-jobs">
                  <p>No related jobs found in this category.</p>
                </div>
              )}
            </div>
          </div>

          {/* Right Sidebar */}
          <div className="job-sidebar">
            {/* Job Details Card */}
            <div className="sidebar-card job-details-card">
              <div className="card-content">                {/* Job Type */}
                <div className="detail-item">
                  <div className="detail-icon">
                    <FontAwesomeIcon icon={faBriefcase} />
                  </div>
                  <div className="detail-content">
                    <span className="detail-label">Job Type</span>
                    <span className="detail-value">{formatJobType(job)}</span>
                  </div>
                </div>

                {/* Location */}
                <div className="detail-item">
                  <div className="detail-icon">
                    <FontAwesomeIcon icon={faMapMarkerAlt} />
                  </div>
                  <div className="detail-content">
                    <span className="detail-label">Location</span>
                    <span className="detail-value">{job.company_name ? `${job.company_name}, Sri Lanka` : 'Sri Lanka'}</span>
                  </div>
                </div>

                {/* Salary */}
                <div className="detail-item">
                  <div className="detail-icon">
                    <FontAwesomeIcon icon={faDollarSign} />
                  </div>
                  <div className="detail-content">
                    <span className="detail-label">Salary</span>
                    <span className="detail-value">{formatSalary(job)}</span>
                  </div>
                </div>

                {/* Date Posted */}
                <div className="detail-item">
                  <div className="detail-icon">
                    <FontAwesomeIcon icon={faClock} />
                  </div>
                  <div className="detail-content">
                    <span className="detail-label">Date posted</span>
                    <span className="detail-value">{formatDatePosted(job.start_date)}</span>
                  </div>
                </div>

                {/* Expiration Date */}
                <div className="detail-item">
                  <div className="detail-icon">
                    <FontAwesomeIcon icon={faCalendarAlt} />
                  </div>
                  <div className="detail-content">
                    <span className="detail-label">Expiration date</span>
                    <span className="detail-value">{formatExpirationDate(job.end_date)}</span>
                  </div>
                </div>
              </div>
              <div className="card-divider"></div>            </div>

            {/* Follow Us Section */}
            <div className="follow-section">
              <h3 className="section-title">FOLLOW US</h3>
              <div className="section-line"></div>
              <div className="follow-social-row">
                <a
                  href="https://www.facebook.com/profile.php?id=61564913969342"
                  className="follow-social-icon facebook"
                  target="_blank"
                  rel="noopener noreferrer"
                  aria-label="Facebook"
                >
                  <FontAwesomeIcon icon={faFacebookF} />
                </a>
                <a
                  href={(() => {
                    // Determine WhatsApp channel based on job type
                    const jobType = job.main_topics ? job.main_topics.toLowerCase() : '';
                    
                    if (jobType.includes('government') || jobType.includes('govt')) {
                      return "https://whatsapp.com/channel/0029Vb268Am9WtCBjjcHaZ0D";
                    } else if (jobType.includes('intern') || jobType.includes('internship')) {
                      return "https://whatsapp.com/channel/0029Vb7JjeP3WHTS1NMbnt2s";
                    } else if (jobType.includes('private')) {
                      return "https://whatsapp.com/channel/0029Vb6PMrQIyPtZoQfkuM2z";
                    } else if (jobType.includes('foreign') || jobType.includes('abroad')) {
                      return "https://whatsapp.com/channel/0029Vb6zfPK4tRroHoqVJ73n";
                    } else {
                      // Default channel if no match
                      return "https://whatsapp.com/channel/0029Vb7JjeP3WHTS1NMbnt2s";
                    }
                  })()}
                  className="follow-social-icon whatsapp"
                  target="_blank"
                  rel="noopener noreferrer"
                  aria-label="WhatsApp"
                >
                  <FontAwesomeIcon icon={faWhatsapp} />
                </a>
                <a
                  href="https://www.instagram.com/jobpagelk/"
                  className="follow-social-icon instagram"
                  target="_blank"
                  rel="noopener noreferrer"
                  aria-label="Instagram"
                >
                  <FontAwesomeIcon icon={faInstagram} />
                </a>
                <a
                  href="https://www.tiktok.com/@jobpage.lk?is_from_webapp=1&sender_device=pc"
                  className="follow-social-icon tiktok"
                  target="_blank"
                  rel="noopener noreferrer"
                  aria-label="TikTok"
                >
                  <FontAwesomeIcon icon={faTiktok} />
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default JobDetails;
