const multer = require('multer');
const path = require('path');

// Configure storage
const storage = multer.memoryStorage();

// Handle blog image uploads with memory storage for processing in controllers
const blogImageUpload = multer({
  storage: storage,
  limits: { fileSize: 5 * 1024 * 1024 }, // 5MB limit
  fileFilter: (req, file, cb) => {
    // Skip validation if no file is selected or file is empty
    if (!file || !file.originalname) {
      return cb(null, true);
    }
    
    // Accept only image file formats
    const allowedMimeTypes = [
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp'
    ];
    
    const allowedExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp'];
    const fileExtension = path.extname(file.originalname).toLowerCase();
    
    if (allowedMimeTypes.includes(file.mimetype) && allowedExtensions.includes(fileExtension)) {
      cb(null, true);
    } else {
      cb(new Error('Only JPG, JPEG, PNG, GIF, and WEBP images are allowed'), false);
    }
  }
});

// Export the middleware
module.exports = blogImageUpload;