const db = require('../config/db');

// Helper function to sanitize text for the database
const sanitizeText = (text) => {
  if (!text) return '';
  
  // Modified sanitization to preserve unicode characters for multilingual support
  // but remove emoji characters that cause MySQL errors
  return text
    // Remove emoji characters (most common ranges)
    .replace(/[\u{1F600}-\u{1F64F}]/gu, '') // Emoticons
    .replace(/[\u{1F300}-\u{1F5FF}]/gu, '') // Symbols & pictographs
    .replace(/[\u{1F680}-\u{1F6FF}]/gu, '') // Transport & map symbols
    .replace(/[\u{1F700}-\u{1F77F}]/gu, '') // Alchemical symbols
    .replace(/[\u{1F780}-\u{1F7FF}]/gu, '') // Geometric shapes
    .replace(/[\u{1F800}-\u{1F8FF}]/gu, '') // Supplemental arrows
    .replace(/[\u{1F900}-\u{1F9FF}]/gu, '') // Supplemental symbols and pictographs
    .replace(/[\u{1FA00}-\u{1FA6F}]/gu, '') // Chess symbols
    .replace(/[\u{1FA70}-\u{1FAFF}]/gu, '') // Symbols and pictographs extended-A
    .replace(/[\u{2702}-\u{27B0}]/gu, '')   // Dingbats
    .replace(/[\u{24C2}-\u{1F251}]/gu, '')  // Enclosed characters
    // Remove control characters and potentially harmful characters
    .replace(/[\u0000-\u001F\u007F-\u009F]/g, '')
    .replace(/[\u200B-\u200D\uFEFF]/g, '')
    .replace(/[&<>"'`=\/\\]/g, '')
    .trim();
};

const Job = {
  // Get all jobs
  getAll: (callback) => {
    const query = "SELECT * FROM job_details";
    db.query(query, (err, results, fields) => {
      if (err) {
        console.error("Database query failed:", err);
        return callback(err, null);
      }
      
      callback(null, results);
    });
  },

  // Get job by ID
  getById: (jobId, callback) => {
    const query = "SELECT * FROM job_details WHERE job_id = ?";
    db.query(query, [jobId], callback);
  },

  // Create new job
  create: (jobData, callback) => {
    try {
      // Field length limits based on typical MySQL column types
      const fieldLimits = {
        job_title: 255,        // VARCHAR(255)
        company_name: 255,     // VARCHAR(255)
        send_cv_email: 255,    // VARCHAR(255)
        main_topics: 100,      // VARCHAR(100)
        sub_topics: 1000,      // TEXT
        job_type: 100,         // VARCHAR(100)
        salary_range: 100,     // VARCHAR(100)
        job_description: 10000 // TEXT or MEDIUMTEXT
      };
      
      // Initialize with required fields - based on actual database schema
      const newJob = {
        job_title: jobData.job_title ? sanitizeText(jobData.job_title).substring(0, fieldLimits.job_title) : '',
        company_name: jobData.company_name ? jobData.company_name.substring(0, fieldLimits.company_name) : '',
        year: jobData.year || new Date().getFullYear(),
        // Always use current date for new jobs if not explicitly provided
        start_date: jobData.start_date || new Date().toISOString().split('T')[0],
        end_date: jobData.end_date || '',
        send_cv_email: jobData.send_cv_email ? jobData.send_cv_email.substring(0, fieldLimits.send_cv_email) : '',
        main_topics: jobData.main_topics ? jobData.main_topics.substring(0, fieldLimits.main_topics) : '',
        sub_topics: (() => {
          // Handle sub_topics more robustly
          try {
            if (!jobData.sub_topics) return '[]';
            
            let finalValue = '';
            
            if (Array.isArray(jobData.sub_topics)) {
              finalValue = JSON.stringify(jobData.sub_topics);
            } else if (typeof jobData.sub_topics === 'string') {
              // If it's already a JSON string, validate it
              try {
                if (jobData.sub_topics.trim().startsWith('[')) {
                  // Check if it's valid JSON
                  JSON.parse(jobData.sub_topics);
                  finalValue = jobData.sub_topics;
                } else {
                  // It's a simple string, convert to array with one item
                  finalValue = JSON.stringify([jobData.sub_topics]);
                }
              } catch (jsonError) {
                // If parsing fails, treat as a plain string
                finalValue = JSON.stringify([jobData.sub_topics]);
              }
            } else {
              finalValue = '[]';
            }
            
            // Truncate if too long
            if (finalValue.length > fieldLimits.sub_topics) {
              finalValue = finalValue.substring(0, fieldLimits.sub_topics);
            }
            
            return finalValue;
          } catch (err) {
            return '[]';
          }
        })(),
        job_type: jobData.job_type ? jobData.job_type.substring(0, fieldLimits.job_type) : 'Full Time',
        experience_level: jobData.experience_level ? jobData.experience_level.substring(0, 100) : 'Entry Level',
        // Handle "0" as a valid salary value and null/undefined/empty as NULL for DECIMAL column
        salary_range: (jobData.salary_range === 0 || jobData.salary_range === '0') 
          ? '0' 
          : (jobData.salary_range && jobData.salary_range !== '') 
            ? String(jobData.salary_range).substring(0, fieldLimits.salary_range) 
            : null,
        min_salary: jobData.min_salary !== undefined && jobData.min_salary !== '' && !isNaN(Number(jobData.min_salary))
          ? Number(jobData.min_salary)
          : null,
        max_salary: jobData.max_salary !== undefined && jobData.max_salary !== '' && !isNaN(Number(jobData.max_salary))
          ? Number(jobData.max_salary)
          : null,
        job_description: jobData.job_description ? sanitizeText(jobData.job_description).substring(0, fieldLimits.job_description) : ''
      };

      // Handle image fields - can be either Cloudinary URLs or binary data
      if (jobData.job_post_image) {
        // If it's a URL string, check length
        if (typeof jobData.job_post_image === 'string' && jobData.job_post_image.length > 2000) {
          newJob.job_post_image = jobData.job_post_image.substring(0, 2000);
        } else {
          newJob.job_post_image = jobData.job_post_image;
        }
      };

      // Add hot field if present
      if (typeof jobData.hot !== 'undefined') {
        newJob.hot = jobData.hot === true || jobData.hot === 'true' ? 1 : 0;
      }
      
      if (jobData.job_post_thumbnail) {
        // If it's a URL string, check length
        if (typeof jobData.job_post_thumbnail === 'string' && jobData.job_post_thumbnail.length > 2000) {
          newJob.job_post_thumbnail = jobData.job_post_thumbnail.substring(0, 2000);
        } else {
          newJob.job_post_thumbnail = jobData.job_post_thumbnail;
        }
      }
      
      if (jobData.company_logo) {
        // If it's a URL string, check length
        if (typeof jobData.company_logo === 'string' && jobData.company_logo.length > 2000) {
          newJob.company_logo = jobData.company_logo.substring(0, 2000);
        } else {
          newJob.company_logo = jobData.company_logo;
        }
      }
      
      // Build query dynamically based on available fields
      const fields = Object.keys(newJob);
      const placeholders = fields.map(() => '?').join(', ');
      const values = fields.map(field => newJob[field]);
      
      const query = `INSERT INTO job_details (${fields.join(', ')}) VALUES (${placeholders})`;
      
      db.query(query, values, (err, result) => {
        if (err) {
          console.error("Error in job creation SQL:", err);
          return callback(err, null);
        }
        callback(null, result);
      });
    } catch (error) {
      console.error("Unexpected error in job creation model:", error);
      callback(error, null);
    }
  },

  // Update job
  update: (jobId, jobData, callback) => {
    try {
      // Only update fields that are provided and exist in the database schema
      const updates = {};
      
      if (jobData.job_title !== undefined) updates.job_title = sanitizeText(jobData.job_title);
      if (jobData.company_name !== undefined) updates.company_name = jobData.company_name;
      if (jobData.year !== undefined) updates.year = jobData.year;
      if (jobData.start_date !== undefined) updates.start_date = jobData.start_date;
      if (jobData.end_date !== undefined) updates.end_date = jobData.end_date;
      if (jobData.send_cv_email !== undefined) updates.send_cv_email = jobData.send_cv_email;
      if (jobData.main_topics !== undefined) updates.main_topics = jobData.main_topics;
      if (jobData.sub_topics !== undefined) {
        try {
          if (Array.isArray(jobData.sub_topics)) {
            updates.sub_topics = JSON.stringify(jobData.sub_topics);
          } else if (typeof jobData.sub_topics === 'string') {
            // If it's already a JSON string, validate it
            if (jobData.sub_topics.trim().startsWith('[')) {
              // Check if it's valid JSON
              JSON.parse(jobData.sub_topics);
              updates.sub_topics = jobData.sub_topics;
            } else {
              // It's a simple string, convert to array with one item
              updates.sub_topics = JSON.stringify([jobData.sub_topics]);
            }
          } else {
            updates.sub_topics = '[]';
          }
        } catch (err) {
          console.warn("Error processing sub_topics in update, using default:", err);
          updates.sub_topics = '[]';
        }
      }
      if (jobData.job_type !== undefined) updates.job_type = jobData.job_type;
      if (jobData.experience_level !== undefined) updates.experience_level = jobData.experience_level;
      // Handle empty, null, undefined, and "0" values for salary_range
      if (jobData.salary_range !== undefined || jobData.salary_range === 0 || jobData.salary_range === '0') {
        updates.salary_range = jobData.salary_range === null || jobData.salary_range === '' 
          ? null 
          : String(jobData.salary_range);
      }
      if (jobData.min_salary !== undefined) {
        updates.min_salary = jobData.min_salary === '' || isNaN(Number(jobData.min_salary))
          ? null
          : Number(jobData.min_salary);
      }
      if (jobData.max_salary !== undefined) {
        updates.max_salary = jobData.max_salary === '' || isNaN(Number(jobData.max_salary))
          ? null
          : Number(jobData.max_salary);
      }
      if (jobData.job_description !== undefined) updates.job_description = sanitizeText(jobData.job_description);
      // Add hot field if present
      if (typeof jobData.hot !== 'undefined') {
        updates.hot = jobData.hot === true || jobData.hot === 'true' ? 1 : 0;
      }
      
      // Handle image fields - can be either Cloudinary URLs or binary data
      // When using Cloudinary, image fields contain secure URLs like: https://res.cloudinary.com/cloud_name/image/upload/...
      if (jobData.job_post_image) updates.job_post_image = jobData.job_post_image;
      if (jobData.job_post_thumbnail) updates.job_post_thumbnail = jobData.job_post_thumbnail;
      if (jobData.company_logo) updates.company_logo = jobData.company_logo;
      
      // Only proceed if there are fields to update
      if (Object.keys(updates).length === 0) {
        return callback(null, { affectedRows: 0 });
      }
      
      // Build update query
      const setClause = Object.keys(updates)
        .map(key => `${key} = ?`)
        .join(', ');
      const values = [...Object.values(updates), jobId];
      
      const query = `UPDATE job_details SET ${setClause} WHERE job_id = ?`;
      
      db.query(query, values, (err, result) => {
        if (err) {
          console.error("Error in job update SQL:", err);
          return callback(err, null);
        }
        callback(null, result);
      });
    } catch (error) {
      console.error("Unexpected error in job update model:", error);
      callback(error, null);
    }
  },

  // Delete job
  delete: (jobId, callback) => {
    const query = "DELETE FROM job_details WHERE job_id = ?";
    db.query(query, [jobId], (err, result) => {
      if (err) {
        console.error("Error in job deletion SQL:", err);
        return callback(err, null);
      }
      callback(null, result);
    });
  },
  
  // Increment view count for a job
  incrementViewCount: (jobId, callback) => {
    // First check if view_count column exists in the table
    const checkQuery = `
      SELECT COUNT(*) AS column_exists 
      FROM information_schema.COLUMNS 
      WHERE TABLE_NAME = 'job_details' 
      AND COLUMN_NAME = 'view_count'
    `;
    
    db.query(checkQuery, (checkErr, checkResult) => {
      if (checkErr) {
        console.error("Error checking for view_count column:", checkErr);
        return callback(checkErr, null);
      }
      
      // If column doesn't exist, add it
      if (checkResult[0].column_exists === 0) {
        const alterQuery = `
          ALTER TABLE job_details 
          ADD COLUMN view_count INT DEFAULT 0
        `;
        
        db.query(alterQuery, (alterErr) => {
          if (alterErr) {
            console.error("Error adding view_count column:", alterErr);
            return callback(alterErr, null);
          }
          
          // After adding the column, proceed with the increment
          incrementCount();
        });
      } else {
        // Column already exists, proceed with the increment
        incrementCount();
      }
    });
    
    // Function to increment the view count
    function incrementCount() {
      const updateQuery = `
        UPDATE job_details 
        SET view_count = IFNULL(view_count, 0) + 1 
        WHERE job_id = ?
      `;
      
      db.query(updateQuery, [jobId], (updateErr, updateResult) => {
        if (updateErr) {
          console.error("Error incrementing view count:", updateErr);
          return callback(updateErr, null);
        }
        
        callback(null, updateResult);
      });
    }
  }
};

module.exports = Job;
