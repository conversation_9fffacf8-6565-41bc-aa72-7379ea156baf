/* eslint-disable no-unused-vars */
import React, { useState, useEffect } from 'react';
import '../../css/AdminPanel.css';
import '../../css/search-fix.css';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faUsers,
  faCalendar,
  faComments,
  faCog,
  faBars,
  faBell,
  faSearch,
  faBriefcase,
  faUserCircle,
  faChartPie,
  faFileAlt,
  faSignOutAlt,
  faBuilding,
  faIdCard
} from '@fortawesome/free-solid-svg-icons';
import JobsAdmin from './JobsAdmin';
import AnalyticsDashboard from './AnalyticsDashboard';
import BlogAdmin from './BlogAdmin';
import CompaniesAdmin from './CompaniesAdmin';
import CvAdmin from './CvAdmin';
import AdminUsers from './AdminUsers';
import { useNavigate } from 'react-router-dom';

const AdminPanel = ({ onJobPost }) => {
  const navigate = useNavigate();
  const [currentUser, setCurrentUser] = useState({ name: 'Admin User', role: 'Administrator' });

  // Get current user information from localStorage
  useEffect(() => {
    const userData = localStorage.getItem('user');
    if (userData) {
      try {
        const user = JSON.parse(userData);
        setCurrentUser({
          name: user.admin_username || user.name || user.username || 'Admin User',
          role: user.role || 'Administrator'
        });
        // Set default active item based on user role
        // For Editor role, default to Job Listings (id: 1)
        setActiveSidebarItem(user.role === 'Editor' ? 1 : 0);
      } catch (error) {
        console.error('Error parsing user data:', error);
      }
    }
  }, []);

  // Auto sign-out when tab is closed or refreshed
  useEffect(() => {
    const handleBeforeUnload = () => {
      // Clear authentication data when tab is being closed or refreshed
      localStorage.removeItem('token');
      localStorage.removeItem('user');
    };

    const handleVisibilityChange = () => {
      // Clear authentication data when tab becomes hidden (user switches tabs or minimizes)
      if (document.visibilityState === 'hidden') {
        localStorage.removeItem('token');
        localStorage.removeItem('user');
      }
    };

    // Add event listeners
    window.addEventListener('beforeunload', handleBeforeUnload);
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Cleanup event listeners on component unmount
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  // Function to show logout confirmation modal
  const handleLogout = () => {
    setShowLogoutModal(true);
  };

  // Function to confirm logout
  const confirmLogout = () => {
    // Clear authentication data before redirecting
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    navigate('/jp-admin');
  };

  // Function to cancel logout
  const cancelLogout = () => {
    setShowLogoutModal(false);
  };

  const [jobData, setJobData] = useState({
    hot: false,
    image: "",
    logo: "",
    company: "",
    position: "",
    location: "",
    workTime: "Full Time",
    salary: "",
    postedTime: "Just now",
    views: "0"
  });

  const [imagePreview, setImagePreview] = useState(null);
  const [logoPreview, setLogoPreview] = useState(null);
  const [activeTab, setActiveTab] = useState('newJob');
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [activeSidebarItem, setActiveSidebarItem] = useState(0);
  const [activeAccordion, setActiveAccordion] = useState('dashboard');
  const [showLogoutModal, setShowLogoutModal] = useState(false);

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setJobData({
      ...jobData,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  const handleImageUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = () => {
        setImagePreview(reader.result);
        setJobData({ ...jobData, image: reader.result });
      };
      reader.readAsDataURL(file);
    }
  };

  const handleLogoUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = () => {
        setLogoPreview(reader.result);
        setJobData({ ...jobData, logo: reader.result });
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onJobPost(jobData);
    
    // Reset form
    setJobData({
      hot: false,
      image: "",
      logo: "",
      company: "",
      position: "",
      location: "",
      workTime: "Full Time",
      salary: "",
      postedTime: "Just now",
      views: "0"
    });
    setImagePreview(null);
    setLogoPreview(null);
  };

  const handleSidebarItemClick = (index) => {
    setActiveSidebarItem(index);
    setIsSidebarOpen(false); // Close sidebar on mobile after item selection
  };

  const toggleAccordion = (accordionName) => {
    setActiveAccordion(activeAccordion === accordionName ? null : accordionName);
  };

  // Function to filter sidebar items based on user role
  const getFilteredSidebarStructure = () => {
    const fullSidebarStructure = [
      {
        category: "Main",
        items: [
          { id: 0, title: "Analytics", icon: faChartPie },
        ]
      },
      {
        category: "Job Management",
        items: [
          { id: 1, title: "Job Listings", icon: faBriefcase },
          { id: 6, title: "Companies", icon: faBuilding },
          { id: 7, title: "CV Management", icon: faIdCard },
        ]
      },
      {
        category: "Content",
        items: [
          { id: 2, title: "Blog", icon: faFileAlt },
        ]
      },
      {
        category: "Administration",
        items: [
          { id: 4, title: "User Accounts", icon: faUserCircle },
          { id: 5, title: "Logout", icon: faSignOutAlt },
        ]
      }
    ];

    // Filter sidebar items based on user role
    if (currentUser.role === 'Editor') {
      return [
        {
          category: "Job Management",
          items: [
            { id: 1, title: "Job Listings", icon: faBriefcase },
            { id: 6, title: "Companies", icon: faBuilding },
          ]
        },
        {
          category: "Content",
          items: [
            { id: 2, title: "Blog", icon: faFileAlt },
          ]
        },
        {
          category: "Account",
          items: [
            { id: 5, title: "Logout", icon: faSignOutAlt },
          ]
        }
      ];
    }

    // Return full structure for Admin and Superadmin roles
    return fullSidebarStructure;
  };

  // Get filtered sidebar structure based on user role
  const sidebarStructure = getFilteredSidebarStructure();

  // Render content based on selected sidebar item
  const renderContent = () => {
    switch (activeSidebarItem) {
      case 0: // Analytics
        return <AnalyticsDashboard />;
      case 1: // Jobs
        return <JobsAdmin />;
      case 2: // Blog
        return <BlogAdmin />;
      case 4: // User Accounts
        return <AdminUsers />;
      // case 5: // Logout - This was causing the double popup
      //  handleLogout();
      //  return null;
      case 6: // Companies
        return <CompaniesAdmin />;
      case 7: // CV Management
        return <CvAdmin />;
      default:
        return <div>Select an option from the sidebar</div>;
    }
  };

  return (
    <div className="admin-container">
      {/* Header */}
      <header className="admin-header">
        <div className="header-left">
          <button className="menu-button mobile-only" onClick={() => setIsSidebarOpen(!isSidebarOpen)}>
            <FontAwesomeIcon icon={faBars} />
          </button>
          <h1>Job Page Dashboard</h1>
        </div>
        
        <div className="header-controls">
        </div>
      </header>

      <div className="admin-content">
        {/* Sidebar */}
        <aside className={`admin-sidebar ${isSidebarOpen ? 'open' : ''}`}>
          {/* Logo */}
          <div className="logo-container">
            <div className="logo">
              <FontAwesomeIcon icon={faBriefcase} />
              <span>Job Page </span>
            </div>
          </div>
          
          {/* Navigation sections - will stretch to fill available space */}
          <div className="sidebar-nav-container">
            {sidebarStructure.map((section, sectionIndex) => (
              <div key={sectionIndex} className="nav-group">
                <h3 className="nav-group-title">{section.category}</h3>
                {section.items.map((item) => (
                  <div
                    key={item.id}
                    className={`nav-link ${activeSidebarItem === item.id ? 'active' : ''}`}
                    onClick={() => item.id === 5 ? handleLogout() : handleSidebarItemClick(item.id)}
                  >
                    <FontAwesomeIcon 
                      icon={item.icon} 
                      className={`nav-icon ${activeSidebarItem === item.id ? 'active' : ''}`} 
                    />
                    <span>{item.title}</span>
                  </div>
                ))}
              </div>
            ))}
          </div>
          
          {/* User profile - will stay at the bottom */}
          <div className="user-profile">
            <div className="user-avatar">
              <FontAwesomeIcon icon={faUserCircle} size="lg" />
            </div>
            <div className="user-info">
              <span className="user-name">{currentUser.name}</span>
              <span className="user-role">{currentUser.role}</span>
            </div>
          </div>
        </aside>

        {/* Main Content */}
        <main className="admin-main">
          {/* Render content based on sidebar selection */}
          {renderContent()}
        </main>
      </div>
            
      {/* Mobile Bottom Navigation */}
      <div className="mobile-bottom-nav">
        <div 
          className={`mobile-nav-item ${activeSidebarItem === 0 ? 'active' : ''}`}
          onClick={() => handleSidebarItemClick(0)}
        >
          <div className="mobile-nav-icon">
            <FontAwesomeIcon icon={faChartPie} />
          </div>
          <span>Analytics</span>
        </div>
        <div 
          className={`mobile-nav-item ${activeSidebarItem === 1 ? 'active' : ''}`}
          onClick={() => handleSidebarItemClick(1)}
        >
          <div className="mobile-nav-icon">
            <FontAwesomeIcon icon={faBriefcase} />
          </div>
          <span>Jobs</span>
        </div>
        <div 
          className={`mobile-nav-item ${activeSidebarItem === 2 ? 'active' : ''}`}
          onClick={() => handleSidebarItemClick(2)}
        >
          <div className="mobile-nav-icon">
            <FontAwesomeIcon icon={faFileAlt} />
          </div>
          <span>Blog</span>
        </div>
        <div 
          className={`mobile-nav-item ${activeSidebarItem === 7 ? 'active' : ''}`}
          onClick={() => handleSidebarItemClick(7)}
        >
          <div className="mobile-nav-icon">
            <FontAwesomeIcon icon={faIdCard} />
          </div>
          <span>CVs</span>
        </div>
      </div>

      {/* Logout Confirmation Modal */}
      {showLogoutModal && (
        <div className="modal-overlay">
          <div className="logout-modal">
            <div className="modal-header">
              <FontAwesomeIcon icon={faSignOutAlt} className="modal-icon" />
              <h3>Confirm Logout</h3>
            </div>
            <div className="modal-body">
              <p>Are you sure you want to logout? You will need to sign in again to access the admin panel.</p>
            </div>
            <div className="modal-footer">
              <button className="btn-cancel" onClick={cancelLogout}>
                Cancel
              </button>
              <button className="btn-confirm" onClick={confirmLogout}>
                <FontAwesomeIcon icon={faSignOutAlt} />
                Logout
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminPanel;