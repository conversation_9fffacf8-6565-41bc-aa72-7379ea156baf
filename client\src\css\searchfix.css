/* Fix for search input styles to ensure consistency across browsers */
.search-input {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  outline: none;
}

.search-input:focus {
  border-color: #8057ff;
  box-shadow: 0 0 0 2px rgba(128, 87, 255, 0.1);
}

/* Ensure search icon is properly positioned */
.search-input-wrapper .search-icon {
  pointer-events: none;
}

/* Fix for mobile devices */
@media (max-width: 768px) {
  .search-container {
    max-width: 100%;
  }
  
  .filters-container {
    flex-direction: column;
  }
  
  .filter-select {
    width: 100%;
  }
  
  .blogs-table th,
  .blogs-table td {
    padding: 10px 12px;
  }
  
  .action-button {
    width: 28px;
    height: 28px;
  }
}