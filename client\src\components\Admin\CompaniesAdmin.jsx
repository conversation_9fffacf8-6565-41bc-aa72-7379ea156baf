import React, { useState, useEffect, useRef } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faEdit, 
  faTrash, 
  faPlus, 
  faSearch, 
  faTimes, 
  faSave,
  faBuilding,
  faUpload,
  faExclamationTriangle,
  faSync
} from '@fortawesome/free-solid-svg-icons';
import ApiService from '../../services/apiService';
import '../../css/CompaniesAdmin.css';
import '../../css/shared-delete-dialog.css';
// Import search-fix.css last to ensure it takes precedence
import '../../css/search-fix.css';

const CompaniesAdmin = () => {
  // State
  const [companies, setCompanies] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [isAddingCompany, setIsAddingCompany] = useState(false);
  const [isEditingCompany, setIsEditingCompany] = useState(null);
  const [logoPreview, setLogoPreview] = useState(null);
  const [logoFile, setLogoFile] = useState(null);
  
  // Delete confirmation dialog state
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [companyToDelete, setCompanyToDelete] = useState(null);
  
  const [companyForm, setCompanyForm] = useState({
    company_name: '',
    company_description: '',
    company_website: '',
  });
  const [notification, setNotification] = useState({ show: false, message: '', type: '' });
  const fileInputRef = useRef(null);

  // Fetch companies on component mount
  useEffect(() => {
    fetchCompanies();
  }, []);
  // Reset form and logo preview when add/edit mode changes
  useEffect(() => {
    if (!isAddingCompany && isEditingCompany === null) {
      setCompanyForm({
        company_name: '',
        company_description: '',
        company_website: '',
      });
      setLogoPreview(null);
      setLogoFile(null);
    }
  }, [isAddingCompany, isEditingCompany]);

  // Fetch companies from API
  const fetchCompanies = async () => {
    try {
      setLoading(true);
      const response = await ApiService.companies.getAll();
      setCompanies(response.data.data || []);
      setError(null);
    } catch (err) {
      console.error('Error fetching companies:', err);
      setError('Failed to load companies');
    } finally {
      setLoading(false);
    }
  };

  // Handle input change for form fields
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setCompanyForm(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle logo file selection
  const handleLogoChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setLogoFile(file);
      
      // Create a preview URL
      const reader = new FileReader();
      reader.onloadend = () => {
        setLogoPreview(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  // Trigger file input click
  const handleBrowseClick = () => {
    fileInputRef.current.click();
  };

  // Handle adding new company
  const handleAddCompany = () => {
    setIsAddingCompany(true);
    setIsEditingCompany(null);
  };
  // Handle editing existing company
  const handleEditCompany = (company) => {
    setIsEditingCompany(company.company_id);
    setIsAddingCompany(false);
    
    // Populate form with company data
    setCompanyForm({
      company_name: company.company_name || '',
      company_description: company.company_description || '',
      company_website: company.company_website || '',
    });
    
    // Set logo preview if exists
    if (company.company_logo_url) {
      setLogoPreview(company.company_logo_url);
    } else {
      setLogoPreview(null);
    }
  };

  // Handle deleting company
  const handleDeleteCompany = async (companyId) => {
    try {
      await ApiService.companies.delete(companyId);
      setNotification({
        show: true,
        message: 'Company deleted successfully',
        type: 'success'
      });
      
      // Reset delete confirmation dialog
      setShowDeleteConfirm(false);
      setCompanyToDelete(null);
      
      // Refresh the companies list
      fetchCompanies();
    } catch (err) {
      console.error('Error deleting company:', err);
      setNotification({
        show: true,
        message: 'Failed to delete company',
        type: 'error'
      });
    }
  };

  // Function to show delete confirmation
  const confirmDelete = (company) => {
    setCompanyToDelete(company);
    setShowDeleteConfirm(true);
  };

  // Function to cancel delete
  const cancelDelete = () => {
    setShowDeleteConfirm(false);
    setCompanyToDelete(null);
  };

  // Handle form submission (create or update)
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    try {
      const formData = new FormData();
      
      // Add all text fields to form data, including empty values for optional fields
      formData.append('company_name', companyForm.company_name);
      formData.append('company_description', companyForm.company_description || '');
      formData.append('company_website', companyForm.company_website || '');
      
      // Add logo if selected
      if (logoFile) {
        formData.append('company_logo', logoFile);
      }
      
      if (isAddingCompany) {
        // Create new company
        await ApiService.companies.create(formData);
        
        setNotification({
          show: true,
          message: 'Company added successfully',
          type: 'success'
        });
      } else if (isEditingCompany) {
        // Update existing company
        await ApiService.companies.update(isEditingCompany, formData);
        
        setNotification({
          show: true,
          message: 'Company updated successfully',
          type: 'success'
        });
      }
      
      // Reset form state and refresh companies
      setIsAddingCompany(false);
      setIsEditingCompany(null);
      fetchCompanies();
    } catch (err) {
      console.error('Error saving company:', err);
      setNotification({
        show: true,
        message: 'Failed to save company',
        type: 'error'
      });
    }
  };

  // Cancel adding/editing
  const handleCancel = () => {
    setIsAddingCompany(false);
    setIsEditingCompany(null);
  };

  // Close notification
  const closeNotification = () => {
    setNotification({ show: false, message: '', type: '' });
  };

  // Filter companies based on search term
  const filteredCompanies = companies.filter(company => 
    company.company_name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="companies-admin-container">
      {/* Delete Confirmation Dialog */}
      {showDeleteConfirm && (
        <div className="delete-confirm-overlay">
          <div className="delete-confirm-dialog">
            <div className="delete-confirm-header">
              <FontAwesomeIcon icon={faExclamationTriangle} className="delete-icon" />
              <h3>Confirm Deletion</h3>
            </div>
            <div className="delete-confirm-content">
              <p>Are you sure you want to delete this company?</p>
              <p><strong>{companyToDelete?.company_name}</strong></p>
              <p>This action cannot be undone. All jobs associated with this company may also be affected.</p>
            </div>
            <div className="delete-confirm-actions">
              <button 
                className="cancel-delete-btn" 
                onClick={cancelDelete}
              >
                Cancel
              </button>
              <button 
                className="confirm-delete-btn" 
                onClick={() => handleDeleteCompany(companyToDelete.company_id)}
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
      
      {/* Notification */}
      {notification.show && (
        <div className={`admin-notification ${notification.type}`}>
          <span>{notification.message}</span>
          <button onClick={closeNotification}>
            <FontAwesomeIcon icon={faTimes} />
          </button>
        </div>
      )}
      <div className="companies-admin-header">
        <h2>
          <FontAwesomeIcon icon={faBuilding} className="mr-2" />
          Company Management
        </h2>
        
        <div className="header-controls">
          <button 
            className="refresh-button" 
            onClick={fetchCompanies}
            disabled={loading}
          >
            <FontAwesomeIcon icon={faSync} />
            <span>Refresh</span>
          </button>
          <button 
            className="add-company-btn"
            onClick={handleAddCompany}
            disabled={isAddingCompany || isEditingCompany !== null}
          >
            <FontAwesomeIcon icon={faPlus} /> Add Company
          </button>
        </div>
      </div>      {/* Form for adding/editing company - Modal */}

      <div className="filters-container">
      <div className="search-container">
        <div className="search-input-wrapper">
          <FontAwesomeIcon icon={faSearch} className="search-icon" />          <input
          type="text"
          placeholder="Search by company or position..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="search-input"
          style={{ paddingLeft: '40px' }}
        />
        </div>
      </div>
    </div>

      {(isAddingCompany || isEditingCompany !== null) && (
        <div className="modal-overlay">
          <div className="modal-container">
            <div className="modal-header">
              <h3>{isAddingCompany ? 'Add New Company' : 'Edit Company'}</h3>
              <button 
                type="button" 
                className="modal-close-btn"
                onClick={handleCancel}
              >
                <FontAwesomeIcon icon={faTimes} />
              </button>
            </div>
            
            <div className="modal-content">
              <form onSubmit={handleSubmit} className="company-form">
                <div className="form-group">
                  <label>
                    <FontAwesomeIcon icon={faBuilding} className="form-icon" />
                    Company Name*
                  </label>
                  <input
                    type="text"
                    name="company_name"
                    value={companyForm.company_name}
                    onChange={handleInputChange}
                    required
                    placeholder="Enter company name"
                  />
                </div>

                <div className="form-group">
                  <label>
                    Description
                  </label>
                  <textarea
                    name="company_description"
                    value={companyForm.company_description}
                    onChange={handleInputChange}
                    placeholder="Enter company description (optional)"
                    rows="4"
                    className="textarea-field"
                  />
                </div>

                <div className="form-group">
                  <label>
                    Website URL
                  </label>
                  <input
                    type="url"
                    name="company_website"
                    value={companyForm.company_website}
                    onChange={handleInputChange}
                    placeholder="https://www.example.com (optional)"
                  />
                </div>

                <div className="logo-upload-container">
                  <div className="logo-preview">
                    {logoPreview ? (
                      <img src={logoPreview} alt="Company logo preview" />
                    ) : (
                      <div className="no-logo">
                        <FontAwesomeIcon icon={faBuilding} />
                        <span>No logo</span>
                      </div>
                    )}
                  </div>
                  
                  <div className="logo-upload">
                    <label>Company Logo</label>
                    
                    <button 
                      type="button" 
                      onClick={handleBrowseClick} 
                      className="browse-logo-btn"
                    >
                      <FontAwesomeIcon icon={faUpload} /> Browse for Logo
                    </button>
                    
                    <input
                      ref={fileInputRef}
                      type="file"
                      accept="image/*"
                      onChange={handleLogoChange}
                      className="hidden-file-input"
                    />
                    
                    <p className="file-hint">Recommended size: 200x200px</p>
                    {logoFile && <p className="selected-file">Selected: {logoFile.name}</p>}
                  </div>
                </div>

                <div className="form-actions">
                  <button 
                    type="button" 
                    className="cancel-btn"
                    onClick={handleCancel}
                  >
                    <FontAwesomeIcon icon={faTimes} /> Cancel
                  </button>
                  
                  <button 
                    type="submit" 
                    className="save-btn"
                  >
                    <FontAwesomeIcon icon={faSave} /> Save Company
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Companies list */}
      <div className="companies-list-container">
        {loading ? (
          <div className="loading-message">Loading companies...</div>
        ) : error ? (
          <div className="error-message">{error}</div>
        ) : filteredCompanies.length === 0 ? (
          <div className="no-companies-message">
            {searchTerm ? 'No companies match your search' : 'No companies added yet'}
          </div>
        ) : (
          <div className="companies-table-wrapper">
            <table className="companies-table">
              <thead>
                <tr>
                  <th>Logo</th>
                  <th>Company Name</th>
                  <th>Description</th>
                  <th>Website</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredCompanies.map(company => (
                  <tr key={company.company_id}>
                    <td className="company-logo-cell">
                      {company.company_logo_url ? (
                        <img 
                          src={company.company_logo_url} 
                          alt={`${company.company_name} logo`} 
                          className="company-logo-thumbnail"
                        />
                      ) : (
                        <div className="no-logo-thumbnail">
                          <FontAwesomeIcon icon={faBuilding} />
                        </div>
                      )}
                    </td>
                    <td>{company.company_name}</td>
                    <td className="description-cell">
                      {company.company_description ? (
                        <div className="description-text" title={company.company_description}>
                          {company.company_description.length > 100 
                            ? `${company.company_description.substring(0, 100)}...` 
                            : company.company_description}
                        </div>
                      ) : (
                        <span className="no-data">No description</span>
                      )}
                    </td>
                    <td className="website-cell">
                      {company.company_website ? (
                        <a 
                          href={company.company_website.startsWith('http') 
                            ? company.company_website 
                            : `https://${company.company_website}`}
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="website-link"
                        >
                          Visit Website
                        </a>
                      ) : (
                        <span className="no-data">No website</span>
                      )}
                    </td>
                    <td className="actions-cell">
                      <button 
                        className="edit-btn"
                        onClick={() => handleEditCompany(company)}
                      >
                        <FontAwesomeIcon icon={faEdit} />
                      </button>
                      
                      <button 
                        className="delete-btn"
                        onClick={() => confirmDelete(company)}
                      >
                        <FontAwesomeIcon icon={faTrash} />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};

export default CompaniesAdmin;