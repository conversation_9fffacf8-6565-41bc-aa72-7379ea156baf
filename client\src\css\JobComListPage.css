/* JobComListPage.css */
.jobcom-container {
  display: flex;
  padding: 30px 5%;
  gap: 30px;
  background-color: #f9f5fa;
  font-family: 'Inter', sans-serif;
}

.jobcom-sidebar {
  width: 300px;
  flex-shrink: 0;
}

.jobcom-section {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.jobcom-section-title {
  color: #333;
  font-weight: 600;
  font-size: 18px;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.jobcom-section-description {
  color: #777;
  font-size: 14px;
  line-height: 1.6;
  margin-bottom: 15px;
}

.jobcom-input-group {
  position: relative;
  margin-bottom: 15px;
}

.jobcom-input {
  width: 100%;
  padding: 10px 15px;
  padding-left: 35px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
}

.jobcom-input:focus {
  outline: none;
  border-color: #7743DB;
}

.jobcom-input-icon {
  position: absolute;
  top: 50%;
  left: 10px;
  transform: translateY(-50%);
  color: #777;
}

.jobcom-filter-items {
  max-height: 200px;
  overflow-y: auto;
}

.jobcom-filter-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  cursor: pointer;
}

.jobcom-filter-item input[type="checkbox"] {
  margin-right: 10px;
}

.jobcom-filter-label {
  color: #555;
  font-size: 14px;
}

.jobcom-filter-count {
  margin-left: auto;
  color: #999;
  font-size: 12px;
}

.jobcom-btn {
  display: inline-block;
  padding: 10px 20px;
  background-color: #7743DB;
  color: white;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  text-align: center;
  transition: background-color 0.3s;
}

.jobcom-btn:hover {
  background-color: #6234c7;
}

.jobcom-btn-outline {
  background-color: transparent;
  border: 1px solid #7743DB;
  color: #7743DB;
}

.jobcom-btn-outline:hover {
  background-color: #f5f0ff;
}

.jobcom-main-content {
  flex: 1;
}

.jobcom-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.jobcom-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.jobcom-sort-select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  color: #555;
}

.jobcom-sort-select:focus {
  outline: none;
  border-color: #7743DB;
}

.jobcom-company-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
}

.jobcom-company-card {
  background-color: #fff;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
  transition: transform 0.3s, box-shadow 0.3s;
}

.jobcom-company-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(119, 67, 219, 0.15);
}

.jobcom-company-logo {
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  padding: 15px;
}

.jobcom-company-logo img,
.jobcom-company-logo span {
  max-height: 60px;
  max-width: 120px;
  font-size: 32px;
  color: #7743DB;
  font-weight: bold;
}

.jobcom-company-info {
  padding: 15px;
}

.jobcom-company-name {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.jobcom-company-industry {
  color: #7743DB;
  font-size: 14px;
  margin-bottom: 10px;
}

.jobcom-company-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 10px;
}

.jobcom-meta-item {
  display: flex;
  align-items: center;
  color: #777;
  font-size: 13px;
}

.jobcom-meta-icon {
  margin-right: 5px;
  color: #7743DB;
  font-size: 12px;
}

.jobcom-company-rating {
  margin-bottom: 10px;
}

.jobcom-star {
  color: #FFD700;
  margin-right: 2px;
}

.jobcom-star-inactive {
  color: #ddd;
  margin-right: 2px;
}

.jobcom-job-count {
  font-size: 13px;
  color: #666;
  margin-top: 5px;
}

.jobcom-company-actions {
  display: flex;
  justify-content: space-between;
  border-top: 1px solid #eee;
  padding-top: 10px;
  margin-top: 5px;
}

.jobcom-action-btn {
  background: none;
  border: none;
  color: #7743DB;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  padding: 5px;
}

.jobcom-action-btn:hover {
  color: #6234c7;
}

.jobcom-action-icon {
  margin-right: 5px;
  font-size: 14px;
}

.jobcom-pagination {
  display: flex;
  justify-content: center;
  margin-top: 30px;
  gap: 5px;
}

.jobcom-pagination-btn {
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid #ddd;
  border-radius: 6px;
  background-color: #fff;
  color: #555;
  cursor: pointer;
  transition: all 0.3s;
}

.jobcom-pagination-btn:hover {
  background-color: #f5f0ff;
  border-color: #7743DB;
  color: #7743DB;
}

.jobcom-pagination-btn.active {
  background-color: #7743DB;
  border-color: #7743DB;
  color: #fff;
}

/* Media Queries */
@media (max-width: 1024px) {
  .jobcom-company-grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  }
}

@media (max-width: 768px) {
  .jobcom-container {
    flex-direction: column;
    padding: 20px 4%;
  }
  
  .jobcom-sidebar {
    width: 100%;
  }
  
  .jobcom-company-grid {
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  }
  
  .jobcom-header {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }
}

@media (max-width: 480px) {
  .jobcom-company-grid {
    grid-template-columns: 1fr;
  }
}
  