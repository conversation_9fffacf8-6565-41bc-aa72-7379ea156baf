/* eslint-disable no-unused-vars */
import React, { useEffect, useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faBriefcase,
  faCheckCircle,
  faUsers,
  faEye,
  faChartLine,
  faFileAlt,
  faMapMarkerAlt,
  faCalendarAlt,
  faSpinner,
  faExclamationTriangle,
  faBuilding,
  faIndustry,
  faSync,
  faInfoCircle,
  faChartBar,
  faChartPie,
  faSignOutAlt
} from '@fortawesome/free-solid-svg-icons';
import ApiService from '../../services/apiService';
import '../../css/AnalyticsDashboard.css';

const AnalyticsDashboard = () => {
  const [stats, setStats] = useState([
    { id: 1, title: 'Total Jobs', value: '0', icon: faBriefcase, color: '#8057ff', change: '0%' },
    { id: 2, title: 'Active Listings', value: '0', icon: faCheckCircle, color: '#10b981', change: '0%' },
    { id: 3, title: 'Total Views', value: '0', icon: faEye, color: '#3b82f6', change: '0%' },
    { id: 4, title: 'Applicants', value: '0', icon: faUsers, color: '#f59e0b', change: '0%' },
    { id: 5, title: 'Companies', value: '0', icon: faBuilding, color: '#ec4899', change: '0%' }
  ]);
  
  const [jobsByCategory, setJobsByCategory] = useState([]);
  const [companiesByIndustry, setCompaniesByIndustry] = useState([]);
  const [recentJobs, setRecentJobs] = useState([]);
  const [recentCompanies, setRecentCompanies] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [retryCount, setRetryCount] = useState(0);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Fetch both jobs and companies data with error handling
        const jobsPromise = ApiService.jobs.getDashboard().catch(err => {
          console.error("Error fetching jobs dashboard data:", err);
          return { data: { 
            stats: { totalJobs: 0, activeJobs: 0, totalViews: 0, estimatedApplicants: 0 },
            jobsByCategory: [],
            recentJobs: []
          }};
        });
        
        const companiesPromise = ApiService.companies.getDashboard().catch(err => {
          console.error("Error fetching companies dashboard data:", err);
          return { data: { data: { total_companies: 0, companies_by_industry: {}, newest_companies: [] }}};
        });
        
        const [jobsResponse, companiesResponse] = await Promise.all([jobsPromise, companiesPromise]);
        
        const jobsDashboardData = jobsResponse.data || { 
          stats: { totalJobs: 0, activeJobs: 0, totalViews: 0, estimatedApplicants: 0 },
          jobsByCategory: [],
          recentJobs: []
        };
        
        const companiesDashboardData = companiesResponse.data?.data || { 
          total_companies: 0, 
          companies_by_industry: {}, 
          newest_companies: [] 
        };
        
        // Update stats with both jobs and companies data
        setStats([
          { 
            id: 1, 
            title: 'Total Jobs', 
            value: jobsDashboardData.stats.totalJobs.toString(), 
            icon: faBriefcase, 
            color: '#8057ff', 
            change: '+' + (Math.round(jobsDashboardData.stats.totalJobs * 0.05)) + '%' 
          },
          { 
            id: 3, 
            title: 'Total Views', 
            value: jobsDashboardData.stats.totalViews >= 1000 ? (jobsDashboardData.stats.totalViews / 1000).toFixed(1) + 'K' : jobsDashboardData.stats.totalViews.toString(), 
            icon: faEye, 
            color: '#3b82f6', 
            change: '+' + (Math.round(jobsDashboardData.stats.totalViews * 0.02)) + '%' 
          }
        ]);
        
        // Set jobs by category data
        setJobsByCategory(jobsDashboardData.jobsByCategory?.slice(0, 7) || []); // Limit to top 7 categories
        
        // Set companies by industry data
        const companiesByIndustryArray = Object.entries(companiesDashboardData.companies_by_industry || {})
          .map(([industry, count]) => ({ 
            category: industry || 'Unknown', 
            count: count || 0
          }))
          .sort((a, b) => b.count - a.count) // Sort by count descending
          .slice(0, 7); // Limit to top 7 industries
          
        setCompaniesByIndustry(companiesByIndustryArray);
        
        // Process recent jobs data
        const formattedRecentJobs = (jobsDashboardData.recentJobs || []).map(job => ({
          id: job.job_id,
          position: job.job_title || 'Untitled Position',
          company: job.company_name || 'Unknown Company',
          location: job.main_topics || 'Not specified',
          datePosted: formatDatePosted(job.start_date || job.created_at),
          applications: Math.floor((job.views || 0) * 0.2) + 1 // Estimate based on views
        }));
        
        setRecentJobs(formattedRecentJobs);
        
        // Process recent companies data
        const formattedRecentCompanies = (companiesDashboardData.newest_companies || []).map(company => ({
          id: company.company_id,
          name: company.company_name || 'Unnamed Company',
          industry: company.company_industry || 'N/A',
          size: company.company_size || 'N/A',
          logoUrl: company.company_logo_url,
          dateAdded: formatDatePosted(company.created_at)
        }));
        
        setRecentCompanies(formattedRecentCompanies);
        setError(null);
      } catch (err) {
        console.error("Error fetching dashboard data:", err);
        setError("Failed to load dashboard data. Please try again.");
      } finally {
        setLoading(false);
      }
    };
    
    fetchDashboardData();
  }, [retryCount]);
  
  // Helper function to format date posted as "X days ago", "1 week ago", etc.
  const formatDatePosted = (dateString) => {
    if (!dateString) return 'Unknown';
    
    const date = new Date(dateString);
    const now = new Date();
    const diffInDays = Math.floor((now - date) / (1000 * 60 * 60 * 24));
    
    if (diffInDays === 0) return 'Today';
    if (diffInDays === 1) return 'Yesterday';
    if (diffInDays < 7) return `${diffInDays} days ago`;
    if (diffInDays < 14) return '1 week ago';
    if (diffInDays < 30) return `${Math.floor(diffInDays / 7)} weeks ago`;
    if (diffInDays < 60) return '1 month ago';
    return `${Math.floor(diffInDays / 30)} months ago`;
  };

  // Calculate max counts for chart scaling
  const maxJobCount = jobsByCategory.length > 0 ? 
    Math.max(...jobsByCategory.map(item => item.count)) : 0;
    
  const maxCompanyCount = companiesByIndustry.length > 0 ? 
    Math.max(...companiesByIndustry.map(item => item.count)) : 0;

  // Function to handle retry
  const handleRetry = () => {
    setRetryCount(prevCount => prevCount + 1);
  };

  // Function to handle logout
  const handleLogout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user'); // Also remove user data for complete logout
    window.location.href = '/admin/login';
  };

  return (
    <div className="analytics-dashboard modern-dashboard">
      <div className="dashboard-header">
        <h2><FontAwesomeIcon icon={faChartBar} /> Analytics Dashboard</h2>
        <div className="dashboard-actions">
          {!loading && (
            <button className="refresh-button" onClick={handleRetry} title="Refresh data">
              <FontAwesomeIcon icon={faSync} /> Refresh
            </button>
          )}
        </div>
      </div>
      
      {loading && (
        <div className="loading-container">
          <FontAwesomeIcon icon={faSpinner} spin size="2x" />
          <span>Loading dashboard data...</span>
        </div>
      )}
      
      {error && (
        <div className="error-container">
          <div className="error-message">
            <FontAwesomeIcon icon={faExclamationTriangle} />
            <span>{error}</span>
          </div>
          <button className="retry-button" onClick={handleRetry}>
            <FontAwesomeIcon icon={faSync} /> Retry
          </button>
        </div>
      )}
      
      {!loading && !error && (
        <>
          {/* Stats row */}
          <div className="stats-grid">
            {stats.map(stat => (
              <div key={stat.id} className="stat-card">
                <div className="stat-header">
                  <div className="stat-icon" style={{ backgroundColor: `${stat.color}15`, color: stat.color }}>
                    <FontAwesomeIcon icon={stat.icon} />
                  </div>
                  <span className={`stat-change ${stat.change.startsWith('+') ? 'positive' : 'negative'}`}>
                    {stat.change}
                  </span>
                </div>
                <div className="stat-value">{stat.value}</div>
                <div className="stat-title">{stat.title}</div>
              </div>
            ))}
          </div>

          {/* Two column layout for charts and tables */}
          <div className="dashboard-grid">
            {/* Jobs by Category Chart */}
            <div className="dashboard-card">
              <div className="card-header">
                <div className="card-title">
                  <FontAwesomeIcon icon={faChartPie} />
                  <span>Jobs by Category</span>
                </div>
                <div className="card-actions">
                  <span className="info-tooltip" title="Distribution of jobs across different categories">
                    <FontAwesomeIcon icon={faInfoCircle} />
                  </span>
                </div>
              </div>
              <div className="card-body">
                {jobsByCategory.length > 0 ? (
                  <div className="chart-container">
                    <div className="chart-bar-container">
                      {jobsByCategory.map((item, index) => (
                        <div key={index} className="chart-bar">
                          <div className="chart-bar-label">
                            <span>{item.category}</span>
                            <span>{item.count}</span>
                          </div>
                          <div 
                            className="chart-bar-value" 
                            style={{ 
                              width: `${(item.count / maxJobCount) * 100}%`,
                              backgroundColor: `hsl(${210 + index * 30}, 70%, 60%)`
                            }}
                          ></div>
                        </div>
                      ))}
                    </div>
                  </div>
                ) : (
                  <div className="no-data-message">
                    <FontAwesomeIcon icon={faInfoCircle} />
                    <span>No category data available</span>
                  </div>
                )}
              </div>
            </div>

            {/* Recent Jobs Table */}
            <div className="dashboard-card">
              <div className="card-header">
                <div className="card-title">
                  <FontAwesomeIcon icon={faFileAlt} />
                  <span>Recently Posted Jobs</span>
                </div>
                <div className="card-actions">
                  <span className="info-tooltip" title="Most recently posted job listings">
                    <FontAwesomeIcon icon={faInfoCircle} />
                  </span>
                </div>
              </div>
              <div className="card-body">
                {recentJobs.length > 0 ? (
                  <div className="table-responsive">
                    <table className="analytics-table">
                      <thead>
                        <tr>
                          <th>Job</th>
                          <th>Location</th>
                          <th>Posted</th>
                          <th>Applications</th>
                        </tr>
                      </thead>
                      <tbody>
                        {recentJobs.map(job => (
                          <tr key={job.id}>
                            <td>
                              <div className="job-title">{job.position}</div>
                              <div className="company-name">{job.company}</div>
                            </td>
                            <td>
                              <div className="location-display">
                                <FontAwesomeIcon icon={faMapMarkerAlt} />
                                <span>{job.location}</span>
                              </div>
                            </td>
                            <td>
                              <div className="date-display">
                                <FontAwesomeIcon icon={faCalendarAlt} />
                                <span>{job.datePosted}</span>
                              </div>
                            </td>
                            <td>
                              <span className="applications-badge">
                                {job.applications}
                              </span>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                ) : (
                  <div className="no-data-message">
                    <FontAwesomeIcon icon={faInfoCircle} />
                    <span>No recent jobs available</span>
                  </div>
                )}
              </div>
            </div>
          </div>

        </>
      )}
    </div>
  );
};

export default AnalyticsDashboard;