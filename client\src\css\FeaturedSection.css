/* Add flex row for featured content and social feed */
.featured-content-row {
  display: flex;
  flex-direction: row;
  gap: 30px;
  align-items: flex-start;
}

.featured-content {
  flex: 3 1 0;
  min-width: 0;
}

.social-feed {
  flex: 1 1 320px;
  min-width: 280px;
  max-width: 400px;
}
/* FeaturedSection.css */
.featured-container {
    display: flex;
    width: 100%;
    margin: 0 auto;
    gap: 30px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    padding: 0 20px;
    box-sizing: border-box;
    position: relative;
  }
  
  /* Featured Section Styles */
  .featured-section {
    flex: 3;
    width: 100%;
    position: relative;
  }
  
  .featured-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    position: relative;
    margin-top: -30px;
  }
  
  .featured-header h2 {
    font-size: 28px;
    font-weight: 700;
    margin: 0;
    margin-right: 15px;
  }
  
  .featured-underline {
    flex: 1;
    height: 3px;
    background-color: #9777FA;
  }
  
  .navigation-buttons {
    display: flex;
    gap: 10px;
    margin-left: 15px;
  }
  
  .nav-button {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    border: 1px solid #e0e0e0;
    background: transparent;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: #555;
  }
  
  .featured-content {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }
  
  .featured-main-article {
    border-radius: 10px;
    overflow: hidden;
    position: relative;
    height: 300px;
  }
  
  .article-image-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
  }
  
  .article-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .article-meta {
    display: flex;
    gap: 15px;
    align-items: center;
    margin-bottom: 10px;
  }
  
  .featured-main-article .article-meta {
    position: absolute;
    bottom: 60px;
    left: 20px;
    z-index: 2;
  }
  
  .featured-main-article .article-title {
    position: absolute;
    bottom: 20px;
    left: 20px;
    color: white;
    margin: 0;
    font-size: 22px;
    max-width: 80%;
    z-index: 2;
  }
  
  .featured-main-article:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 60%;
    background: linear-gradient(to top, rgba(0,0,0,0.7), transparent);
  }
  
  .article-category {
    background-color: #ff4757;
    color: white;
    padding: 4px 12px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
  }
  
  .article-date {
    color: #888;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 5px;
  }
  
  .featured-main-article .article-date {
    color: white;
  }
  
  .calendar-icon {
    font-size: 14px;
  }
  
  .featured-articles {
    display: flex;
    gap: 20px;
  }
  
  .article-card {
    flex: 1;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
  }
  
  .article-card .article-image-container {
    height: 200px;
    position: relative;
  }
  
  .share-button {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: rgba(255,255,255,0.8);
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 2;
  }
  
  .article-info {
    padding: 15px;
  }
  
  .article-card .article-title {
    margin: 10px 0 0;
    font-size: 18px;
    line-height: 1.4;
  }
  
  /* Social Feed Styles */
  .social-feed {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 15px;
  }
  
  .social-feed h3 {
    color: #9777FA;
    font-size: 28px;
    font-weight: 700;
    margin: 0 0 20px;
    padding-top: 5px;
    position: relative;
  }
  /* Remove underline from Social Feed header */
  .social-feed h3::after {
    display: none !important;
    content: none !important;
  }
  
  .social-media-item a{
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: 5px;
    cursor: pointer;
  }

  .social-media-item:hover {
    background-color: #fff;
  }
  
  .social-icon {
    width: 50px;
    height: 50px;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
  }
  
  .facebook {
    background-color: #3b5998;
  }
  
  .linkedin {
    background-color: #1877f2;
  }
  
  .instagram {
    background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
  }
  
  .youtube {
    background-color: #ff0000;
  }

  .tiktok{
    background-color: #000;
  }
  
  .social-stats {
    font-size: 14px;
    color: #444;
    display: flex;
    padding: 0px 40px 0 0  ;
    flex-direction: column;
    align-items: end;
    text-align: end;
    margin-left: 10px;
    min-width: 80px;
  }
  
  .count {
    font-weight: 700;
    font-size: 16px;
    line-height: 1.2;
    margin-bottom: 2px;
  }
  
  .stat-icon {
    font-size: 12px;
    margin-right: 5px;
  }
  
  .stat-label {
    font-size: 13px;
    color: #666;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1.2;
  }
  
  .social-action-btn {
    background: none;
    border: none;
    color: #9777FA;
    font-weight: 500;
    cursor: pointer;
    padding: 5px;
  }
  
  .promo-banner {
    margin-top: 10px;
    border-radius: 8px;
    overflow: hidden;
    height: 150px;
    position: relative;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
  
  .promo-content {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    padding: 20px;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  
  .promo-content p {
    color: white;
    font-size: 16px;
    font-weight: 600;
    margin: 0 0 15px;
    line-height: 1.4;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  }
  
  .purchase-btn {
    background-color: #ff4757;
    color: white;
    border: none;
    padding: 8px 20px;
    border-radius: 5px;
    font-weight: 500;
    cursor: pointer;
    width: fit-content;
    transition: all 0.2s;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  }
  
  .purchase-btn:hover {
    background-color: #ff2d43;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  }
  
  /* Company Cards Section */
.featured-companies {
  margin: 40px 0;
}

.company-cards-container {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 20px;
  margin-top: 20px;
}

.company-card {
  background: #fff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.08);
  transition: all 0.3s ease;
  text-align: center;
  border: 1px solid #f0f0f0;
  min-height: 140px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.company-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
  border-color: #9777FA;
}

.company-logo {
  width: 100%;
  height: 80px;
  object-fit: contain;
  margin-bottom: 12px;
  transition: transform 0.3s ease;
}

.company-card:hover .company-logo {
  transform: scale(1.1);
}

.company-name {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin: 0;
  line-height: 1.3;
}

.company-jobs-count {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

/* Responsive adjustments for company cards */
@media (max-width: 1200px) {
  .company-cards-container {
    grid-template-columns: repeat(4, 1fr);
    gap: 15px;
  }
}

@media (max-width: 992px) {
  .company-cards-container {
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
  }
  
  .company-card {
    padding: 15px;
    min-height: 120px;
  }
  
  .company-logo {
    height: 60px;
  }
}

@media (max-width: 768px) {
  .company-cards-container {
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }
  
  .company-card {
    padding: 12px;
    min-height: 100px;
  }
  
  .company-logo {
    height: 50px;
  }
  
  .company-name {
    font-size: 13px;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .featured-container {
      flex-direction: column;
      width: 100%;
    }
    
    .featured-articles {
      flex-direction: column;
    }
    
    .company-cards-container {
      grid-template-columns: repeat(2, 1fr);
      gap: 10px;
    }
    
    .company-card {
      padding: 12px;
      min-height: 100px;
    }
    
    .company-logo {
      height: 50px;
    }
  }