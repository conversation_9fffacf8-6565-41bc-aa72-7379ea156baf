import axios from 'axios';

// Create an axios instance with base URL
const api = axios.create({
  baseURL: process.env.REACT_APP_API_BASE_URL || 'http://localhost:5000/api',
  timeout: 30000, // 30 seconds timeout
  headers: {
    'Content-Type': 'application/json'
  },
  // Add retry configuration
  retry: 3,
  retryDelay: 1000
});

// Add a request interceptor to include auth token on all requests
api.interceptors.request.use(
  config => {
    // Get token from localStorage
    const token = localStorage.getItem('token');
    
    // If token exists, add it to the headers
    if (token) {
      config.headers['x-auth-token'] = token;
    }
    
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// Add a response interceptor to handle auth errors and retries
api.interceptors.response.use(
  response => response,
  async error => {
    const config = error.config;
    
    // Handle network errors with retry logic
    if (!error.response && config && !config.__isRetryRequest) {
      config.__isRetryRequest = true;
      config.__retryCount = config.__retryCount || 0;
      
      if (config.__retryCount < (config.retry || 3)) {
        config.__retryCount++;
        
        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, config.retryDelay || 1000));
        
        return api(config);
      }
    }
    
    // Handle 401 Unauthorized or 403 Forbidden errors
    if (error.response && (error.response.status === 401 || error.response.status === 403)) {
      // Clear invalid token
      localStorage.removeItem('token');
      
      // Only redirect to login if not on dashboard or admin pages
      const currentPath = window.location.pathname;
      if (!currentPath.includes('/dashboard') && currentPath !== '/jp-admin') {
        window.location.href = '/jp-admin';
      }
      // If on dashboard, let the component handle the error
    }
    
    return Promise.reject(error);
  }
);

export default api;