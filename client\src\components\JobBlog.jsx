import React, { useState, useEffect } from 'react';
import { FaSearch, FaBookmark, FaChevronLeft, FaChevronRight, FaSpinner, FaExclamationCircle } from 'react-icons/fa';
import { useNavigate } from 'react-router-dom';
import '../css/JobBlog.css';
import banner from "../assets/jobdetails.png";
import banneradd from "../assets/banneradd.png";
import { ApiService } from '../services/apiService';
import PageHelmet from './PageHelmet';

const JobBlog = () => {
  const navigate = useNavigate();
  const [blogs, setBlogs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const blogsPerPage = 6;

  const categories = [
    { name: 'Recruitment News', count: 36 },
    { name: 'Job Reviews', count: 28 },
    { name: 'Job Tools', count: 24 },
    { name: 'Full Time Job', count: 24 },
    { name: 'Work From Home', count: 18 },
    { name: 'Job Tips', count: 38 }
  ];

  // Force update the document title
  // useEffect(() => {
  //   document.title = loading ? 'Job Page | Loading Blogs' : 'Job Page | Blogs and Newsletters';
  // }, [loading]);

  // Fetch blogs from backend
  useEffect(() => {
    const fetchBlogs = async () => {
      try {
        setLoading(true);
        const response = await ApiService.blogs.getAll();
        
        const formattedBlogs = response.data.map(blog => ({
          id: blog.blog_id,
          title: blog.blog_title,
          author: blog.author || 'Admin',
          date: blog.posted_date ? new Date(blog.posted_date).toLocaleDateString('en-US', {
            day: '2-digit',
            month: 'long',
            year: 'numeric'
          }) : 'No date',
          timeAgo: blog.posted_date ? calculateTimeAgo(new Date(blog.posted_date)) : 'Unknown',
          image: blog.image_url || banner,
          excerpt: blog.blog_description || '',
          content: blog.blog_content || blog.blog_description || '',
          tags: blog.tags ? blog.tags.split(',') : [''],
          views: blog.views || 0,
          posted_date: blog.posted_date,
          image_url: blog.image_url,
          category: blog.category || 'Uncategorized'
        })).sort((a, b) => new Date(b.posted_date) - new Date(a.posted_date)); // Sort by newest first
        
        setBlogs(formattedBlogs);
        setError(null);
      } catch (err) {
        console.error("Error fetching blogs:", err);
        setError("Failed to load blogs. Please try again later.");
      } finally {
        setLoading(false);
      }
    };

    fetchBlogs();
    
    // Scroll to top when component mounts
    window.scrollTo(0, 0);
  }, []);

  // Helper functions
  const calculateTimeAgo = (date) => {
    const now = new Date();
    const diffInMs = now - date;
    const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

    if (diffInHours < 1) {
      const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
      return diffInMinutes <= 1 ? '1 minute ago' : `${diffInMinutes} minutes ago`;
    } else if (diffInHours < 24) {
      return diffInHours === 1 ? '1 hour ago' : `${diffInHours} hours ago`;
    } else if (diffInDays === 1) {
      return '1 day ago';
    } else if (diffInDays < 7) {
      return `${diffInDays} days ago`;
    } else {
      const diffInWeeks = Math.floor(diffInDays / 7);
      return diffInWeeks === 1 ? '1 week ago' : `${diffInWeeks} weeks ago`;
    }
  };

  const handleBlogClick = async (blog) => {
    try {
      // Increment view count in the backend
      await ApiService.blogs.incrementViews(blog.id);
      
      // Navigate to the blog details page
      navigate(`/blogs/${blog.id}`, { 
        state: { 
          blogData: {
            ...blog,
            views: blog.views + 1 // Update views locally for immediate feedback
          },
          relatedBlogs: getRelatedBlogs(blog.id)
        } 
      });
    } catch (error) {
      console.error("Error incrementing blog views:", error);
      // Still navigate even if view increment fails
      navigate(`/blogs/${blog.id}`, { 
        state: { 
          blogData: blog,
          relatedBlogs: getRelatedBlogs(blog.id)
        } 
      });
    }
  };

  const getRelatedBlogs = (currentBlogId) => {
    return blogs
      .filter(blog => blog.id !== currentBlogId)
      .sort(() => 0.5 - Math.random())
      .slice(0, 3);
  };

  const getLatestBlogs = () => {
    return [...blogs] // Create a copy to avoid mutating the original array
      .sort((a, b) => new Date(b.posted_date) - new Date(a.posted_date)) // Newest first
      .slice(0, 4);
  };

  const featuredBlogs = () => {
    return [...blogs] // Create a copy to avoid mutating the original array
      .sort((a, b) => new Date(a.posted_date) - new Date(b.posted_date)) // Oldest first
      .slice(0, 5);
  };

  // Filtering and pagination
  const filteredBlogs = blogs.filter(blog => {
    const matchesSearch = blog.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         blog.excerpt.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = !selectedCategory || 
                           (blog.category || 'Uncategorized') === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  const totalPages = Math.ceil(filteredBlogs.length / blogsPerPage);
  const startIndex = (currentPage - 1) * blogsPerPage;
  const endIndex = startIndex + blogsPerPage;
  const currentBlogs = filteredBlogs.slice(startIndex, endIndex);

  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1);
  };

  const handleCategoryFilter = (categoryName) => {
    setSelectedCategory(selectedCategory === categoryName ? '' : categoryName);
    setCurrentPage(1);
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const formatExcerpt = (text, maxLength = 150) => {
    if (!text) return '';
    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
  };

  if (loading) {
    return (
      <div className="job-blog-container">
        <PageHelmet title="Blogs" />
        <div className="loading-overlay">
          <FaSpinner className="spinner" />
          <p>Loading Blogs</p>
        </div>
      </div>
    );
  }

  return (
    <div className="job-blog-container">
      <PageHelmet 
        title="Blogs" 
        description="Expert advice, recruitment trends, and professional development resources for job seekers."
      />
      {/* Hero Section */}
      <div className="blog-hero">
        <div className="hero-content">
          <h1>Blogs and Newsletters</h1>
          <p>Expert advice, recruitment trends, and professional development resources</p>
          <div className="search-container">
            <FaSearch className="search-icon" />
            <input
              type="text"
              placeholder="Search articles, topics, or keywords..."
              value={searchTerm}
              onChange={handleSearch}
            />
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="blog-content-wrapper">
        {error && (
          <div className="error-notification">
            <span>{error}</span>
          </div>
        )}
            {/* Category Filter Tabs */}
            <div className="category-tabs">
              <button
                className={`tab-btn ${!selectedCategory ? 'active' : ''}`}
                onClick={() => handleCategoryFilter('')}
              >
                All Articles
              </button>
              {categories.map(category => (
                <button
                  key={category.name}
                  className={`tab-btn ${selectedCategory === category.name ? 'active' : ''}`}
                  onClick={() => handleCategoryFilter(category.name)}
                >
                  {category.name}
                </button>
              ))}
            </div>
        <div className="blog-layout">
          {/* Main Articles */}
          <main className="articles-main">
            {/* Articles Grid */}
            {currentBlogs.length > 0 ? (
              <div className="articles-grid">
                {currentBlogs.map(blog => (
                  <article key={blog.id} className="blog-card" onClick={() => handleBlogClick(blog)}>
                    <div className="card-image">
                      <img src={blog.image} alt={blog.title} />
                      {blog.category && (
                        <span className="category-badge">{blog.category}</span>
                      )}
                    </div>
                    <div className="card-content">
                      <div className="meta-info">
                        <span className="date">{blog.date}</span>
                      </div>
                      <h3 className="card-title">{blog.title}</h3>
                      <div className="card-footer">
                        <button className="read-more-btn">
                          Continue Reading
                          <FaChevronRight className="arrow-icon" />
                        </button>
                      </div>
                    </div>
                  </article>
                ))}
              </div>
            ) : (
              <div className="no-results">
                <h3>No articles found</h3>
                <p>{searchTerm ? `No results for "${searchTerm}"` : 'Try selecting a different category'}</p>
              </div>
            )}

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="pagination-controls">
                <button
                  className={`pagination-btn ${currentPage === 1 ? 'disabled' : ''}`}
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                >
                  <FaChevronLeft />
                </button>
                
                {Array.from({ length: totalPages }, (_, i) => (
                  <button
                    key={i + 1}
                    className={`pagination-btn ${currentPage === i + 1 ? 'active' : ''}`}
                    onClick={() => handlePageChange(i + 1)}
                  >
                    {i + 1}
                  </button>
                ))}
                
                <button
                  className={`pagination-btn ${currentPage === totalPages ? 'disabled' : ''}`}
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                >
                  <FaChevronRight />
                </button>
              </div>
            )}
          </main>

          {/* Sidebar */}
          <aside className="blog-sidebar">
            {/* Featured Blogs (showing oldest first) */}
            <div className="sidebar-widget">
              <h3 className="widget-title">Featured Blogs</h3>
              <div className="latest-articles">
                {featuredBlogs().map(blog => (
                  <div key={blog.id} className="latest-item" onClick={() => handleBlogClick(blog)}>
                    <div className="latest-image">
                      <img src={blog.image} alt={blog.title} />
                    </div>
                    <div className="latest-content">
                      <h4>{blog.title}</h4>
                      <span className="latest-date">{blog.date}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Latest Articles (showing newest first) */}
            {/* <div className="sidebar-widget">
              <h3 className="widget-title">Latest Blogs</h3>
              <div className="latest-articles">
                {getLatestBlogs().map(blog => (
                  <div key={blog.id} className="latest-item" onClick={() => handleBlogClick(blog)}>
                    <div className="latest-image">
                      <img src={blog.image} alt={blog.title} />
                    </div>
                    <div className="latest-content">
                      <h4>{blog.title}</h4>
                      <span className="latest-date">{blog.date}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div> */}

            {/* Newsletter/Ad Banner */}
            {/* <div className="sidebar-promo">
              <img src={banneradd} alt="Professional resources" />
            </div> */}
          </aside>
        </div>
      </div>
    </div>
  );
};

export default JobBlog;