const multer = require('multer');
const path = require('path');

// Configure storage
const storage = multer.memoryStorage();

// Handle CV file uploads with memory storage for processing in controllers
const cvUpload = multer({
  storage: storage,
  limits: { fileSize: 5 * 1024 * 1024 }, // 5MB limit
  fileFilter: (req, file, cb) => {
    // Skip validation if no file is selected or file is empty
    if (!file || !file.originalname) {
      return cb(null, true);
    }
    
    // Accept only CV file formats: PDF, DOC, DOCX
    const allowedMimeTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ];
    
    const allowedExtensions = ['.pdf', '.doc', '.docx'];
    const fileExtension = path.extname(file.originalname).toLowerCase();
    
    if (allowedMimeTypes.includes(file.mimetype) && allowedExtensions.includes(fileExtension)) {
      cb(null, true);
    } else {
      cb(new Error('Only PDF, DOC, and DOCX files are allowed'), false);
    }
  }
});

// Export the middleware
module.exports = cvUpload;