/* Job Form Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  animation: fadeIn 0.3s ease-out;
  backdrop-filter: blur(2px);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.job-modal {
  background: linear-gradient(to right bottom, #ffffff, #fafafa);
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(151, 119, 250, 0.1);
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #f0f0f0;
  position: sticky;
  top: 0;
  background-color: white;
  z-index: 10;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}

.modal-header h2 {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.close-button {
  background-color: transparent;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #6b7280;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.close-button:hover {
  background-color: #f3f4f6;
  color: #111827;
}

.modal-content {
  padding: 20px 24px;
}

.form-row {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.form-group {
  flex: 1;
  min-width: 250px;
  display: flex;
  flex-direction: column;
}

.form-group.full-width {
  flex-basis: 100%;
  min-width: 100%;
}

.form-group label {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-weight: 500;
  color: #374151;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  font-size: 0.95rem;
  transition: all 0.2s ease;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  border-color: #9777FA;
  outline: none;
  box-shadow: 0 0 0 3px rgba(151, 119, 250, 0.15);
}

.form-group.checkbox-group {
  display: flex;
  align-items: center;
  flex-direction: row !important;
  min-width: auto;
}

.checkbox-container {
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
}

.checkbox-container input[type="checkbox"] {
  width: auto;
  margin-right: 8px;
}

.checkbox-text {
  display: flex;
  align-items: center;
  font-size: 14px;
}

.hot-icon {
  color: #e11d48;
  margin-right: 6px;
}

.subtopic-input-container {
  display: flex;
  gap: 10px;
}

.subtopic-input-container input {
  flex: 1;
}

.add-subtopic-btn {
  background-color: #8057ff;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 10px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.add-subtopic-btn:hover {
  background-color: #7046e0;
}

.subtopics-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 10px;
}

.subtopic-tag {
  display: flex;
  align-items: center;
  background-color: #f3f4f6;
  border-radius: 4px;
  padding: 6px 10px;
  font-size: 13px;
}

.remove-subtopic-btn {
  background: transparent;
  border: none;
  color: #6b7280;
  margin-left: 6px;
  cursor: pointer;
  padding: 0;
  font-size: 12px;
}

.remove-subtopic-btn:hover {
  color: #dc2626;
}

.file-upload-container {
  position: relative;
  margin-bottom: 10px;
}

.file-input {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}

.file-upload-label {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: #f3f4f6;
  color: #4b5563;
  border: 1px dashed #d1d5db;
  border-radius: 6px;
  padding: 10px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: border-color 0.2s, color 0.2s;
}

.file-upload-label:hover {
  border-color: #8057ff;
  color: #8057ff;
}

.image-preview,
.thumbnail-preview,
.logo-preview {
  width: 100%;
  max-height: 120px;
  margin-top: 10px;
  border-radius: 6px;
  overflow: hidden;
  border: 1px solid #e5e7eb;
}

.image-preview img,
.thumbnail-preview img,
.logo-preview img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 24px;
  border-top: 1px solid #f0f0f0;
  margin-top: 16px;
  position: sticky;
  bottom: 0;
  background-color: white;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
}

.cancel-button {
  background-color: white;
  color: #666;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 10px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancel-button:hover {
  background-color: #f8f8f8;
  border-color: #d0d0d0;
}

.submit-button {
  background-color: #9777FA;
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.submit-button:hover {
  background-color: #8967ea;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(151, 119, 250, 0.3);
}

/* Make modal responsive on mobile */
@media (max-width: 768px) {
  .job-modal {
    width: 95%;
    max-height: 95vh;
  }
  
  .form-group {
    min-width: 100%;
  }
}

/* Add to your existing JobFormModal.css or create this file if it doesn't exist */

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.job-modal {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  width: 90%;
  max-width: 900px;
  max-height: 90vh;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #f0f0f0;
}

.modal-header h2 {
  margin: 0;
  font-size: 20px;
  color: #333;
}

.close-button {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #6b7280;
}

.close-button:hover {
  color: #ef4444;
}

.modal-content {
  padding: 20px 24px;
}

.form-row {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.form-group {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.form-group.full-width {
  width: 100%;
}

.form-group label {
  margin-bottom: 8px;
  font-weight: 500;
  color: #374151;
}

.form-group input[type="text"],
.form-group input[type="email"],
.form-group input[type="number"],
.form-group input[type="date"],
.form-group select,
.form-group textarea {
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 10px 14px;
  font-size: 0.95rem;
  transition: all 0.2s ease;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  border-color: #9777FA;
  outline: none;
  box-shadow: 0 0 0 3px rgba(151, 119, 250, 0.15);
}

.subtopic-input-container {
  display: flex;
  gap: 10px;
}

.add-subtopic-btn {
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0 15px;
  cursor: pointer;
  font-weight: 500;
}

.add-subtopic-btn:hover {
  background-color: #2563eb;
}

.subtopics-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 10px;
}

.subtopic-tag {
  background-color: #e5e7eb;
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.remove-subtopic-btn {
  background: none;
  border: none;
  cursor: pointer;
  color: #6b7280;
  padding: 0;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.remove-subtopic-btn:hover {
  color: #ef4444;
}

.file-upload-container {
  margin-bottom: 10px;
}

.file-input {
  display: none;
}

.file-upload-label {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: #f3f4f6;
  padding: 10px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  border: 1px dashed #d1d5db;
  transition: all 0.2s;
}

.file-upload-label:hover {
  background-color: #e5e7eb;
}

.image-preview,
.thumbnail-preview,
.logo-preview {
  margin-top: 10px;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  padding: 5px;
  position: relative;
}

.image-preview img,
.thumbnail-preview img,
.logo-preview img {
  width: 100%;
  height: auto;
  max-height: 150px;
  object-fit: contain;
  border-radius: 2px;
}

.remove-image-btn {
  position: absolute;
  top: 5px;
  right: 5px;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
}

.remove-image-btn:hover {
  background-color: rgba(0, 0, 0, 0.7);
}

.checkbox-container {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 10px;
  cursor: pointer;
}

.checkbox-text {
  display: flex;
  align-items: center;
  gap: 8px;
}

.hot-icon {
  color: #ef4444;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  padding: 16px 24px;
  border-top: 1px solid #f0f0f0;
  margin-top: 16px;
  position: sticky;
  bottom: 0;
  background-color: white;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
}

.cancel-button {
  background-color: #f3f4f6;
  color: #374151;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 10px 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.cancel-button:hover {
  background-color: #e5e7eb;
}

.submit-button {
  background-color: #3b82f6;
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.submit-button:hover {
  background-color: #2563eb;
}

.form-help-text {
  margin-top: 10px;
  font-size: 13px;
  color: #6b7280;
}

/* Responsive styles for mobile */
@media (max-width: 640px) {
  .form-row {
    flex-direction: column;
    gap: 15px;
  }
  
  .job-modal {
    width: 95%;
    max-height: 80vh;
  }
}

/* Styles for the sub-topics checkbox grid layout */
.subtopics-checkbox-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 10px;
  max-height: 450px;
  overflow-y: auto;
  margin-top: 12px;
  padding: 20px;
  background-color: #fafafa;
  border-radius: 10px;
  border: 1px solid #e5e5e5;
  box-shadow: inset 0 2px 6px rgba(0, 0, 0, 0.04);
  scroll-behavior: smooth;
  will-change: scroll-position;
  contain: layout style;
}

/* Custom styling for checkbox items */
.subtopic-checkbox {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-radius: 8px;
  background-color: #ffffff;
  border: 1px solid #e0e0e0;
  transition: all 0.2s ease-in-out;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.06);
  cursor: pointer;
  min-height: 48px;
  height: auto;
  flex-shrink: 0;
}

/* Hide the default checkbox */
.subtopic-checkbox input[type="checkbox"] {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

/* Create a custom checkbox */
.subtopic-checkbox label {
  position: relative;
  cursor: pointer;
  font-size: 0.95rem;
  padding-left: 32px;
  user-select: none;
  color: #333;
  font-weight: 500;
  width: 100%;
  line-height: 1.4;
  word-wrap: break-word;
}

.subtopic-checkbox label:before {
  content: '';
  position: absolute;
  left: 0;
  top: 2px;
  width: 20px;
  height: 20px;
  border: 2px solid #d1d5db;
  background-color: #fff;
  border-radius: 5px;
  transition: all 0.2s ease;
}

/* Style the checkmark/indicator */
.subtopic-checkbox label:after {
  content: '';
  position: absolute;
  left: 7px;
  top: 5px;
  width: 6px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
  opacity: 0;
  transition: all 0.2s ease;
}

/* Show the checkmark when checked */
.subtopic-checkbox input:checked ~ label:before {
  background-color: #9777FA;
  border-color: #9777FA;
}

.subtopic-checkbox input:checked ~ label:after {
  opacity: 1;
}

.subtopic-checkbox:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  border-color: #9777FA;
  background-color: #f8f7ff;
}

/* Add a subtle animation for the checked state */
.subtopic-checkbox input:checked ~ label {
  color: #1f2937;
  font-weight: 600;
}

/* Selected counter styling */
.small-text {
  font-size: 0.8rem;
  color: #9777FA;
  font-weight: normal;
  margin-left: 10px;
  background-color: rgba(151, 119, 250, 0.1);
  padding: 2px 8px;
  border-radius: 12px;
}

/* Add a selected state for checkboxes */
.subtopic-checkbox.selected {
  border-color: #9777FA;
  background-color: rgba(151, 119, 250, 0.05);
}

/* Custom scrollbar for the checkboxes container */
.subtopics-checkbox-container::-webkit-scrollbar {
  width: 8px;
}

.subtopics-checkbox-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

.subtopics-checkbox-container::-webkit-scrollbar-thumb {
  background: #9777FA;
  border-radius: 10px;
}

.subtopics-checkbox-container::-webkit-scrollbar-thumb:hover {
  background: #845bf7;
}

/* Make the label section look nicer */
.form-group label {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-weight: 500;
}

/* Add a nice animation for checkbox selection */
.subtopic-checkbox {
  position: relative;
}

.subtopic-checkbox.selected {
  background-color: rgba(151, 119, 250, 0.05);
  border-color: #9777FA;
}

@keyframes pulse {
  0% {
    opacity: 0.8;
    transform: scale(0);
  }
  50% {
    opacity: 0.4;
  }
  100% {
    opacity: 0;
    transform: scale(1.8);
  }
}

/* Responsive styling for checkbox grid */
@media (max-width: 768px) {
  .subtopics-checkbox-container {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    max-height: 250px;
  }
}

@media (max-width: 480px) {
  .subtopics-checkbox-container {
    grid-template-columns: 1fr;
    max-height: 200px;
  }
  
  .subtopic-checkbox {
    padding: 10px;
  }
  
  .small-text {
    display: block;
    margin-left: 0;
    margin-top: 4px;
  }
  
  .form-group label {
    flex-direction: column;
    align-items: flex-start;
  }
}

/* Add these styles at the end of the file for the company selector */

.company-selector-container {
  position: relative;
  width: 100%;
  z-index: 100; /* Ensure dropdown appears above other elements */
}

.company-input-container {
  position: relative;
  display: flex;
  align-items: center;
  cursor: pointer;
  border: 1px solid #e0e0e0; 
  border-radius: 6px;
  background-color: #fff;
  transition: all 0.2s;
}

.company-input-container:hover {
  border-color: #9777FA;
  box-shadow: 0 0 0 1px rgba(151, 119, 250, 0.1);
}

.company-input-container input {
  width: 100%;
  padding: 10px 12px;
  padding-right: 30px; /* Space for the dropdown icon */
  border: none;
  border-radius: 6px;
  outline: none;
  background: transparent;
}

.dropdown-icon {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: #6b7280;
  pointer-events: none;
  transition: transform 0.2s;
}

/* Rotate icon when dropdown is open */
.company-input-container.open .dropdown-icon {
  transform: translateY(-50%) rotate(180deg);
}

.company-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  max-height: 250px;
  overflow-y: auto;
  background-color: white;
  border: 1px solid #e5e7eb;
  border-top: none;
  border-radius: 0 0 6px 6px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  z-index: 200;
  margin-top: -1px;
}

.company-dropdown-item {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  cursor: pointer;
  transition: background-color 0.2s;
  border-bottom: 1px solid #f3f4f6;
}

.company-dropdown-item:last-child {
  border-bottom: none;
}

.company-dropdown-item:hover {
  background-color: #f3f4f6;
}

.company-logo-small {
  width: 30px;
  height: 30px;
  min-width: 30px;
  border-radius: 4px;
  overflow: hidden;
  margin-right: 10px;
  background-color: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #e0e0e0;
}

.company-logo-small img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.company-logo-small svg {
  color: #9ca3af;
  font-size: 18px;
}

.no-companies-message {
  padding: 15px;
  color: #6b7280;
  text-align: center;
  font-style: italic;
}

.logo-preview-with-action {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.logo-preview-with-action .image-preview {
  width: 100px;
  height: 100px;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f9fafb;
  padding: 5px;
}

.logo-preview-with-action .image-preview img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.remove-logo-btn {
  padding: 6px 12px;
  background-color: #fee2e2;
  color: #b91c1c;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: background-color 0.2s;
}

.remove-logo-btn:hover {
  background-color: #fecaca;
}

/* Company Selector Styles */
.company-selector {
  position: relative;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.company-select-dropdown {
  position: relative;
  width: 100%;
}

.company-select-button {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 10px 12px;
  background-color: white;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.95rem;
  transition: all 0.2s ease;
  text-align: left;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
}

.company-select-button:hover {
  border-color: #9777FA;
}

.company-list-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  max-height: 250px;
  overflow-y: auto;
  background-color: white;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  z-index: 1000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-top: 4px;
}

.company-item {
  display: flex;
  align-items: center;
  padding: 10px 12px;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
}

.company-item:last-child {
  border-bottom: none;
}

.company-item:hover {
  background-color: #f8f9fa;
}

.company-item-logo {
  width: 32px;
  height: 32px;
  border-radius: 4px;
  margin-right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f3f4f6;
  overflow: hidden;
}

.company-item-logo img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.company-item-logo svg {
  color: #9ca3af;
  font-size: 16px;
}

.company-name-input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  font-size: 0.95rem;
  transition: all 0.2s ease;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
}

.company-name-input:focus {
  border-color: #9777FA;
  outline: none;
  box-shadow: 0 0 0 3px rgba(151, 119, 250, 0.15);
}

.no-companies {
  padding: 12px;
  text-align: center;
  color: #6b7280;
  font-style: italic;
}

/* Logo Preview Styles */
.logo-preview-container {
  margin-top: 10px;
  width: 80px;
  height: 80px;
  border-radius: 6px;
  overflow: hidden;
  border: 1px dashed #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.logo-preview-container img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.remove-logo-button {
  position: absolute;
  top: 3px;
  right: 3px;
  width: 22px;
  height: 22px;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;
  font-size: 12px;
  color: #ef4444;
}

.remove-logo-button:hover {
  background-color: rgba(255, 255, 255, 1);
}

/* Style for required field indicators */
.form-group label span.required-field,
.form-group label span[style*="color: red"] {
  color: #ff4d4f;
  margin-left: 4px;
}

/* Style for invalid input fields */
/* .form-group input:invalid,
.form-group select:invalid {
  border-color: #ff4d4f;
} */

/* Style for error messages */
.error-message {
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
  color: #ff4d4f;
  padding: 10px 15px;
  border-radius: 4px;
  margin-bottom: 20px;
  font-weight: 500;
  display: flex;
  align-items: center;
}

.error-message svg {
  margin-right: 8px;
}

/* Add these styles for the manual company entry */
.manual-company-entry {
  padding: 12px;
  border-top: 1px solid #e8e8e8;
  margin-top: 8px;
}

.manual-company-entry label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  color: #555;
}

.manual-company-entry input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  margin-bottom: 8px;
}

.apply-company-btn {
  background-color: #1890ff;
  color: white;
  border: none;
  padding: 5px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.apply-company-btn:hover {
  background-color: #40a9ff;
}