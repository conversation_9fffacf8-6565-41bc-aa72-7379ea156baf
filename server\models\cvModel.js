const db = require('../config/db');

const CV = {
  // Get all CV submissions
  getAll: (callback) => {
    const query = "SELECT * FROM cv_submissions ORDER BY created_at DESC";
    db.query(query, (err, results) => {
      if (err) {
        console.error("Database query failed:", err);
        return callback(err, null);
      }
      callback(null, results);
    });
  },

  // Get CV by national ID
  getByNationalId: (nationalId, callback) => {
    const query = "SELECT * FROM cv_submissions WHERE national_id = ?";
    db.query(query, [nationalId], callback);
  },

  // Create or update CV submission (upsert based on national_id)
  upsert: (cvData, callback) => {
    const newCV = {
      national_id: cvData.national_id,
      full_name: cvData.full_name || '',
      email: cvData.email || '',
      phone: cvData.phone || '',
      gender: cvData.gender || '',
      skills: cvData.skills || '',
      cv_file_url: cvData.cv_file_url || '',
      cv_file_name: cvData.cv_file_name || '',
      additional_notes: cvData.additional_notes || '',
      company_name: cvData.company_name || '',
      position: cvData.position || '',
      updated_at: new Date()
    };
    
    // Check if record exists
    const checkQuery = "SELECT national_id FROM cv_submissions WHERE national_id = ?";
    db.query(checkQuery, [cvData.national_id], (err, results) => {
      if (err) {
        console.error("Error checking existing CV:", err);
        return callback(err, null);
      }
      
      if (results.length > 0) {
        // Update existing record
        const updateFields = Object.keys(newCV).filter(key => key !== 'national_id');
        const updatePlaceholders = updateFields.map(field => `${field} = ?`).join(', ');
        const updateValues = updateFields.map(field => newCV[field]);
        updateValues.push(cvData.national_id); // Add national_id for WHERE clause
        
        const updateQuery = `UPDATE cv_submissions SET ${updatePlaceholders} WHERE national_id = ?`;
        
        db.query(updateQuery, updateValues, (err, result) => {
          if (err) {
            console.error("Error updating CV:", err);
            return callback(err, null);
          }
          callback(null, { ...result, isUpdate: true });
        });
      } else {
        // Insert new record
        newCV.created_at = new Date();
        const fields = Object.keys(newCV);
        const placeholders = fields.map(() => '?').join(', ');
        const values = fields.map(field => newCV[field]);
        
        const insertQuery = `INSERT INTO cv_submissions (${fields.join(', ')}) VALUES (${placeholders})`;
        
        db.query(insertQuery, values, (err, result) => {
          if (err) {
            console.error("Error creating CV:", err);
            return callback(err, null);
          }
          callback(null, { ...result, isUpdate: false });
        });
      }
    });
  },

  // Delete CV by national ID
  delete: (nationalId, callback) => {
    const query = "DELETE FROM cv_submissions WHERE national_id = ?";
    db.query(query, [nationalId], (err, result) => {
      if (err) {
        console.error("Error deleting CV:", err);
        return callback(err, null);
      }
      callback(null, result);
    });
  },

  // Get dashboard analytics
  getDashboardAnalytics: (callback) => {
    const queries = {
      totalSubmissions: "SELECT COUNT(*) as count FROM cv_submissions",
      recentSubmissions: "SELECT COUNT(*) as count FROM cv_submissions WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)",
      genderDistribution: "SELECT gender, COUNT(*) as count FROM cv_submissions GROUP BY gender"
    };
    
    const results = {};
    let completed = 0;
    const totalQueries = Object.keys(queries).length;
    
    Object.keys(queries).forEach(key => {
      db.query(queries[key], (err, result) => {
        if (err) {
          console.error(`Error in ${key} query:`, err);
          results[key] = null;
        } else {
          results[key] = result;
        }
        
        completed++;
        if (completed === totalQueries) {
          callback(null, results);
        }
      });
    });
  }
};

module.exports = CV;