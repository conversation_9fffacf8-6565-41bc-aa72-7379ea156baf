/* Urgent tag for job cards */
.jobcard-urgent-tag {
  position: absolute;
  top: 10px;
  right: 10px;
  background: linear-gradient(45deg, #ff4757, #ff3742);
  color: white;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 10px;
  font-weight: bold;
  z-index: 2;
  box-shadow: 0 2px 8px rgba(255, 71, 87, 0.12);
  animation: pulse 2s infinite;
}
  .job-card {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    position: relative;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05), 0 2px 8px rgba(0, 0, 0, 0.08);
    border: 1px solid #f5f5f5;
    margin-bottom: 16px;
    margin-left: 0;
    width: 100%;
    transition: transform 0.2s, box-shadow 0.2s;
    display: flex;
    align-items: start;
    text-align: left;
    flex-direction: column;
    height: 100%;
    padding: 0;
    gap: 0;
  }
  
  .job-card *,
  .job-card *::before,
  .job-card *::after,
  .job-card .job-card-content *,
  .job-card .job-card-content,
  .job-card .company-info-urgent,
  .job-card .company-details-urgent,
  .job-card .company-name-urgent,
  .job-card .job-title,
  .job-card .job-meta,
  .job-card .job-location,
  .job-card .job-time,
  .job-card .job-footer,
  .job-card .job-salary {
    text-align: left !important;
    justify-content: flex-start !important;
    align-items: flex-start !important;
  }
  
  .job-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1), 0 4px 12px rgba(0, 0, 0, 0.12);
  }
  
  /* JobCard specific classes to avoid conflicts with JobListingPage */
  .hot-tag {
    position: absolute;
    top: 10px;
    left: 10px;
    background: #ff6b00;
    color: white;
    padding: 8px 10px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    z-index: 10;
    text-align: center;
  }
  
  .job-card .job-card-image {
    width: 100%;
    height: 200px;
    overflow: hidden;
    background-color: #f5f5f5; /* Fallback background while image loads */
    flex-shrink: 0;
  }
  
  .job-card .job-card-image img {
    width: 100%;
    /* height: 100%; */
    object-fit: contain;
    object-position: center;
    transition: transform 0.3s ease;
    background: #fff;
    display: block;
  }
  
  .job-card:hover .job-card-image img {
    transform: scale(1.05);
  }
  
  .job-card .job-card-content {
    padding: 12px 16px 12px 16px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;
    text-align: left;
  }
  
  .job-card .company-info,
  .job-card .company-info-urgent {
    display: flex;
    align-items: center;
    text-align: left;
  }
  
  .job-card .company-logo,
  .job-card .company-logo-urgent {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 8px;
    border: 1px solid #e0e0e0;
    background-color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }
  
  .job-card .company-logo img,
  .job-card .company-logo-urgent img {
    width: 24px;
    height: 24px;
    object-fit: cover;
    display: block;
    margin: 0 auto;
  }
  
  .job-card .company-name,
  .job-card .company-name-urgent {
    font-size: 14px;
    color: #666;
    font-weight: 500;
    text-align: left;
  }
  
  .job-card .company-details-urgent {
    display: flex;
    flex-direction: column;
    text-align: left;
  }
  
  .job-card .job-title {
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 6px;
    color: #333;
    line-height: 1.3;
    max-height: 48px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
  
  .job-card .job-meta {
    display: flex;
    font-size: 13px;
    color: #777;
    margin-bottom: 8px;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: flex-start;
    align-items: flex-start;
  }
  
  .job-card .job-location, 
  .job-card .job-time {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin-right: 10px;
    margin-bottom: 2px;
    text-align: left;
  }
  
  .job-card .icon {
    margin-right: 6px;
    font-size: 14px;
    width: 15px;
    height: 15px;
    color: #8057ff;
  }
  
  .job-card .icon-small {
    margin-right: 4px;
    font-size: 12px;
    color: #999;
  }
  
  .job-card .job-footer {
    display: flex;
    justify-content: space-between !important;
    align-items: center;
    margin-top: 8px;
    width: 100%;
    text-align: left !important;
  }
  
  .job-card .job-salary {
    font-weight: bold;
    color: #8057ff;
    font-size: 18px;
    margin-right: 8px;
    text-align: left !important;
  }
  
  .job-card .job-stats {
     display: flex;
     font-size: 12px;
     color: #999;
     gap: 8px;
     margin-left: auto;
   }
 
   .job-card .time-posted {
     margin-left: auto;
     text-align: right !important;
     justify-content: flex-end !important;
     align-items: center !important;
   }
   
   /* Override global flex-start for job-footer to maintain space-between layout */
   .job-card .job-footer {
     justify-content: space-between !important;
     align-items: center !important;
   }
  
  .job-card .view-count {
    display: flex;
    align-items: center;
  }
  
  /* Responsive adjustments */
  @media (max-width: 576px) {
    .job-card .job-title {
      font-size: 16px;
      max-height: 42px;
    }
  
    .job-card .job-card-image {
      height: 120px;
    }
    
    .job-card .job-salary {
      font-size: 18px;
    }
  }

/* Add styles for view count at the end of the file */
.job-stats {
  display: flex;
  align-items: center;
  gap: 12px;
}

.time-posted {
  font-size: 0.85rem;
  color: #777;
}

.view-count {
  font-size: 0.85rem;
  color: #777;
  display: flex;
  align-items: center;
  gap: 4px;
}

.views-icon {
  font-size: 0.8rem;
  color: #666;
}

/* Add styles for the posted date in the right corner */
.job-card .company-info-urgent {
  display: flex;
  align-items: center;
  position: relative;
  width: 100%;
  text-align: left;
}

/* Add styles for the job date in the job footer */
.job-card .job-date {
  font-size: 0.85rem;
  color: #777;
  margin-left: auto;
  text-align: right;
  }