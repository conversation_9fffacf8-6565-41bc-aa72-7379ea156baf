{"name": "client", "version": "0.1.0", "private": true, "homepage": "https://jobpage.lk", "proxy": "http://localhost:5000", "dependencies": {"@fortawesome/fontawesome-free": "^6.7.2", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.9.0", "http-proxy-middleware": "^2.0.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-helmet": "^6.1.0", "react-helmet-async": "^2.0.5", "react-icons": "^5.5.0", "react-router-dom": "^7.4.1", "react-scripts": "5.0.1", "react-slick": "^0.30.3", "slick-carousel": "^1.8.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-app-rewired --openssl-legacy-provider start", "build": "react-app-rewired build", "test": "react-app-rewired test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"react-app-rewired": "^2.2.1", "webpack-dev-server": "4.6.0"}}