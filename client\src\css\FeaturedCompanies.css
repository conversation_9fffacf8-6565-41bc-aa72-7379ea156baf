/* FeaturedCompanies.css */

.featured-companies-container {
  margin: 20px 0;
  padding: 30px calc(2% + 210px);
  width: 100%;
  max-width: none;

  box-sizing: border-box;
}

.featured-companies-container h2 {
  font-size: 28px;
  margin-bottom: 30px;
  color: #333;
  text-align: center; /* Center the heading */
}

.company-slider-wrapper {
  display: flex;
  align-items: center;
  position: relative;
  width: 100%;
  max-width: 1200px;
  padding: 10px 0;
  margin: 0 auto; /* Center the slider */
}

.company-list {
  display: flex;
  overflow-x: auto;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none;
  gap: 15px; /* Reduced gap for more items */
  padding: 20px 40px; /* Added horizontal padding for arrow buttons */
  width: 100%;
  flex-grow: 1;
  align-items: center;
  justify-content: flex-start; /* Ensure items start from the left */
}

/* Hide scrollbar for Chrome, Safari and Opera */
.company-list::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Edge */
}

/* Make company boxes more compact to fit more */
.company-name-badge {
  min-width: 140px; /* Smaller width to fit more logos */
  height: 80px; /* Reduced height */
  margin: 0 5px; /* Reduced side margins */
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px; /* Smaller border radius */
  padding: 8px; /* Reduced padding */
  transition: transform 0.2s ease;
}

.company-name-badge:hover {
  transform: scale(1.05);
}

/* Make logo images much larger within their containers */
.featured-companies-container .company-name-badge .company-logo {
  display: block !important;
  width: 80%; /* Slightly reduced percentage */
  height: 80%; /* Slightly reduced percentage */
  object-fit: contain; /* Maintain aspect ratio */
  filter: grayscale(0%);
  opacity: 1;
  transition: all 0.3s ease;
}

.company-name-badge:hover .company-logo {
  transform: scale(1.08); /* Slightly reduced to prevent overflow */
}

.slider-arrow {
  background: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  position: absolute;
  z-index: 2;
}

.slider-arrow:hover {
  background-color: #f7f7f7;
}

.slider-arrow:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.3);
}

.left-arrow {
  left: 10px;
}

.right-arrow {
  right: 10px;
}

/* Responsive adjustments */
@media (max-width: 1400px) {
  .featured-companies-container {
    padding: 30px calc(2% + 80px);
  }
}

@media (max-width: 768px) {
  .featured-companies-container {
    padding: 20px calc(2% + 30px);
  }
  
  .featured-companies-container h2 {
    font-size: 24px;
    margin-bottom: 25px;
  }

  .company-list {
    gap: 12px;
    padding: 20px 25px;
  }
  
  .company-name-badge {
    padding: 8px 16px;
    min-width: 80px;
    font-size: 13px;
  }

  .slider-arrow {
    width: 32px;
    height: 32px;
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .featured-companies-container {
    padding: 15px calc(2% + 15px);
  }

  .featured-companies-container h2 {
    font-size: 20px;
    margin-bottom: 20px;
  }

  .company-list {
    gap: 10px;
  }
  
  .company-name-badge {
    padding: 6px 12px;
    min-width: 70px;
    font-size: 12px;
  }

  .slider-arrow {
    width: 28px;
    height: 28px;
    font-size: 10px;
  }
}
