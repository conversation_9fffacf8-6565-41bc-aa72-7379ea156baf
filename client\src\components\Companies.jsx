import React, { useState, useEffect, useMemo } from 'react';
import '../css/Companies.css';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faBriefcase, faBuilding, faSearch, faStar } from '@fortawesome/free-solid-svg-icons';
import { FaSpinner } from 'react-icons/fa';
import ApiService from '../services/apiService';
import { Link } from 'react-router-dom';
import PageHelmet from './PageHelmet';

const CACHE_KEY = 'companies_data';
const CACHE_DURATION = 5 * 60 * 1000;

const Companies = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [companies, setCompanies] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeFilter, setActiveFilter] = useState('all');
  
  const refreshData = () => {
    localStorage.removeItem(CACHE_KEY);
    window.location.reload();
  };

  useEffect(() => {
    const fetchCompanies = async () => {
      try {
        const cachedData = localStorage.getItem(CACHE_KEY);
        if (cachedData) {
          const { data, timestamp } = JSON.parse(cachedData);
          if (Date.now() - timestamp < CACHE_DURATION) {
            setCompanies(data);
            setLoading(false);
            setError(null);
            return;
          }
        }
        
        setLoading(true);
        const [companiesResponse, jobsResponse] = await Promise.all([
          ApiService.companies.getAll(),
          ApiService.jobs.getAll()
        ]);
        
        const allJobs = jobsResponse.data || [];
        const jobCounts = allJobs.reduce((acc, job) => {
          acc[job.company_name] = (acc[job.company_name] || 0) + 1;
          return acc;
        }, {});
        
        const formattedCompanies = (companiesResponse.data.data || []).map((company, index) => ({
          id: company.company_id,
          name: company.company_name,
          logo: company.company_logo_url || `https://via.placeholder.com/200x200?text=${company.company_name.charAt(0)}`,
          industry: company.industry || 'Business',
          location: company.location || 'Sri Lanka',
          rating: (Math.random() * 2 + 3).toFixed(1),
          jobs: jobCounts[company.company_name] || 0,
          website: company.company_website || '#',
          color: ['#6C5CE7', '#00B894', '#0984E3', '#FD79A8', '#FDCB6E', '#E17055', '#00CEC9', '#A29BFE'][index % 8]
        }));
        
        localStorage.setItem(CACHE_KEY, JSON.stringify({
          data: formattedCompanies,
          timestamp: Date.now()
        }));
        
        setCompanies(formattedCompanies);
        setError(null);
      } catch (err) {
        console.error('Error fetching companies:', err);
        setError('Failed to load companies. Please try again later.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchCompanies();
    
    // Scroll to top when component mounts
    window.scrollTo(0, 0);
  }, []);
  
  const filteredCompanies = useMemo(() => {
    return companies.filter(company => {
      const matchesSearch = company.name.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesFilter = activeFilter === 'all' || 
                         (activeFilter === 'hiring' && company.jobs > 0);
      return matchesSearch && matchesFilter;
    });
  }, [companies, searchTerm, activeFilter]);

  // Force update the document title
  useEffect(() => {
    document.title = 'Job Page | Companies';
  }, []);

  return (
    <div className="companies-container">
      <PageHelmet 
        title="Companies" 
        description="Discover leading companies and explore career opportunities with top employers across industries."
      />
      <div className="companies-hero">
        <div className="hero-content">
          <h1>Discover Leading Companies</h1>
          <p>Explore career opportunities with top employers across industries</p>
          <div className="search-container">
            <FontAwesomeIcon icon={faSearch} className="search-icon" />
            <input
              type="text"
              placeholder="Search companies..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>
      </div>

      <div className="companies-main">
        <div className="filter-tabs">
          <button 
            className={activeFilter === 'all' ? 'active' : ''}
            onClick={() => setActiveFilter('all')}
          >
            All Companies
          </button>

          <button 
            className={activeFilter === 'hiring' ? 'active' : ''}
            onClick={() => setActiveFilter('hiring')}
          >
            Currently Hiring
          </button>
        </div>

        {loading ? (
          <div className="loading-overlay">
            <FaSpinner className="spinner" />
            <p>Loading companies...</p>
          </div>
        ) : error ? (
          <div className="error-message" style={{margin: '0 auto'}}>
            <p>{error}</p>
   
          </div>
        ) : filteredCompanies.length === 0 ? (
          <div className="no-results">
            <FontAwesomeIcon icon={faBuilding} size="3x" />
            <h3>No companies found</h3>
            <p>Try adjusting your search or filters</p>
          </div>
        ) : (
          <div className="companies-grid">
            {filteredCompanies.map(company => (
              <Link 
                to={`/company/${company.id}`} 
                key={company.id} 
                className={`company-card ${company.featured ? 'featured' : ''}`}
              >
                <div className="company-logo" style={{ backgroundColor:'#fff' }}>
                  {company.logo ? (
                    <img 
                      src={company.logo} 
                      alt={company.name}
                      onError={(e) => {
                        e.target.onerror = null;
                        e.target.src = `https://via.placeholder.com/200x200?text=${company.name.charAt(0)}`;
                      }}
                    />
                  ) : (
                    <span>{company.name.charAt(0)}</span>
                  )}
                </div>
                <div className="company-info">
                  <h3>{company.name}</h3>
                 
                  <div className="industry  ">
                    <p><FontAwesomeIcon icon={faBriefcase} /> {company.jobs} {company.jobs === 1 ? 'opening' : 'openings'}</p>

                  </div>
                </div>
              </Link>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default Companies;