/* NotFound.css - 404 Error Page Styles */

.not-found-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f9f5fa 0%, #ffffff 50%, #f0f8ff 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  font-family: 'Inter', sans-serif;
  position: relative;
  overflow: hidden;
}

/* Enhanced Background decoration */
.not-found-container::before {
  content: '';
  position: absolute;
  top: -30%;
  right: -30%;
  width: 60%;
  height: 60%;
  background: radial-gradient(circle, rgba(119, 67, 219, 0.08) 0%, rgba(128, 87, 255, 0.04) 40%, transparent 70%);
  z-index: 1;
  animation: float 6s ease-in-out infinite;
}

.not-found-container::after {
  content: '';
  position: absolute;
  bottom: -30%;
  left: -30%;
  width: 60%;
  height: 60%;
  background: radial-gradient(circle, rgba(128, 87, 255, 0.06) 0%, rgba(119, 67, 219, 0.03) 40%, transparent 70%);
  z-index: 1;
  animation: float 8s ease-in-out infinite reverse;
}

/* Additional decorative elements */
.not-found-content::before {
  content: '';
  position: absolute;
  top: 20px;
  right: 20px;
  width: 100px;
  height: 100px;
  background: linear-gradient(45deg, rgba(119, 67, 219, 0.1), rgba(128, 87, 255, 0.05));
  border-radius: 50%;
  z-index: -1;
  animation: pulse 4s ease-in-out infinite;
}

.not-found-content::after {
  content: '';
  position: absolute;
  bottom: 20px;
  left: 20px;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, rgba(128, 87, 255, 0.08), rgba(119, 67, 219, 0.04));
  border-radius: 50%;
  z-index: -1;
  animation: pulse 5s ease-in-out infinite reverse;
}

.not-found-content {
  max-width: 1400px; /* Increased from 1200px */
  width: 95%; /* Increased from 100% to use more screen width */
  text-align: center;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 32px;
  padding: 100px 80px; /* Increased padding for more spacious feel */
  box-shadow:
    0 32px 80px rgba(0, 0, 0, 0.12),
    0 16px 40px rgba(0, 0, 0, 0.08),
    0 8px 20px rgba(119, 67, 219, 0.1);
  position: relative;
  z-index: 2;
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

/* Error Icon Section */
.error-icon-section {
  margin-bottom: 40px;
  position: relative;
}

.error-icon {
  font-size: 100px;
  color: #7743DB;
  margin-bottom: 30px;
  opacity: 0.9;
  animation: bounce 3s ease-in-out infinite;
  filter: drop-shadow(0 8px 16px rgba(119, 67, 219, 0.3));
}

.error-code {
  font-size: 160px;
  font-weight: 900;
  color: #333;
  margin: 0;
  line-height: 1;
  background: linear-gradient(135deg, #7743DB 0%, #8057ff 50%, #9d6bff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 8px 16px rgba(119, 67, 219, 0.3);
  position: relative;
  display: inline-block;
}

.error-code::before {
  content: '404';
  position: absolute;
  top: 0;
  left: 0;
  background: linear-gradient(135deg, rgba(119, 67, 219, 0.1), rgba(128, 87, 255, 0.05));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  transform: translate(4px, 4px);
  z-index: -1;
}

/* Error Message Section */
.error-message-section {
  margin-bottom: 50px;
}

.error-title {
  font-size: 42px;
  font-weight: 700;
  color: #333;
  margin: 0 0 30px 0;
  line-height: 1.2;
  background: linear-gradient(135deg, #333 0%, #555 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.error-description {
  font-size: 20px;
  color: #666;
  line-height: 1.7;
  margin: 0;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
  font-weight: 400;
  letter-spacing: 0.3px;
}

/* Navigation Options */
.navigation-options {
  display: flex;
  gap: 30px; /* Increased gap between buttons */
  justify-content: center;
  margin-bottom: 70px; /* Increased bottom margin */
  flex-wrap: wrap;
  padding: 20px 0; /* Added vertical padding for better spacing */
}

.nav-button {
  display: inline-flex;
  align-items: center;
  gap: 15px; /* Increased gap between icon and text */
  padding: 24px 48px; /* Increased padding for taller buttons */
  border-radius: 18px; /* Slightly larger border radius */
  text-decoration: none;
  font-weight: 600;
  font-size: 18px; /* Increased font size */
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: 2px solid transparent;
  min-width: 220px; /* Increased minimum width */
  min-height: 65px; /* Added minimum height for consistent button height */
  justify-content: center;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1); /* Added subtle shadow */
}

.nav-button.primary {
  background: linear-gradient(135deg, #7743DB 0%, #8057ff 50%, #9d6bff 100%);
  color: white;
  box-shadow: 0 15px 35px rgba(119, 67, 219, 0.4); /* Enhanced shadow */
  border: 2px solid rgba(255, 255, 255, 0.2); /* Added subtle border */
}

.nav-button.primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s;
}

.nav-button.primary:hover::before {
  left: 100%;
}

.nav-button.primary:hover {
  transform: translateY(-4px) scale(1.03); /* Enhanced hover effect */
  box-shadow: 0 20px 50px rgba(119, 67, 219, 0.5); /* Stronger shadow on hover */
  background: linear-gradient(135deg, #6234c7 0%, #7046e0 50%, #8a5aeb 100%);
}

.nav-button.secondary {
  background: rgba(255, 255, 255, 0.95); /* Slightly more opaque */
  color: #7743DB;
  border: 2px solid #7743DB;
  backdrop-filter: blur(15px); /* Enhanced blur effect */
  box-shadow: 0 8px 25px rgba(119, 67, 219, 0.15); /* Added shadow */
}

.nav-button.secondary:hover {
  background: linear-gradient(135deg, #7743DB 0%, #8057ff 100%);
  color: white;
  transform: translateY(-4px) scale(1.03); /* Enhanced hover effect */
  box-shadow: 0 15px 40px rgba(119, 67, 219, 0.35); /* Stronger shadow */
}

/* Helpful Links */
.helpful-links {
  margin-bottom: 60px; /* Increased margin */
  padding: 50px 40px; /* Increased vertical padding */
  background: rgba(249, 245, 250, 0.6); /* Slightly more visible background */
  border-radius: 24px; /* Larger border radius */
  border: 1px solid rgba(119, 67, 219, 0.15); /* Slightly more visible border */
  box-shadow: 0 8px 25px rgba(119, 67, 219, 0.08); /* Added subtle shadow */
}

.helpful-links h3 {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0 0 30px 0;
  background: linear-gradient(135deg, #333 0%, #555 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.links-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.helpful-link {
  display: block;
  padding: 22px 28px; /* Increased padding for taller links */
  background: rgba(255, 255, 255, 0.85); /* Slightly more opaque */
  color: #7743DB;
  text-decoration: none;
  border-radius: 16px; /* Larger border radius */
  font-weight: 500;
  font-size: 16px; /* Added font size for consistency */
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(119, 67, 219, 0.25); /* Slightly more visible border */
  backdrop-filter: blur(12px); /* Enhanced blur */
  position: relative;
  overflow: hidden;
  min-height: 55px; /* Added minimum height */
  display: flex; /* Changed to flex for better alignment */
  align-items: center; /* Center content vertically */
  justify-content: center; /* Center content horizontally */
}

.helpful-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(119, 67, 219, 0.05), rgba(128, 87, 255, 0.02));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.helpful-link:hover::before {
  opacity: 1;
}

.helpful-link:hover {
  background: rgba(233, 227, 255, 0.9);
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 8px 25px rgba(119, 67, 219, 0.15);
  border-color: rgba(119, 67, 219, 0.3);
}

/* Search Suggestion */
.search-suggestion {
  border-top: 1px solid rgba(119, 67, 219, 0.1);
  padding-top: 40px;
  background: rgba(249, 245, 250, 0.3);
  border-radius: 20px;
  padding: 40px;
  margin-top: 20px;
}

.search-suggestion p {
  font-size: 18px;
  color: #666;
  margin: 0 0 25px 0;
  font-weight: 500;
}

.search-box {
  display: flex;
  max-width: 500px;
  margin: 0 auto;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
  border: 2px solid rgba(119, 67, 219, 0.2);
  transition: all 0.4s ease;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
}

.search-box:focus-within {
  border-color: #7743DB;
  box-shadow: 0 12px 35px rgba(119, 67, 219, 0.2);
  transform: translateY(-2px);
}

.search-input {
  flex: 1;
  padding: 18px 24px;
  border: none;
  outline: none;
  font-size: 17px;
  background: transparent;
  color: #333;
}

.search-input::placeholder {
  color: #999;
}

.search-button {
  padding: 18px 24px;
  background: linear-gradient(135deg, #7743DB 0%, #8057ff 100%);
  color: white;
  border: none;
  cursor: pointer;
  font-size: 17px;
  transition: all 0.4s ease;
  min-width: 70px;
  position: relative;
  overflow: hidden;
}

.search-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s;
}

.search-button:hover::before {
  left: 100%;
}

.search-button:hover {
  background: linear-gradient(135deg, #6234c7 0%, #7046e0 100%);
  transform: scale(1.05);
}

/* Enhanced Animations */
@keyframes pulse {
  0%, 100% {
    opacity: 0.8;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-10px) rotate(1deg);
  }
  66% {
    transform: translateY(5px) rotate(-1deg);
  }
}

/* Enhanced Responsive Design */
@media (max-width: 768px) {
  .not-found-container {
    padding: 30px 20px;
  }
  
  .not-found-content {
    padding: 60px 35px; /* Increased mobile padding */
    border-radius: 24px;
    max-width: 98%; /* Use even more width on mobile */
    width: 98%; /* Ensure it uses the full available width */
  }
  
  .error-code {
    font-size: 100px;
  }
  
  .error-icon {
    font-size: 70px;
  }
  
  .error-title {
    font-size: 28px;
  }
  
  .error-description {
    font-size: 18px;
  }
  
  .navigation-options {
    flex-direction: column;
    align-items: center;
    gap: 20px;
  }
  
  .nav-button {
    width: 100%;
    max-width: 350px; /* Increased max width for mobile */
    min-height: 60px; /* Maintain button height on mobile */
    padding: 20px 40px; /* Adjusted mobile padding */
  }
  
  .helpful-links {
    padding: 40px 25px; /* Increased mobile padding */
  }
  
  .links-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }
  
  .search-box {
    max-width: 100%;
  }
  
  .search-suggestion {
    padding: 30px 20px;
  }
}

@media (max-width: 480px) {
  .not-found-content {
    padding: 40px 25px;
    border-radius: 20px;
  }
  
  .error-code {
    font-size: 80px;
  }
  
  .error-icon {
    font-size: 60px;
  }
  
  .error-title {
    font-size: 24px;
  }
  
  .error-description {
    font-size: 16px;
  }
  
  .nav-button {
    padding: 16px 28px;
    font-size: 16px;
  }
  
  .helpful-links {
    padding: 25px 15px;
  }
  
  .search-input,
  .search-button {
    padding: 16px 20px;
    font-size: 16px;
  }
  
  .search-suggestion {
    padding: 25px 15px;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .not-found-content {
    border: 2px solid #333;
  }
  
  .nav-button.primary {
    background: #333;
  }
  
  .nav-button.secondary {
    border-color: #333;
    color: #333;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .error-icon {
    animation: none;
  }
  
  .nav-button,
  .helpful-link {
    transition: none;
  }
  
  .nav-button:hover,
  .helpful-link:hover {
    transform: none;
  }
}