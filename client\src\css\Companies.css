/* Companies.css */
.companies-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Hero Section */
.companies-hero {
  background: linear-gradient(135deg, #f9f2ff 0%, #ffefef 100%);
  padding: 80px 20px;
  text-align: center;
  border-radius: 20px;
  margin-bottom: 40px;
  position: relative;
  overflow: hidden;
    border: 1px solid #e2e8f0;
}

.companies-hero::before {
   content: "";
  position: absolute;
  top: -50px;
  right: -50px;
  width: 200px;
  height: 200px;
  background: rgba(134, 87, 243, 0.1);
  border-radius: 50%;
  z-index: 0;
}

.companies-hero::after {
  content: "";
  position: absolute;
  bottom: -100px;
  left: -100px;
  width: 300px;
  height: 300px;
  background: rgba(255, 107, 107, 0.1);
  border-radius: 50%;
  z-index: 0;
}

.hero-content {
  position: relative;
  z-index: 1;
  max-width: 800px;
  margin: 0 auto;
}

.hero-content h1 {
  font-size: 2.5rem;
  color: #000;
  font-weight: 700;
  margin-bottom: 15px;
}

.hero-content p {
  font-size: 1.1rem;
  color: #555;
  opacity: 0.9;
  margin-bottom: 30px;
}

.search-container {
  position: relative;
  max-width: 600px;
  margin: 0 auto;
}

.search-container input {
  width: 100%;
  padding: 15px 20px 15px 50px;
  border-radius: 50px;
  border: none;
  font-size: 1rem;
  background-color: #e6e9f7;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.search-icon {
  position: absolute;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--primary-color);
}

/* Filter Tabs */
.filter-tabs {
  display: flex;
  justify-content: center;
  margin-bottom: 40px;
  gap: 10px;
}

.filter-tabs button {
  padding: 12px 25px;
  border: none;
  background: #f5f7ff;
  color: #555;
  font-weight: 500;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.filter-tabs button:hover {
  background: #e6e9f7;
}

.filter-tabs button.active {
  background: var(--primary-color);
  color: white;
}

/* Companies Grid */
.companies-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 25px;
  margin-bottom: 60px;
}

.company-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
}

.company-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.company-card.featured {
  border: 2px solid var(--primary-color);
}

.featured-badge {
  position: absolute;
  top: 15px;
  right: 15px;
  background: var(--primary-color);
  color: white;
  padding: 5px 15px;
  border-radius: 50px;
  font-size: 0.8rem;
  font-weight: 600;
  z-index: 1;
}

.company-logo {
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.company-logo img {
  max-height: 80px;
  max-width: 80%;
  object-fit: contain;
  border-radius: 4px;
}

.company-logo span {
  font-size: 2.5rem;
  font-weight: 700;
  color: white;
}

.company-info {
  padding: 20px;
  text-align: center;
}

.company-info h3 {
  font-size: 1.2rem;
  margin-bottom: 8px;
  color: #333;
}

.industry {
  color: var(--primary-color);
  font-weight: 500;
  font-size: 0.9rem;
  margin-bottom: 15px;
}

.rating {
  margin-bottom: 15px;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 5px;
}

.rating .filled {
  color: #FFC107;
}

.rating span {
  margin-left: 5px;
  color: #666;
  font-size: 0.9rem;
}

.meta {
  display: flex;
  justify-content: center;
  font-size: 0.9rem;
  color: #666;
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #f0f0f0;
}

.meta p {
  display: flex;
  align-items: center;
  gap: 5px;
}

/* Loading State */
.loading {
  pointer-events: none;
}

.company-card-skeleton {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  height: 280px;
}

.skeleton-logo {
  height: 120px;
  background: #f5f5f5;
  animation: pulse 1.5s infinite;
}

.skeleton-info {
  padding: 20px;
}

.skeleton-line {
  height: 16px;
  background: #f5f5f5;
  border-radius: 4px;
  margin-bottom: 12px;
  animation: pulse 1.5s infinite;
  animation-delay: 0.2s;
}

.skeleton-line.short {
  width: 60%;
  margin: 0 auto 15px;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Error State */
.error-message {
    color: rgb(231, 76, 60);
    background-color: rgb(253, 242, 242);
    border: 1px solid rgb(245, 198, 203);
    border-radius: 8px;
    padding: 12px 16px;
    margin: 0px ;
    text-align: center;
    font-size: 14px;
    max-width: 400px;
    box-shadow: rgba(231, 76, 60, 0.1) 0px 2px 4px;

}

.error-message button {
  margin-top: 20px;
  padding: 10px 25px;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.error-message button:hover {
  background: #5649c4;
}

/* No Results */
.no-results {
  text-align: center;
  padding: 60px 20px;
  grid-column: 1 / -1;
  color: #666;
}

.no-results h3 {
  margin: 20px 0 10px;
  color: #333;
}

/* Loading Overlay */
.loading-overlay {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;

  border-radius: 12px;
  margin: 20px 0;
  min-height: 200px;
}

.loading-overlay .spinner {
  font-size: 2rem;
  color: #8657f3;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

.loading-overlay p {
  color: #666;
  font-size: 1.1rem;
  margin: 0;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1200px) {
  .companies-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 768px) {
  .companies-hero {
    padding: 60px 20px;
  }
  
  .hero-content h1 {
    font-size: 2rem;
  }
  
  .filter-tabs {
    flex-wrap: wrap;
  }
  
  .companies-grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  }
}

@media (max-width: 480px) {
  .companies-hero {
    padding: 50px 15px;
  border-radius: 20px;
  }
  
  .hero-content h1 {
    font-size: 1.8rem;
  }
  
  .search-container input {
    padding: 12px 15px 12px 45px;
  }
  
  .company-card {
    max-width: 100%;
  }
}