import React from "react";
import NewsLetterBG from "../assets/Newsletter.png"; 
import "../css/NewsLetter.css"; 
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPaperPlane } from '@fortawesome/free-solid-svg-icons';

const NewsLetter = () => {
  // const sliderRef = useRef(null);

  // const scrollLeft = () => {
  //   if (sliderRef.current) {
  //     sliderRef.current.scrollBy({ left: -200, behavior: "smooth" });
  //   }
  // };

  // const scrollRight = () => {
  //   if (sliderRef.current) {
  //     sliderRef.current.scrollBy({ left: 200, behavior: "smooth" });
  //   }
  // };

  return (
    <div className="newsletter-container">
      {/* Image with overlay content */}
      <div className="newsletter-content-wrapper">
        <img src={NewsLetterBG} alt="Newsletter" className="newsletter-image" />
        
        {/* Overlay Text */}
        <div className="newsletter-overlay">
          <div className="newsletter-text">
            <h1>Sign up to get <br /> the latest jobs</h1>
          </div>
          
          {/* Email Input Container */}
          <div className="email-container">
            <input
              type="email"
              placeholder="<EMAIL>"
              className="email-input"
            />
            <button className="subscribe-button">
              Subscribe <FontAwesomeIcon icon={faPaperPlane} />
            </button>
          </div>
        </div>
      </div>

      {/* Image Slider */}
      {/* <div className="image-slider-container">
        <button className="slider-button" onClick={scrollLeft}>‹</button>
        <div className="image-slider" ref={sliderRef}>
          <div className="slider-box"><img src=" image1.jpg" alt="Slide 1"/></div>
          <div className="slider-box"><img src="image2.jpg" alt="Slide 2" /></div>
          <div className="slider-box"><img src="image3.jpg" alt="Slide 3" /></div>
          <div className="slider-box"><img src="image4.jpg" alt="Slide 4" /></div>
          <div className="slider-box"><img src="image5.jpg" alt="Slide 5" /></div>
          <div className="slider-box"><img src="image6.jpg" alt="Slide 6" /></div>
          <div className="slider-box"><img src="image7.jpg" alt="Slide 7" /></div>
        </div>
        <button className="slider-button" onClick={scrollRight}>›</button>
      </div> */}

    </div>
  );
};

export default NewsLetter;
