:root {
  /* color palette */

  --primary-color: #9777FA; 
  --secondary-color: #FF2D55;

  --primary-color-h: #9877fab7; 
  --secondary-color-h: #ff2d53ca;
  
  /* RGB values for rgba usage */
  --primary-color-rgb: 151, 119, 250;
  --secondary-color-rgb: 255, 45, 85;

  --background-color: #ffff;
  
  --text-color-b: #000; 
  --text-color-w: #fff; 

  --padding-size: 15px 210px;
}

body {
  background-color: var(--background-color);
  margin: 0;
  font-family: Arial, sans-serif;
  color: var(--text-color-b);
  overflow-x: hidden;
}

/* Content spacing for layout */
.content-spacing {
  height: 3rem;
}

/* App container styles */
.App {
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
  box-sizing: border-box;
}

/* Main content wrapper - no default padding, components handle their own spacing */

.page-content {
  width: 100%;
  max-width: 100%;
  padding: 0;
  margin: 0 auto;
  box-sizing: border-box;
}

/* Prevent fixed navbar from affecting layout */
.MainLayout {
  position: relative;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  width: 100%;
  box-sizing: border-box;
}

/* Responsive padding adjustments */
@media (max-width: 1400px) {
  :root {
    --padding-size: 15px 80px;
    --padding-horizontal: 80px;
  }
}

@media (max-width: 768px) {
  :root {
    --padding-size: 15px 30px;
    --padding-horizontal: 30px;
  }
}

@media (max-width: 480px) {
  :root {
    --padding-size: 15px 15px;
    --padding-horizontal: 15px;
  }
}
