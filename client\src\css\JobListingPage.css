/* JobListingPage.css */

.job-listing-page {
    background-color: #f9f5fa;
    min-height: 100vh;
    padding: 20px 0;
    font-family: 'Inter', sans-serif;
    width: 100%;
    box-sizing: border-box;
  }
  
  .job-listing-page .container {
    width: 100%;
    margin: 0 auto;
    padding: 0 20px;
    box-sizing: border-box;
  }
  
  /* Header */
  .job-listing-page .header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    width: 100%;
  }
  
  .job-listing-page .header h1 {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin-right: 15px;
  }
  
  .job-listing-page .header-line {
    flex-grow: 1;
    height: 2px;
    background-color: #e0e6ff;
  }
  
  /* Tab Navigation */
  .job-listing-page .tab-navigation {
    display: flex;
    margin-bottom: 16px;
    border-bottom: 1px solid #eaedf2;
    padding-bottom: 10px;
  }
  
  .job-listing-page .tab-navigation button {
    background: none;
    border: none;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 500;
    color: #6b7280;
    cursor: pointer;
    margin-right: 24px;
  }
  
  .job-listing-page .tab-navigation button.tab-active {
    color: #8057ff;
    border-bottom: 2px solid #8057ff;
    padding-bottom: 6px;
  }
  
  .job-listing-page .jobs-count {
    font-size: 14px;
    color: #6b7280;
    margin-bottom: 24px;
  }
  
  /* Layout */
  .job-listing-page .content-area {
    display: flex;
    gap: 20px;
    width: 100%;
    box-sizing: border-box;
  }
  
  .job-listing-page .sidebar-left {
    width: 26%;
  }
  
  .job-listing-page .main-content {
    width: 48%;
  }
  
  .job-listing-page .sidebar-right {
    width: 26%;
  }
  
  /* Featured Job Cards */
  .job-listing-page .featured-job-card {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    position: relative;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
    transition: transform 0.2s, box-shadow 0.2s;
  }
  
  .job-listing-page .featured-job-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  }
  
  .job-listing-page .featured-job-content {
    padding: 16px;
  }
  
  .job-listing-page .urgent-tag {
    position: absolute;
    top: 10px;
    left: 10px;
    background: #ff6b00;
    color: white;
    padding: 8px 10px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    z-index: 10;
    text-align: center;
  }
  
  .job-listing-page .featured-profile-image {
    margin-bottom: 16px;
    height: 140px;
    overflow: hidden;
    background-color: #f5f5f5;
  }
  
  .job-listing-page .profile-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }
  
  .job-listing-page .featured-job-card:hover .profile-image {
    transform: scale(1.05);
  }
  
  .job-listing-page .company-info {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
  }
  
  .job-listing-page .company-badge {
    width: 30px;
    height: 30px;
    background-color: #8057ff;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    font-weight: 600;
    margin-right: 8px;
    flex-shrink: 0;
  }
  
  .job-listing-page .company-name {
    font-size: 14px;
    color: #666;
    font-weight: 500;
    margin-right: 4px;
  }
  
  .job-listing-page .job-type-tag {
    color: #666;
    font-size: 14px;
  }
  
  .job-listing-page .featured-job-title {
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 12px;
    color: #333;
    line-height: 1.3;
    max-height: 50px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
  
  .job-listing-page .job-meta {
    display: flex;
    font-size: 13px;
    color: #777;
    margin-bottom: 14px;
    flex-wrap: wrap;
    gap: 16px;
  }
  
  .job-listing-page .meta-item {
    display: flex;
    align-items: center;
    font-size: 13px;
    color: #777;
  }
  
  .job-listing-page .meta-item svg {
    margin-right: 6px;
    color: #8057ff;
    width: 15px;
    height: 15px;
  }
  
  .job-listing-page .job-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .job-listing-page .job-salary {
    font-weight: bold;
    color: #8057ff;
    font-size: 20px;
  }
  
  .job-listing-page .salary-period {
    font-weight: 400;
    font-size: 12px;
    color: #777;
  }
  
  /* Main job cards */
  .job-listing-page .job-card {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    position: relative;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
    transition: transform 0.2s, box-shadow 0.2s;
  }
  
  .job-listing-page .job-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  }
  
  .job-listing-page .job-card-wrapper {
    display: flex;
    padding: 16px;
  }
  
  .job-listing-page .job-card-left {
    margin-right: 16px;
  }
  
  .job-listing-page .job-thumbnail {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    object-fit: cover;
  }
  
  .job-listing-page .job-card-content {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
  }
  
  .job-listing-page .job-header {
    margin-bottom: 12px;
  }
  
  .job-listing-page .job-title-container {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
  }
  
  .job-listing-page .job-title {
    font-size: 18px;
    font-weight: 700;
    color: #333;
    margin: 0;
    line-height: 1.3;
    max-height: 50px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
  
  .job-listing-page .job-meta-info {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    margin-bottom: 12px;
    font-size: 13px;
    color: #777;
  }
  
  .job-listing-page .job-location, 
  .job-listing-page .job-type, 
  .job-listing-page .job-time {
    display: flex;
    align-items: center;
  }
  
  .job-listing-page .type-dot {
    margin: 0 4px;
  }
  
  .job-listing-page .meta-icon {
    margin-right: 6px;
    color: #8057ff;
  }
  
  .job-listing-page .job-description {
    font-size: 14px;
    color: #666;
    line-height: 1.5;
    margin-bottom: 16px;
  }
  
  .job-listing-page .job-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
  }
  
  .job-listing-page .job-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .job-listing-page .tag {
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: 500;
  }
  
  .job-listing-page .tag.urgent {
    background-color: #ffedd5;
    color: #c2410c;
  }
  
  .job-listing-page .tag.senior {
    background-color: #dbeafe;
    color: #1e40af;
  }
  
  .job-listing-page .tag.full-time {
    color: #666;
  }
  
  .job-listing-page .action-button-small {
    background: none;
    border: 1px solid #e0e0e0;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: #8057ff;
    margin-left: 8px;
  }
  
  .job-listing-page .action-button-small:hover {
    background-color: #f5f5f5;
  }
  
  /* Pagination */
  .job-listing-page .pagination {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin: 32px 0;
  }
  
  .job-listing-page .pagination-arrow {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background: white;
    cursor: pointer;
    color: #6b7280;
  }
  
  .job-listing-page .pagination-number {
    min-width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background: white;
    cursor: pointer;
    color: #6b7280;
    font-size: 14px;
  }
  
  .job-listing-page .pagination-number.active {
    background-color: #8057ff;
    color: white;
    border-color: #8057ff;
  }
  
  .job-listing-page .view-more {
    text-align: center;
    margin: 20px 0;
  }
  
  .job-listing-page .view-more-btn {
    background-color: transparent;
    border: none;
    color: #8057ff;
    font-weight: 600;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 4px;
    margin: 0 auto;
    cursor: pointer;
  }
  
  /* Categories */
  .job-listing-page .categories-box {
    background-color: white;
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  }
  
  .job-listing-page .categories-box h3 {
    font-size: 18px;
    font-weight: 700;
    color: #333;
    margin-bottom: 16px;
  }
  
  .job-listing-page .categories-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
  }
  
  .job-listing-page .category-card {
    background-color: #f9f5fa;
    padding: 12px;
    border-radius: 8px;
    position: relative;
    display: flex;
    flex-direction: column;
    cursor: pointer;
  }
  
  .job-listing-page .category-card::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background-color: #8057ff;
    border-top-left-radius: 8px;
    border-bottom-left-radius: 8px;
  }
  
  .job-listing-page .category-name {
    font-size: 14px;
    font-weight: 600;
    color: #333;
  }
  
  .job-listing-page .category-count {
    font-size: 12px;
    color: #6b7280;
  }
  
  /* Responsive */
  @media (max-width: 1024px) {
    .job-listing-page .content-area {
      flex-direction: column;
      width: 100%;
    }
    
    .job-listing-page .sidebar-left, 
    .job-listing-page .main-content,
    .job-listing-page .sidebar-right {
      width: 100%;
    }
  }
  
  @media (max-width: 768px) {
    .job-listing-page .tab-navigation {
      overflow-x: auto;
      white-space: nowrap;
    }
    
    .job-listing-page .tab-navigation button {
      margin-right: 16px;
    }
    
    .job-listing-page .job-card-wrapper {
      flex-direction: column;
    }
    
    .job-listing-page .job-card-left {
      margin-right: 0;
      margin-bottom: 12px;
    }
    
    .job-listing-page .categories-grid {
      grid-template-columns: 1fr;
    }
  }
  
  @media (max-width: 576px) {
    .job-listing-page .featured-job-title,
    .job-listing-page .job-title {
      font-size: 16px;
      max-height: 42px;
    }
    
    .job-listing-page .job-meta,
    .job-listing-page .job-meta-info {
      font-size: 12px;
    }
    
    .job-listing-page .job-description {
      font-size: 13px;
    }
    
    .job-listing-page .featured-profile-image {
      height: 120px;
    }
  }
  
  /* Recruiting Box */
  .job-listing-page .recruiting-box {
    background-color: #f0f7ff;
    border-radius: 16px;
    padding: 16px;
    margin-bottom: 20px;
  }
  
  .job-listing-page .recruiting-box h3 {
    font-size: 18px;
    font-weight: 700;
    color: #333;
    margin-bottom: 8px;
  }
  
  .job-listing-page .recruiting-box p {
    font-size: 14px;
    color: #666;
    margin-bottom: 16px;
    line-height: 1.5;
  }
  
  .job-listing-page .post-job-btn {
    background-color: #8057ff;
    color: white;
    border: none;
    padding: 10px 16px;
    border-radius: 8px;
    font-weight: 600;
    font-size: 14px;
    cursor: pointer;
    width: 100%;
    margin-bottom: 12px;
    }
  
  .job-listing-page .recruiting-image {
    text-align: center;
  }