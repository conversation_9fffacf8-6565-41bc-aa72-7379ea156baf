const db = require('../config/db');

const AdminUser = {
  // Get all admin users
  getAll: (callback) => {
    const query = "SELECT admin_id, admin_username, admin_email, role, created_at FROM admin_users ORDER BY created_at DESC";
    db.query(query, (err, results) => {
      if (err) {
        console.error("Database query failed:", err);
        return callback(err, null);
      }
      callback(null, results);
    });
  },

  // Get admin user by ID
  getById: (adminId, callback) => {
    const query = "SELECT admin_id, admin_username, admin_email, role, created_at FROM admin_users WHERE admin_id = ?";
    db.query(query, [adminId], (err, results) => {
      if (err) {
        return callback(err, null);
      }
      callback(null, results[0]);
    });
  },

  // Get admin user by username (for authentication)
  getByUsername: (username, callback) => {
    const query = "SELECT * FROM admin_users WHERE admin_username = ?";
    db.query(query, [username], (err, results) => {
      if (err) {
        return callback(err, null);
      }
      callback(null, results[0]);
    });
  },

  // Get admin user by username or email (for authentication)
  getByUsernameOrEmail: (identifier, callback) => {
    const query = "SELECT * FROM admin_users WHERE admin_username = ? OR admin_email = ?";
    db.query(query, [identifier, identifier], (err, results) => {
      if (err) {
        return callback(err, null);
      }
      callback(null, results[0]);
    });
  },

  // Create a new admin user
  create: (userData, callback) => {
    const query = "INSERT INTO admin_users (admin_username, admin_password, admin_email, role) VALUES (?, ?, ?, ?)";
    db.query(
      query, 
      [userData.admin_username, userData.admin_password, userData.admin_email, userData.role || 'User'],
      (err, result) => {
        if (err) {
          return callback(err, null);
        }
        callback(null, { admin_id: result.insertId, ...userData });
      }
    );
  },

  // Update an admin user
  update: (adminId, userData, callback) => {
    // Start building the query and parameters
    let query = "UPDATE admin_users SET ";
    const queryParams = [];
    const updateFields = [];

    // Add fields that are present in userData
    if (userData.admin_username) {
      updateFields.push("admin_username = ?");
      queryParams.push(userData.admin_username);
    }
    
    if (userData.admin_email) {
      updateFields.push("admin_email = ?");
      queryParams.push(userData.admin_email);
    }
    
    if (userData.role) {
      updateFields.push("role = ?");
      queryParams.push(userData.role);
    }
    
    // Only update password if provided
    if (userData.admin_password) {
      updateFields.push("admin_password = ?");
      queryParams.push(userData.admin_password);
    }

    // If no fields to update, return early
    if (updateFields.length === 0) {
      return callback(new Error("No fields to update"), null);
    }

    // Complete the query
    query += updateFields.join(", ") + " WHERE admin_id = ?";
    queryParams.push(adminId);

    // Execute the query
    db.query(query, queryParams, (err, result) => {
      if (err) {
        return callback(err, null);
      }
      callback(null, { admin_id: adminId, ...userData });
    });
  },

  // Delete an admin user
  delete: (adminId, callback) => {
    const query = "DELETE FROM admin_users WHERE admin_id = ?";
    db.query(query, [adminId], (err, result) => {
      if (err) {
        return callback(err, null);
      }
      callback(null, { admin_id: adminId, deleted: result.affectedRows > 0 });
    });
  },

  // Check if a user has a specific role
  hasRole: (adminId, requiredRole, callback) => {
    const query = "SELECT role FROM admin_users WHERE admin_id = ?";
    db.query(query, [adminId], (err, results) => {
      if (err) {
        return callback(err, false);
      }
      
      if (results.length === 0) {
        return callback(null, false);
      }
      
      const userRole = results[0].role;
      
      // Role hierarchy: Superadmin > Admin > Editor > User
      const roleHierarchy = {
        'Superadmin': 4,
        'Admin': 3,
        'Editor': 2,
        'User': 1
      };
      
      // Check if user's role is sufficient
      const hasAccess = roleHierarchy[userRole] >= roleHierarchy[requiredRole];
      callback(null, hasAccess);
    });
  }
};

module.exports = AdminUser;