.about-wrapper {
    background: #fffaf7;
    padding: 6rem calc(6rem + 210px) 4rem calc(6rem + 210px); /* Add main-content-padded equivalent */
    font-family: "Segoe UI", sans-serif;
    box-sizing: border-box;
  }
  
  .about-grid {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 0.4rem; /* ⬅ Tighter space between text and images */
  }
  
  
  /* Left Text */
  .about-text {
    flex: 1;
  }
  
  .about-text h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1e1e1e;
    line-height: 1.4;
    margin-bottom: 1.5rem;
  }
  
  .about-text p {
    font-size: 1rem;
    color: #555;
    line-height: 1.7;
    margin-bottom: 2.5rem;
  }
  
  .about-buttons {
    display: flex;
    gap: 1.5rem;
  }
  
  .btn-primary {
    background: linear-gradient(to right, #8360c3, #2ebf91);
    color: white;
    padding: 0.5rem 1.2rem;
    border: none;
    border-radius: 10px;
    font-weight: 600;
    cursor: pointer;
    box-shadow: 0 6px 12px rgba(131, 96, 195, 0.3);
    transition: 0.3s ease;
  }
  
  .btn-primary:hover {
    opacity: 0.9;
  }
  
  .btn-text {
    background: none;
    border: none;
    font-weight: 500;
    text-decoration: underline;
    color: #333;
    cursor: pointer;
  }
  
  /* Image layout */
  .about-images {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 0.3rem; /* ⬅ Closer between stacked images and main image */
  }

  /* Mobile responsive for about images */
  @media (max-width: 768px) {
    .about-images {
      justify-content: center;
      margin-top: 1rem;
      gap: 0.5rem;
    }

    .main-img {
      width: 280px;
      height: 240px;
      border-radius: 20px;
    }

    .img-small-circle {
      width: 50px;
      height: 50px;
    }

    .img-medium-square {
      width: 70px;
      height: 70px;
      border-radius: 12px;
    }

    .img-large-square {
      width: 90px;
      height: 90px;
      border-radius: 14px;
    }
  }

  @media (max-width: 480px) {
    .about-images {
      flex-direction: column;
      align-items: center;
      gap: 1rem;
    }

    .image-stack {
      flex-direction: row;
      gap: 0.8rem;
      margin-top: 0;
    }

    .main-img {
      width: 100%;
      max-width: 300px;
      height: 200px;
      border-radius: 16px;
      order: -1; /* Show main image first on mobile */
    }

    .img-small-circle {
      width: 45px;
      height: 45px;
    }

    .img-medium-square {
      width: 60px;
      height: 60px;
      border-radius: 10px;
    }

    .img-large-square {
      width: 75px;
      height: 75px;
      border-radius: 12px;
    }
  }
  
  .image-stack {
    display: flex;
    flex-direction: column;
    align-items: flex-end; /* ⬅ align images to the right */
    gap: 0.5rem;
    margin-top: 10px;
  }
  
  
  /* Top: Small Circle */
  .img-small-circle {
    width: 65px;
    height: 65px;
    border-radius: 50%;
    object-fit: cover;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  }
  
  /* Middle: Medium Square */
  .img-medium-square {
    width: 90px;
    height: 90px;
    border-radius: 14px;
    object-fit: cover;
    box-shadow: 0 5px 12px rgba(0, 0, 0, 0.1);
  }
  
  /* Bottom: Large Square */
  .img-large-square {
    width: 120px;
    height: 120px;
    border-radius: 16px;
    object-fit: cover;
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
  }
  
  /* Main Image */
  .main-img {
    width: 340px;
    height: 300px;
    object-fit: cover;
    border-radius: 24px;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
    z-index: 1;
  }
  
  /* File: src/css/Services.css */
  
  .services-wrapper {
    margin-top: 4rem;
    padding: 3rem 0;
    background-color: #f9fbfc;
  }
  
  .services-grid {
    display: flex;
    justify-content: center;
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    flex-wrap: nowrap; /* Prevent wrapping of service cards */
  }
  
  .service-card {
    background: #fff;
    padding: 2rem;
    flex: 1 1 250px; /* Minimum width of each card */
    max-width: 250px;
    border-radius: 12px;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
    text-align: center;
    transition: transform 0.3s ease;
  }
  
  .service-card:hover {
    transform: translateY(-5px);
  }
  
  .service-card img {
    width: 60px;
    height: 60px;
    margin-bottom: 1rem;
  }
  
  .service-card h3 {
    font-size: 1.1rem;
    margin-bottom: 0.6rem;
    color: #222;
  }
  
  .service-card p {
    font-size: 0.9rem;
    color: #555;
    margin-bottom: 1rem;
  }
  
  .read-more {
    color: #8360c3;
    font-weight: 500;
    text-decoration: none;
    font-size: 0.9rem;
  }
  
  /* Responsive adjustments */
  @media (max-width: 1024px) {
    .services-grid {
      flex-wrap: wrap;
      gap: 1.5rem;
    }

    .service-card {
      flex: 1 1 45%; /* Allow for two cards per row */
      max-width: 300px;
    }
  }

  @media (max-width: 768px) {
    .services-wrapper {
      padding: 2rem 1rem;
    }

    .services-grid {
      flex-direction: column;
      align-items: center;
      gap: 1.5rem;
      padding: 0 1rem;
    }

    .service-card {
      flex: none;
      width: 100%;
      max-width: 350px;
      padding: 1.5rem;
    }
  }

  @media (max-width: 480px) {
    .services-wrapper {
      margin-top: 2rem;
      padding: 1.5rem 0.5rem;
    }

    .service-card {
      max-width: 100%;
      padding: 1.2rem;
    }

    .service-card h3 {
      font-size: 1rem;
    }

    .service-card p {
      font-size: 0.85rem;
    }
  }

  .dream-job-section {
    padding: 4rem 2rem;
    background-color: #f4f9fd;
    display: flex;
    justify-content: center;
  }
  
  .dream-job-card {
    display: flex;
    background-color: #ffffff;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.05);
    max-width: 960px;
    width: 100%;
  }
  
  .job-image img {
    width: 100%;
    height: 100%;
    max-width: 400px;
    object-fit: cover;
    display: block;
  }
  
  .job-content {
    padding: 2rem;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  
  .find-jobs-link {
    color: #4a90e2;
    font-weight: 600;
    margin-bottom: 0.5rem;
    display: inline-block;
    text-decoration: none;
  }
  
  .job-content h2 {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #2d2d2d;
  }
  
  .job-content p {
    color: #666;
    line-height: 1.6;
    font-size: 1rem;
  }
  
  /* Responsive */
  @media (max-width: 768px) {
    .dream-job-card {
      flex-direction: column;
    }
  
    .job-image img {
      max-width: 100%;
    }
  }
  
  .job-cta-section {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin: 6rem auto;
    max-width: 1100px;
    padding: 2rem;
  }
  
  .job-image-box {
    position: relative;
    z-index: 2;
  }
  
  .cta-image {
    width: 60%;  /* Image takes 60% of the width */
    height: auto;
    object-fit: cover;
    border-top-right-radius: 30px;
    border-bottom-right-radius: 30px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);
  }
  
  .job-text-box {
    background-color: #e5fbef;
    padding: 3rem 2.5rem;
    border-radius: 24px;
    margin-left: -40%;  /* This causes the overlap */
    margin-top: -60px;  /* Slight overlap on the top */
    z-index: 1;
    position: relative;
    max-width: 600px;  /* Expand the rectangle size to ensure text fits */
    min-height: 320px;  /* Increase the height to avoid text clipping */
    display: flex;
    flex-direction: column;
    justify-content: center;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.08);
    padding-left: 20%;
  
  }
  
  .job-link {
    color: #3d89f9;
    font-weight: 600;
    text-decoration: none;
    font-size: 0.95rem;
    display: inline-block;
    margin-bottom: 0.6rem;
  }
  
  .job-text-box h2 {
    font-size: 1.7rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #222;
  }
  
  .job-text-box p {
    font-size: 1rem;
    color: #555;
    line-height: 1.6;
  }
  
  /* Responsive for mobile */
  @media (max-width: 768px) {
    .job-cta-section {
      flex-direction: column;
      align-items: center;
      margin: 3rem auto;
      padding: 1rem;
    }

    .job-image-box {
      width: 100%; /* Image takes full width on mobile */
    }

    .cta-image {
      width: 100%;
      border-radius: 20px;
    }

    .job-text-box {
      width: 100%;  /* Full width for text on mobile */
      margin-left: 0;
      margin-top: 20px;  /* Slight margin top to prevent overlap */
      padding: 2rem 1.5rem;
      z-index: 2;
      min-height: auto;
      padding-left: 1.5rem;
    }

    .job-text-box h2 {
      font-size: 1.5rem;
    }

    .job-text-box p {
      font-size: 0.95rem;
    }
  }

  @media (max-width: 480px) {
    .job-cta-section {
      margin: 2rem auto;
      padding: 0.5rem;
    }

    .job-text-box {
      padding: 1.5rem 1rem;
      border-radius: 20px;
    }

    .job-text-box h2 {
      font-size: 1.3rem;
      margin-bottom: 0.8rem;
    }

    .job-text-box p {
      font-size: 0.9rem;
      line-height: 1.5;
    }

    .job-link {
      font-size: 0.9rem;
    }
  }
  
  .quality-section {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #fff;
    border-radius: 28px;
    box-shadow: 0 8px 32px rgba(80, 80, 80, 0.07);
    padding: 9.5rem 9rem;    /* More padding for airiness */
    margin: 3rem 0;
    gap: 2.5rem;
  }
  
  .quality-content {
    flex: 1;
    margin-right: 2.5rem;
  }
  
  .quality-subtitle {
    color: #3d89f9;
    font-weight: 600;
    font-size: 1rem;
    margin-bottom: 0.6rem;
    display: inline-block;
  }
  
  .quality-content h2 {
    font-size: 2.2rem;
    font-weight: 700;
    color: #1e1e1e;
    margin-bottom: 1.3rem;
    line-height: 1.3;
  }
  
  .quality-content p {
    color: #555;
    font-size: 1rem;
    margin-bottom: 1.2rem;
    line-height: 1.7;
  }
  
  .learn-more-btn {
    background: #8360c3;
    color: #fff;
    border: none;
    border-radius: 8px;
    padding: 0.7rem 1.5rem;
    font-weight: 600;
    cursor: pointer;
    margin-top: 1rem;
    transition: background 0.2s;
  }
  
  .learn-more-btn:hover {
    background: #2ebf91;
  }
  
  .quality-image-box {
    position: relative;
    width: 380px;
    min-width: 260px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .quality-img {
    width: 340px;
    height: 260px;
    object-fit: cover;
    border-top-left-radius: 48px;
    border-bottom-right-radius: 48px;
    border-top-right-radius: 0;
    border-bottom-left-radius: 0;
    box-shadow: 0 8px 32px rgba(80, 80, 80, 0.11);
  }
  
  .quality-badge {
    position: absolute;
    top: 24px;
    left: 100%;
    transform: translateX(-50%); /* Half overlaps the image */
    background: #fff;
    color: #21b573;
    font-weight: 600;
    padding: 0.5rem 1.2rem;
    border-radius: 14px;
    font-size: 1rem;
    box-shadow: 0 2px 8px rgba(80, 80, 80, 0.08);
    display: flex;
    align-items: center;
    min-width: 170px;
    z-index: 2;
  }
  .quality-card {
    position: absolute;
    bottom: 22px;
    left: 30px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(80, 80, 80, 0.09);
    padding: 0.7rem 1.2rem;
    display: flex;
    flex-direction: column;
    gap: 0.3rem;
  }
  
  .quality-card-role {
    color: #8360c3;
    font-size: 0.95rem;
    font-weight: 600;
  }
  
  /* Responsive */
  @media (max-width: 900px) {
    .quality-section {
      flex-direction: column;
      padding: 3rem 2rem;
      gap: 2rem;
    }
    .quality-content {
      margin-right: 0;
      margin-bottom: 1rem;
      text-align: center;
    }
    .quality-content h2 {
      font-size: 1.8rem;
    }
    .quality-image-box {
      width: 100%;
      min-width: 0;
    }
    .quality-img {
      width: 100%;
      max-width: 340px;
      height: 220px;
    }
  }

  @media (max-width: 768px) {
    .quality-section {
      padding: 2rem 1rem;
      margin: 2rem 0;
    }

    .quality-content h2 {
      font-size: 1.6rem;
      margin-bottom: 1rem;
    }

    .quality-content p {
      font-size: 0.95rem;
      margin-bottom: 1rem;
    }

    .learn-more-btn {
      padding: 0.8rem 1.8rem;
      font-size: 0.95rem;
    }

    .quality-img {
      height: 200px;
      border-radius: 24px;
    }

    .quality-badge {
      position: static;
      transform: none;
      margin: 1rem auto 0 auto;
      max-width: fit-content;
    }

    .quality-card {
      position: static;
      margin: 1rem auto 0 auto;
      max-width: fit-content;
    }
  }

  @media (max-width: 480px) {
    .quality-section {
      padding: 1.5rem 0.5rem;
      border-radius: 20px;
    }

    .quality-content h2 {
      font-size: 1.4rem;
      line-height: 1.3;
    }

    .quality-content p {
      font-size: 0.9rem;
      line-height: 1.5;
    }

    .quality-img {
      height: 180px;
      border-radius: 20px;
    }

    .quality-badge {
      font-size: 0.9rem;
      padding: 0.4rem 1rem;
      min-width: 150px;
    }
  }
  
  /* TeamSection.css */
  .team-section {
    max-width: 1200px;
    margin: 0 auto 2.5rem auto;
    padding: 1.2rem 0 2.5rem 0;  /* Remove left/right padding */
    text-align: center;
    font-family: 'Montserrat', 'Segoe UI', Arial, sans-serif;
  }
  
  .team-title {
    font-size: 2.4rem;
    font-weight: 800;
    margin-bottom: 0.3rem;
    letter-spacing: -1px;
  }
  
  .team-desc {
    color: #555;
    font-size: 1.08rem;
    margin-bottom: 2.3rem;
    font-weight: 400;
    max-width: 540px;
    margin-left: auto;
    margin-right: auto;
  }
  /*  gap: 2.3rem 1.5rem;
  */
  .team-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem 1rem; /* Very tight gap horizontally and vertically */
    justify-items: center;
  }
  
  
  .team-card {
    background: #fff;
    border-radius: 18px;
    box-shadow: 0 4px 16px rgba(80,80,80,0.09);
    padding: 0 0 1.9rem 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 200px;
  }
  
  .team-img-rect {
    width: 100%;
    height: 185px;
    overflow: hidden;
    background: #f0f0f0;
    border-radius: 18px 18px 0 0;
    display: block;
  }
  
  .team-img-rect img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
    border-radius: 18px 18px 0 0;
  }
  .team-name {
    font-weight: 600;
    font-size: 1.08rem;
    margin-top: 1rem;
    margin-bottom: 0.6rem;
    color: #222;
    font-family: 'Montserrat', 'Segoe UI', Arial, sans-serif;
  }
  .team-position {
    color: #888;
    font-size: 0.95rem;
    margin-bottom: 0.7rem;
    font-weight: 500;
    font-family: 'Montserrat', 'Segoe UI', Arial, sans-serif;
  }
  
  .team-social {
    display: flex;
    gap: 0.3rem; /* Even tighter spacing */
    justify-content: center;
    margin-top: 0.1rem;
  }
  
  .team-social-link svg {
    width: 18px !important;
    height: 18px !important;
    display: block;
    color: #959595;
    vertical-align: middle;
  }
  .team-social-link img {
    width: 22px;
    height: 22px;
    display: block;
    opacity: 0.7;
    transition: opacity 0.2s;
  }
  
  .team-social-link:hover img {
    opacity: 1;
  }
  
  /* Responsive */
  @media (max-width: 1100px) {
    .team-section {
      padding: 1rem 1rem 2rem 1rem;
    }

    .team-grid {
      grid-template-columns: repeat(2, 1fr);
      gap: 2rem 1.2rem;
    }
    .team-card {
      width: 100%;
      max-width: 240px;
    }
    .team-img-rect {
      height: 180px;
    }
  }

  @media (max-width: 768px) {
    .team-section {
      padding: 1rem 0.5rem 2rem 0.5rem;
    }

    .team-title {
      font-size: 2rem;
      margin-bottom: 0.5rem;
    }

    .team-desc {
      font-size: 1rem;
      margin-bottom: 2rem;
      padding: 0 1rem;
    }

    .team-grid {
      gap: 1.5rem 1rem;
    }
  }

  @media (max-width: 700px) {
    .team-grid {
      grid-template-columns: 1fr;
      gap: 1.3rem 0;
    }
    .team-card {
      width: 100%;
      max-width: 280px;
      margin: 0 auto;
    }
    .team-img-rect {
      height: 160px;
    }
  }

  @media (max-width: 480px) {
    .team-section {
      padding: 1rem 0.5rem 1.5rem 0.5rem;
    }

    .team-title {
      font-size: 1.8rem;
      line-height: 1.2;
    }

    .team-desc {
      font-size: 0.95rem;
      padding: 0 0.5rem;
    }

    .team-card {
      max-width: 100%;
      width: 100%;
    }

    .team-img-rect {
      height: 140px;
    }

    .team-name {
      font-size: 1rem;
    }

    .team-position {
      font-size: 0.9rem;
    }
  }
  
  .testimonial-section {
    max-width: 1000px;
    margin: 0 auto 40px auto;
    text-align: center;
    padding: 0 10px;
  }
  
  .testimonial-title {
    font-size: 2.4rem;
    font-weight: 800;
    margin-bottom: 0.4rem;
    letter-spacing: -1px;
  }
  
  .testimonial-title-our {
    color: #222;
  }
  
  .testimonial-title-happy {
    color: #ff375f;
  }
  
  .testimonial-desc {
    color: #888;
    font-size: 1rem;
    margin-bottom: 2.2rem;
    max-width: 540px;
    margin-left: auto;
    margin-right: auto;
  }
  
  .testimonial-cards {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    margin-bottom: 1.7rem;
  }
  
  .testimonial-card {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 18px rgba(80,80,80,0.07);
    padding: 2rem 1.2rem 1.5rem 1.2rem;
    flex: 1 1 0;
    max-width: 320px;
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 240px;
    transition: box-shadow 0.2s;
  }
  
  .testimonial-card:hover {
    box-shadow: 0 4px 24px rgba(80,80,80,0.13);
  }
  
  .testimonial-avatar {
    width: 68px;
    height: 68px;
    border-radius: 50%;
    object-fit: cover;
    margin-bottom: 1.2rem;
    border: 3px solid #fff;
    box-shadow: 0 2px 8px rgba(80,80,80,0.06);
  }
  
  .testimonial-text {
    color: #222;
    font-size: 1rem;
    margin-bottom: 1.1rem;
    line-height: 1.6;
    min-height: 60px;
  }
  
  .testimonial-stars {
    display: flex;
    gap: 0.1rem;
    margin-bottom: 1.1rem;
  }
  
  .testimonial-author {
    font-weight: 700;
    color: #222;
    font-size: 1.07rem;
    margin-bottom: 0.2rem;
  }
  
  .testimonial-role {
    color: #bbb;
    font-size: 0.95rem;
    font-weight: 500;
  }
  
  .testimonial-dots {
    margin-top: 1.3rem;
    display: flex;
    justify-content: center;
    gap: 0.5rem;
  }
  
  .dot {
    width: 8px;
    height: 8px;
    background: #e5e5e5;
    border-radius: 50%;
    display: inline-block;
    transition: background 0.2s;
  }
  
  .dot.active {
    background: #ff375f;
  }
  
  @media (max-width: 900px) {
    .testimonial-cards {
      flex-direction: column;
      align-items: center;
      gap: 1rem;
    }
    .testimonial-card {
      max-width: 100%;
      width: 100%;
    }
  }
  
  .blog-section {
    max-width: 1200px;
    margin: 0 auto 40px auto;
    padding: 0 10px;
    text-align: center;
  }
  
  .blog-title {
    font-size: 2.3rem;
    font-weight: 800;
    color: #222;
    margin-bottom: 0.2rem;
    letter-spacing: -1px;
  }
  
  .blog-subtitle {
    color: #bbb;
    font-size: 1rem;
    margin-bottom: 2.3rem;
    font-weight: 500;
  }
  
  .blog-cards {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    margin-bottom: 1.7rem;
  }
  
  .blog-card {
    background: #fff;
    border-radius: 13px;
    box-shadow: 0 2px 18px rgba(80,80,80,0.07);
    padding: 0 0 1.2rem 0;
    max-width: 340px;
    min-width: 270px;
    flex: 1 1 0;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    transition: box-shadow 0.2s;
  }
  
  .blog-card:hover {
    box-shadow: 0 4px 24px rgba(80,80,80,0.13);
  }
  
  .blog-card-img {
    width: 100%;
    height: 170px;
    display: flex;
    justify-content: center;   /* Center horizontally */
    align-items: center;       /* Center vertically (optional) */
    border-top-left-radius: 17px;
    border-top-right-radius: 17px;
  
    overflow: hidden;
  }
  
  .blog-card-img img {
    height: 100%;
    /* If you want the image to be centered and not stretched, use auto width: */
    width: auto;
    display: block;
  }
  
  
  .blog-card-meta {
    display: flex;
    justify-content: space-between;
    width: 100%;
    padding: 1.1rem 1.2rem 0.3rem 1.2rem;
    font-size: 0.97rem;
    color: #888;
    font-weight: 500;
  }
  
  .blog-author {
    color: #222;
  }
  
  .blog-date {
    color: #bbb;
  }
  
  .blog-card-title {
    font-size: 1.08rem;
    font-weight: 700;
    color: #222;
    padding: 0 1.2rem;
    margin-bottom: 1.3rem;
    text-align: left;
  }
  
  .blog-card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding: 0 1.2rem;
  }
  
  .blog-read-btn {
    background: #f5f5f7;
    color: #6c63ff;
    border: none;
    border-radius: 8px;
    padding: 0.45rem 1.2rem;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.2s, color 0.2s;
  }
  
  .blog-read-btn.active,
  .blog-read-btn:hover {
    background: #6c63ff;
    color: #fff;
  }
  
  .blog-bookmark-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.3rem;
    display: flex;
    align-items: center;
  }
  
  .blog-bookmark-btn svg {
    width: 20px;
    height: 20px;
  }
  
  .blog-dots {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 1.3rem;
  }
  
  .blog-dot {
    width: 8px;
    height: 8px;
    background: #e5e5e5;
    border-radius: 50%;
    display: inline-block;
    transition: background 0.2s;
    cursor: pointer;
  }
  
  .blog-dot.active {
    background: #6c63ff;
  }
  
  @media (max-width: 900px) {
    .blog-cards {
      flex-direction: column;
      align-items: center;
      gap: 1rem;
    }
    .blog-card {
      max-width: 100%;
      width: 100%;
    }
  }

  /* Responsive padding for AboutUs */
@media (max-width: 1400px) {
  .about-wrapper {
    padding: 6rem 4rem 4rem 4rem;
  }
}

@media (max-width: 768px) {
  .about-wrapper {
    padding: 4rem 2rem 3rem 2rem;
  }

  .about-grid {
    flex-direction: column;
    gap: 2rem;
    text-align: center;
  }

  .about-text h1 {
    font-size: 2rem;
    margin-bottom: 1rem;
  }

  .about-text p {
    font-size: 0.95rem;
    margin-bottom: 1.5rem;
  }

  .about-buttons {
    justify-content: center;
    flex-wrap: wrap;
    gap: 1rem;
  }

  .btn-primary {
    padding: 0.7rem 1.5rem;
    font-size: 0.95rem;
  }
}

@media (max-width: 480px) {
  .about-wrapper {
    padding: 3rem 1rem 2rem 1rem;
  }

  .about-text h1 {
    font-size: 1.8rem;
    line-height: 1.3;
  }

  .about-text p {
    font-size: 0.9rem;
    line-height: 1.6;
  }

  .about-buttons {
    flex-direction: column;
    align-items: center;
    gap: 0.8rem;
  }

  .btn-primary,
  .btn-text {
    width: 100%;
    max-width: 250px;
    text-align: center;
    padding: 0.8rem 1.2rem;
  }
}