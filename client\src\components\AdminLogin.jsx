import React, { useState, useEffect } from 'react';
import { FaUser, FaLock } from 'react-icons/fa';
import { useNavigate } from 'react-router-dom';
import ApiService from '../services/apiService';
import '../css/AdminLogin.css';
import PageHelmet from "./PageHelmet";

const AdminLogin = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  // Check if user is already logged in
  useEffect(() => {
    const token = localStorage.getItem('token');
    if (token) {
      navigate('/dashboard');
    }
  }, [navigate]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError(null);
    setLoading(true);

    try {
      const response = await ApiService.adminUsers.login({ username, password });
      
      // Store token and user info in localStorage
      localStorage.setItem('token', response.data.token);
      localStorage.setItem('user', JSON.stringify(response.data.user));
      
      // Redirect to dashboard
      navigate('/dashboard');
    } catch (err) {
      console.error('Login error:', err);
      setError(err.response?.data?.error || 'Login failed. Please check your credentials.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="admin-login-container">
      <PageHelmet 
        title="Admin Login" 
        description="Secure login portal for Job Page administrators."
      />
      <div className="admin-login-wrapper">
        <form onSubmit={handleSubmit}>
          <h1>ADMIN PORTAL</h1>
          
          {error && (
            <div className="admin-login-error">
              {error}
            </div>
          )}
          
          <div className="admin-login-input-group">
            <input 
              type="text" 
              placeholder="Username or Email"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              required
              disabled={loading}
            />
            <FaUser className="admin-login-icon" />
          </div>
          <div className="admin-login-input-group">
            <input 
              type="password" 
              placeholder="Password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              disabled={loading}
            />
            <FaLock className="admin-login-icon" />
          </div>

          <button 
            type="submit" 
            className="admin-login-btn"
            disabled={loading}
          >
            {loading ? 'Logging in...' : 'Access Dashboard'}
          </button>

          <div className="admin-login-decor">
            <div className="admin-login-decor-circle"></div>
            <div className="admin-login-decor-circle"></div>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AdminLogin;