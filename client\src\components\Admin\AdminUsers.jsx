/* eslint-disable no-unused-vars */
import React, { useState, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faPlus,
  faEdit,
  faTrash,
  faSearch,
  faExclamationCircle,
  faCheckCircle,
  faTimes,
  faSpinner,
  faUserShield,
  faUserCog,
  faUserEdit,
  faUser,
  faSync,
  faSignOutAlt,
  faExclamationTriangle
} from '@fortawesome/free-solid-svg-icons';
import ApiService from '../../services/apiService';
import '../../css/AdminUsers.css';
import '../../css/shared-delete-dialog.css';

const ROLE_OPTIONS = ['Superadmin', 'Admin', 'Editor'];
const ADMIN_ROLE_OPTIONS = ['Editor']; // Admin users can only create Editors

const AdminUsers = () => {
  // State
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [isAddingUser, setIsAddingUser] = useState(false);
  const [isEditingUser, setIsEditingUser] = useState(null);
  const [currentUser, setCurrentUser] = useState(null);
  const [retryCount, setRetryCount] = useState(0);
  
  // Delete confirmation dialog state
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [userToDelete, setUserToDelete] = useState(null);
  
  const [userForm, setUserForm] = useState({
    admin_username: '',
    admin_password: '',
    admin_email: '',
    role: 'Editor'
  });

  // Fetch current user info
  useEffect(() => {
    const fetchCurrentUser = async () => {
      try {
        const response = await ApiService.adminUsers.getCurrentUser();
        setCurrentUser(response.data);
      } catch (err) {
        console.error('Error fetching current user:', err);
        // If unauthorized, redirect to login
        if (err.response && (err.response.status === 401 || err.response.status === 403)) {
          // The global interceptor will handle the redirect
        }
      }
    };

    fetchCurrentUser();
  }, []);

  // Fetch all users
  useEffect(() => {
    const fetchUsers = async () => {
      setLoading(true);
      try {
        const response = await ApiService.adminUsers.getAll();
        setUsers(response.data);
        setError(null);
      } catch (err) {
        console.error('Error fetching users:', err);
        
        // Provide more specific error messages based on error type
        if (err.response) {
          // Server responded with an error status
          if (err.response.status === 401 || err.response.status === 403) {
            setError('Authentication error: You do not have permission to view user accounts. Please contact an administrator.');
            // Don't redirect - just show the error message
          } else if (err.response.status === 500) {
            setError('Server error. Please try again later.');
          } else {
            setError(`Failed to load users. ${err.response.data?.error || err.response.statusText || 'Unknown error'}`);
          }
        } else if (err.request) {
          // Request was made but no response received
          setError('Network error. Please check your connection and try again.');
        } else {
          // Something else caused the error
          setError(`Failed to load users. ${err.message || 'Unknown error'}`);
        }
      } finally {
        setLoading(false);
      }
    };

    fetchUsers();
  }, [retryCount]);

  // Handle retry
  const handleRetry = () => {
    setRetryCount(prevCount => prevCount + 1);
  };

  // Handle logout
  const handleLogout = () => {
    localStorage.removeItem('token');
    window.location.href = '/jp-admin';
  };

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setUserForm({ ...userForm, [name]: value });
  };

  // Handle user creation
  const handleCreateUser = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      const response = await ApiService.adminUsers.create(userForm);
      setUsers([response.data, ...users]);
      setIsAddingUser(false);
      setUserForm({
        admin_username: '',
        admin_password: '',
        admin_email: '',
        role: 'Editor'
      });
      setError(null);
    } catch (err) {
      console.error('Error creating user:', err);
      
      // Provide more specific error messages based on error type
      if (err.response) {
        if (err.response.status === 401 || err.response.status === 403) {
          setError('Authentication error. Please log in again.');
        } else {
          setError(`Failed to create user. ${err.response.data?.error || err.response.statusText || 'Unknown error'}`);
        }
      } else if (err.request) {
        setError('Network error. Please check your connection and try again.');
      } else {
        setError(`Failed to create user. ${err.message || 'Unknown error'}`);
      }
    } finally {
      setLoading(false);
    }
  };

  // Handle user update
  const handleUpdateUser = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      const response = await ApiService.adminUsers.update(isEditingUser, userForm);
      
      // Update users list
      setUsers(users.map(user => 
        user.admin_id === isEditingUser ? { ...user, ...response.data } : user
      ));
      
      setIsEditingUser(null);
      setUserForm({
        admin_username: '',
        admin_password: '',
        admin_email: '',
        role: 'Editor'
      });
      setError(null);
    } catch (err) {
      console.error('Error updating user:', err);
      
      // Provide more specific error messages based on error type
      if (err.response) {
        if (err.response.status === 401 || err.response.status === 403) {
          setError('Authentication error. Please log in again.');
        } else {
          setError(`Failed to update user. ${err.response.data?.error || err.response.statusText || 'Unknown error'}`);
        }
      } else if (err.request) {
        setError('Network error. Please check your connection and try again.');
      } else {
        setError(`Failed to update user. ${err.message || 'Unknown error'}`);
      }
    } finally {
      setLoading(false);
    }
  };

  // Handle user deletion
  const handleDeleteUser = async (userId) => {
    setLoading(true);
    try {
      await ApiService.adminUsers.delete(userId);
      setUsers(users.filter(user => user.admin_id !== userId));
      setError(null);
      
      // Reset delete confirmation dialog
      setShowDeleteConfirm(false);
      setUserToDelete(null);
    } catch (err) {
      console.error('Error deleting user:', err);
      
      // Provide more specific error messages based on error type
      if (err.response) {
        if (err.response.status === 401 || err.response.status === 403) {
          setError('Authentication error. Please log in again.');
        } else {
          setError(`Failed to delete user. ${err.response.data?.error || err.response.statusText || 'Unknown error'}`);
        }
      } else if (err.request) {
        setError('Network error. Please check your connection and try again.');
      } else {
        setError(`Failed to delete user. ${err.message || 'Unknown error'}`);
      }
    } finally {
      setLoading(false);
    }
  };

  // Function to show delete confirmation
  const confirmDelete = (user) => {
    setUserToDelete(user);
    setShowDeleteConfirm(true);
  };

  // Function to cancel delete
  const cancelDelete = () => {
    setShowDeleteConfirm(false);
    setUserToDelete(null);
  };

  // Handle edit button click
  const handleEditClick = (user) => {
    setIsEditingUser(user.admin_id);
    setUserForm({
      admin_username: user.admin_username,
      admin_password: '', // Don't populate password for security
      admin_email: user.admin_email,
      role: user.role
    });
  };

  // Filter users based on search term and current user role permissions
  const filteredUsers = users.filter(user => {
    // First apply search filter
    const matchesSearch = user.admin_username.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.admin_email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.role.toLowerCase().includes(searchTerm.toLowerCase());
    
    if (!matchesSearch) return false;
    
    // Apply role-based filtering
    if (currentUser?.role === 'Admin') {
      // Admin can only see Editors
      return user.role === 'Editor';
    }
    
    // Superadmin can see all users
    return true;
  });

  // Get role icon based on role
  const getRoleIcon = (role) => {
    switch (role) {
      case 'Superadmin':
        return <FontAwesomeIcon icon={faUserShield} className="role-icon superadmin" />;
      case 'Admin':
        return <FontAwesomeIcon icon={faUserCog} className="role-icon admin" />;
      case 'Editor':
        return <FontAwesomeIcon icon={faUserEdit} className="role-icon editor" />;
      default:
        return <FontAwesomeIcon icon={faUser} className="role-icon user" />;
    }
  };

  // Check if current user can edit a specific user
  const canEdit = (userRole) => {
    if (!currentUser) return false;
    
    const roleHierarchy = {
      'Superadmin': 4,
      'Admin': 3,
      'Editor': 2,
      'User': 1
    };
    
    // Superadmin can edit anyone
    if (currentUser.role === 'Superadmin') return true;
    
    // Admin can edit Editor and User
    if (currentUser.role === 'Admin') {
      return roleHierarchy[userRole] < roleHierarchy['Admin'];
    }
    
    return false;
  };

  // Check if current user can create new users
  const canCreate = () => {
    return currentUser && currentUser.role === 'Superadmin';
  };

  return (
    <div className="admin-users-container">
      {/* Delete Confirmation Dialog */}
      {showDeleteConfirm && (
        <div className="delete-confirm-overlay">
          <div className="delete-confirm-dialog">
            <div className="delete-confirm-header">
              <FontAwesomeIcon icon={faExclamationTriangle} className="delete-icon" />
              <h3>Confirm Deletion</h3>
            </div>
            <div className="delete-confirm-content">
              <p>Are you sure you want to delete this user?</p>
              <p><strong>{userToDelete?.admin_username}</strong> ({userToDelete?.role})</p>
              <p>This action cannot be undone.</p>
            </div>
            <div className="delete-confirm-actions">
              <button 
                className="cancel-delete-btn" 
                onClick={cancelDelete}
              >
                Cancel
              </button>
              <button 
                className="confirm-delete-btn" 
                onClick={() => handleDeleteUser(userToDelete.admin_id)}
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
      
      <div className="admin-users-header">
        <h2>
          <FontAwesomeIcon icon={faUserShield} className="admin-users-icon" />
          User Management
        </h2>
        <div className="admin-users-controls">
          <button className="refresh-button" onClick={handleRetry} disabled={loading}>
            <FontAwesomeIcon icon={faSync} />
            <span>Refresh</span>
          </button>
          <div className="admin-users-search">
            <FontAwesomeIcon icon={faSearch} className="search-icon" />
            <input
              type="text"
              placeholder="Search users..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              disabled={loading || error}
            />
          </div>
          {currentUser && (currentUser.role === 'Superadmin' || currentUser.role === 'Admin') && (
            <button 
              className="admin-users-add-btn" 
              onClick={() => {
                setIsAddingUser(true);
                // Set default role based on current user permissions
                setUserForm({
                  admin_username: '',
                  admin_password: '',
                  admin_email: '',
                  role: currentUser.role === 'Admin' ? 'Editor' : 'Editor'
                });
              }}
              disabled={loading || error}
            >
              <FontAwesomeIcon icon={faPlus} /> Add {currentUser.role === 'Admin' ? 'Editor' : 'User'}
            </button>
          )}
        </div>
      </div>

      {error && (
        <div className="admin-users-error">
          <FontAwesomeIcon icon={faExclamationCircle} />
          <div className="error-content">
            <p>{error}</p>
            {error.includes('Authentication error') ? (
              <div className="error-actions">
                <p>You need Admin or Superadmin privileges to access this section.</p>
                <button onClick={handleRetry} className="retry-btn">
                  <FontAwesomeIcon icon={faSync} /> Try Again
                </button>
              </div>
            ) : (
              <button onClick={handleRetry} className="retry-btn">
                <FontAwesomeIcon icon={faSync} /> Try Again
              </button>
            )}
          </div>
        </div>
      )}

      {/* Add User Form Modal */}
      {isAddingUser && (
        <div className="admin-users-modal">
          <div className="admin-users-modal-content">
            <div className="admin-users-modal-header">
              <h3>Add New User</h3>
              <button className="close-btn" onClick={() => setIsAddingUser(false)}>
                <FontAwesomeIcon icon={faTimes} />
              </button>
            </div>
            <form onSubmit={handleCreateUser}>
              <div className="form-group">
                <label htmlFor="admin_username">Username</label>
                <input
                  type="text"
                  id="admin_username"
                  name="admin_username"
                  value={userForm.admin_username}
                  onChange={handleInputChange}
                  required
                />
              </div>
              <div className="form-group">
                <label htmlFor="admin_password">Password</label>
                <input
                  type="password"
                  id="admin_password"
                  name="admin_password"
                  value={userForm.admin_password}
                  onChange={handleInputChange}
                  required
                />
              </div>
              <div className="form-group">
                <label htmlFor="admin_email">Email</label>
                <input
                  type="email"
                  id="admin_email"
                  name="admin_email"
                  value={userForm.admin_email}
                  onChange={handleInputChange}
                  required
                />
              </div>
              <div className="form-group">
                <label htmlFor="role">Role</label>
                <select
                  id="role"
                  name="role"
                  value={userForm.role}
                  onChange={handleInputChange}
                  required
                >
                  {(currentUser?.role === 'Admin' ? ADMIN_ROLE_OPTIONS : ROLE_OPTIONS).map(role => (
                    <option key={role} value={role}>{role}</option>
                  ))}
                </select>
              </div>
              <div className="form-actions">
                <button type="button" onClick={() => setIsAddingUser(false)} className="cancel-btn">
                  Cancel
                </button>
                <button type="submit" className="submit-btn" disabled={loading}>
                  {loading ? <FontAwesomeIcon icon={faSpinner} spin /> : 'Create User'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Edit User Form Modal */}
      {isEditingUser && (
        <div className="admin-users-modal">
          <div className="admin-users-modal-content">
            <div className="admin-users-modal-header">
              <h3>Edit User</h3>
              <button className="close-btn" onClick={() => setIsEditingUser(null)}>
                <FontAwesomeIcon icon={faTimes} />
              </button>
            </div>
            <form onSubmit={handleUpdateUser}>
              <div className="form-group">
                <label htmlFor="edit_admin_username">Username</label>
                <input
                  type="text"
                  id="edit_admin_username"
                  name="admin_username"
                  value={userForm.admin_username}
                  onChange={handleInputChange}
                  required
                />
              </div>
              <div className="form-group">
                <label htmlFor="edit_admin_password">Password (leave blank to keep current)</label>
                <input
                  type="password"
                  id="edit_admin_password"
                  name="admin_password"
                  value={userForm.admin_password}
                  onChange={handleInputChange}
                />
              </div>
              <div className="form-group">
                <label htmlFor="edit_admin_email">Email</label>
                <input
                  type="email"
                  id="edit_admin_email"
                  name="admin_email"
                  value={userForm.admin_email}
                  onChange={handleInputChange}
                  required
                />
              </div>
              <div className="form-group">
                <label htmlFor="edit_role">Role</label>
                <select
                  id="edit_role"
                  name="role"
                  value={userForm.role}
                  onChange={handleInputChange}
                  required
                >
                  {(currentUser?.role === 'Admin' ? ADMIN_ROLE_OPTIONS : ROLE_OPTIONS).map(role => (
                    <option key={role} value={role}>{role}</option>
                  ))}
                </select>
              </div>
              <div className="form-actions">
                <button type="button" onClick={() => setIsEditingUser(null)} className="cancel-btn">
                  Cancel
                </button>
                <button type="submit" className="submit-btn" disabled={loading}>
                  {loading ? <FontAwesomeIcon icon={faSpinner} spin /> : 'Update User'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {loading ? (
        <div className="admin-users-loading">
          <FontAwesomeIcon icon={faSpinner} spin />
          <p>Loading users...</p>
        </div>
      ) : !error ? (
        <div className="admin-users-content">
          {/* Rest of the component rendering users table */}
          {loading && !isAddingUser && !isEditingUser ? (
            <div className="loading-spinner">
              <FontAwesomeIcon icon={faSpinner} spin size="2x" />
              <p>Loading users...</p>
            </div>
          ) : (
          <table className="admin-users-table">
            <thead>
              <tr>
                <th>ID</th>
                <th>Username</th>
                <th>Email</th>
                <th>Role</th>
                <th>Created</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {filteredUsers.length > 0 ? (
                filteredUsers.map(user => (
                  <tr key={user.admin_id}>
                    <td>{user.admin_id}</td>
                    <td>{user.admin_username}</td>
                    <td>{user.admin_email}</td>
                    <td>
                      <div className="role-badge">
                        {getRoleIcon(user.role)}
                        <span>{user.role}</span>
                      </div>
                    </td>
                    <td>{new Date(user.created_at).toLocaleDateString()}</td>
                    <td>
                      <div className="table-actions">
                        {canEdit(user.role) && (
                          <button 
                            className="edit-btn"
                            onClick={() => handleEditClick(user)}
                            title="Edit User"
                          >
                            <FontAwesomeIcon icon={faEdit} />
                          </button>
                        )}
                        
                        {(currentUser?.role === 'Superadmin' || 
                          (currentUser?.role === 'Admin' && user.role === 'Editor')) && (
                          <button 
                            className="delete-btn"
                            onClick={() => confirmDelete(user)}
                            title="Delete User"
                            disabled={currentUser.admin_id === user.admin_id}
                          >
                            <FontAwesomeIcon icon={faTrash} />
                          </button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan="6" className="no-results">
                    {searchTerm ? 'No users match your search.' : 'No users found.'}
                  </td>
                </tr>
              )}
            </tbody>
          </table>
          )}
        </div>
      ) : null}
    </div>
  );
};

export default AdminUsers;