const multer = require('multer');
const path = require('path');

// Configure storage
const storage = multer.memoryStorage();

// Handle file uploads with memory storage for processing in controllers
const upload = multer({
  storage: storage,
  limits: { fileSize: 5 * 1024 * 1024 }, // 5MB limit
  fileFilter: (req, file, cb) => {    // Skip validation if no file is selected or file is empty
    if (!file || !file.originalname) {
      return cb(null, true);
    }
    
    // Accept only image files
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'), false);
    }
  }
});

// Export the middleware
module.exports = upload; 