import React from 'react';
import { Link } from 'react-router-dom';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faMapMarkerAlt, faClock } from '@fortawesome/free-solid-svg-icons';
import '../css/UrgentJobCard.css';

const UrgentJobCard = ({ job, onViewIncrement }) => {
  return (
    <div className="urgent-job-card">
      <Link 
        to={`/job/${job.id}`} 
        style={{ textDecoration: 'none', color: 'inherit' }}
        onClick={() => onViewIncrement && onViewIncrement(job.id)}
      >
        <span className="urgent-tag">🔥 URGENT</span>
        <div className="urgent-job-image">
          <img 
            src={job.image} 
            alt={job.position}
            onError={(e) => {
              e.target.onerror = null;
              e.target.src = 'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=300&h=200&fit=crop';
            }}
          />
        </div>
        <div className="urgent-job-content">
          <div className="company-info-urgent">
            <div className="company-logo-urgent">
              <img 
                src={job.logo} 
                alt={job.company}
                onError={(e) => {
                  e.target.onerror = null;
                  e.target.src = 'https://via.placeholder.com/30';
                }}
              />
            </div>
            <div className="company-details-urgent">
              <h3 className="company-name-urgent">{job.company}</h3>
            </div>
          </div>
          <h2 className="job-position-urgent">{job.position}</h2>
          <div className="job-meta-urgent">
            <span className="job-meta-item-urgent">
              <FontAwesomeIcon icon={faMapMarkerAlt} /> {job.location}
            </span>
            <span className="job-meta-item-urgent">
              <FontAwesomeIcon icon={faClock} /> {job.workTime}
            </span>
          </div>
          <div className="job-footer-urgent">
            <div className="job-salary-urgent">{job.salary}</div>
            <div className="job-posted-urgent">{job.postedTime}</div>
          </div>
        </div>
      </Link>
    </div>
  );
};

export default UrgentJobCard;