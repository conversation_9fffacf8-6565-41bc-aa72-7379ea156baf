const mysql = require("mysql");
require("dotenv").config();

// MySQL Database Connection
const db = mysql.createConnection({
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASS || '',
  database: process.env.DB_NAME || 'jp_db'
});

// Connect to Database
db.connect(err => {
  if (err) {
    console.error("Database connection failed: " + err.message);
    console.error("Make sure XAMPP MySQL service is running and the database 'jp_db' exists");
    process.exit(1); // Stop server if DB connection fails
  }
  console.log("Database connected");
});

module.exports = db; 