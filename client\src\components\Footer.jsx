import React from 'react';
import '../css/Footer.css';
import jobhubLogo from '../assets/job-page-logo.png';
import { FaFacebook, FaTiktok, FaInstagram, FaLinkedin } from 'react-icons/fa';

const Footer = () => {
  return (
    <footer className="footer">
      <div className="footer-container">
        {/* Logo and Description */}
        <div className="footer-brand">
          <div className="logo">
            <img src={jobhubLogo} alt="JobPage Logo" />
          </div>
          <p className="footer-description">
            JobPage is your premier destination for finding the best job opportunities and connecting with employers in Sri Lanka & global.
          </p>
        </div>

        {/* Footer Links */}
        <div className="footer-links">
          {/* For Job Seekers */}
          <div className="footer-column">
            <h3>Quick Links</h3>
            <ul>
              <li><a href="/browse">Browse Jobs</a></li>
              <li><a href="/company">Top Companies</a></li>
            </ul>
          </div>

          {/* Resources */}
          <div className="footer-column">
            <h3>Resources</h3>
            <ul>
              <li><a href="/blogs">Blogs</a></li>
              <li><a href="/faq">FAQ</a></li>
            </ul>
          </div>

          {/* Company */}
          <div className="footer-column">
            <h3>Company</h3>
            <ul>
              <li><a href="/about">About Us</a></li>
              <li><a href="/contact">Contact Us</a></li>
            </ul> 
          </div>

                    {/* For Employers */}
          <div className="footer-column">
            <h3>Legal</h3>
            <ul>
              <li><a href="/privacy">Privacy Policy</a></li>
              <li><a href="/terms">Terms & Conditions</a></li>
            </ul>
          </div>
        </div>
      </div>

      {/* Copyright */}
      <div className="footer-bottom">
        <div className="copyright">
          <p>Copyright ©2025 <a href="/">JobPage.lk</a>. All Rights Reserved</p>
        </div>  
      </div>
    </footer>
  );
};

export default Footer;