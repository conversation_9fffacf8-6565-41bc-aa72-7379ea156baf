const db = require('../config/db');

const Blog = {
  // Get all blogs
  getAll: (callback) => {
    const query = "SELECT * FROM blog_details";
    db.query(query, callback);
  },

  // Get blog by ID
  getById: (blogId, callback) => {
    const query = "SELECT * FROM blog_details WHERE blog_id = ?";
    db.query(query, [blogId], callback);
  },

  // Create new blog
  create: (blogData, callback) => {
    const { blog_title, blog_description, posted_date, image_url, category } = blogData;

    const query = `INSERT INTO blog_details (blog_title, blog_description, posted_date, image_url, category) 
                 VALUES (?, ?, ?, ?, ?)`;

    db.query(query, [blog_title, blog_description, posted_date, image_url || null, category], callback);
  },

  // Update blog
  update: (blogId, blogData, callback) => {
    const { blog_title, blog_description, image_url, category } = blogData;
    // Note: posted_date is excluded from updates to preserve original creation date

    // Only update image_url if a new image is provided (not null, undefined, or empty string)
    if (image_url && typeof image_url === 'string' && image_url.trim() !== '') {
      const query = `UPDATE blog_details 
                   SET blog_title = ?, blog_description = ?, image_url = ?, category = ? 
                   WHERE blog_id = ?`;

      db.query(query, [blog_title, blog_description, image_url, category, blogId], callback);
    } else {
      const query = `UPDATE blog_details 
                   SET blog_title = ?, blog_description = ?, category = ? 
                   WHERE blog_id = ?`;

      db.query(query, [blog_title, blog_description, category, blogId], callback);
    }
  },

  // Delete blog
  delete: (blogId, callback) => {
    const query = "DELETE FROM blog_details WHERE blog_id = ?";
    db.query(query, [blogId], callback);
  },
  
  // Increment view count for a blog
  incrementViewCount: (blogId, callback) => {
    // First check if views column exists in the table
    const checkQuery = `
      SELECT COUNT(*) AS column_exists 
      FROM information_schema.COLUMNS 
      WHERE TABLE_NAME = 'blog_details' 
      AND COLUMN_NAME = 'views'
    `;
    
    db.query(checkQuery, (checkErr, checkResult) => {
      if (checkErr) {
        console.error("Error checking for views column:", checkErr);
        return callback(checkErr, null);
      }
      
      // If column doesn't exist, add it
      if (checkResult[0].column_exists === 0) {
        const alterQuery = `
          ALTER TABLE blog_details 
          ADD COLUMN views INT DEFAULT 0
        `;
        
        db.query(alterQuery, (alterErr) => {
          if (alterErr) {
            console.error("Error adding views column:", alterErr);
            return callback(alterErr, null);
          }
          
          // After adding the column, proceed with the increment
          incrementCount();
        });
      } else {
        // Column already exists, proceed with the increment
        incrementCount();
      }
    });
    
    // Function to increment the view count
    function incrementCount() {
      const updateQuery = `
        UPDATE blog_details 
        SET views = IFNULL(views, 0) + 1 
        WHERE blog_id = ?
      `;
      
      db.query(updateQuery, [blogId], (updateErr, updateResult) => {
        if (updateErr) {
          console.error("Error incrementing blog view count:", updateErr);
          return callback(updateErr, null);
        }
        
        callback(null, updateResult);
      });
    }
  }
};

module.exports = Blog;