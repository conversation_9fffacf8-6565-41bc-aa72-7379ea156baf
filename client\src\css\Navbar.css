/* Navbar styles */
.navbar {
  color: var(--text-color-b);
  position: fixed;
  top: 0;
  background-color: var(--background-color);
  width: 100%;
  z-index: 999;
}

.navbar-container{
  padding: var(--padding-size);
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}

.navbar .logo img {
  height: 40px;
}

.nav-links {
  display: flex;
  list-style-type: none;
  margin: 0;
  padding: 0;
}

.nav-links li {
  margin: 0 20px;
}

.nav-links a {
  text-decoration: none;
  color: inherit;
  display: block;
  transition: color 0.3s;
  white-space: nowrap;
  text-align: center;
  padding: 10px 0;
}

.nav-links a:hover {
  color: var(--primary-color);
}

.nav-links a.active {
  color: var(--primary-color); /* Change color for active link */
  border-bottom: 2px solid var(--primary-color); /* Add a bottom border */
  padding-bottom: 8px; /* Adjust padding to make space for the border */
}

.nav-buttons {
  
  display: flex;
  gap: 10px;
  flex-grow: 1; /* Allow container to grow */
  justify-content: center; /* Center buttons horizontally */
}

.nav-buttons .apply-btn,
.nav-buttons .post-btn {
  display: inline-block;
  text-decoration: none;
  padding: 10px 20px;
  border-radius: 10px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s, color 0.3s;
  text-align: center;
  white-space: nowrap;
  min-width: 90px;
}

.nav-buttons .apply-btn {
  background-color: var(--primary-color);
  color: white;
  border: none;
  /* margin-right: 5px; Removed for better centering with gap */
  padding: 16px 28px; /* Added for equal padding */
}

.nav-buttons .apply-btn:hover {
  background-color: var(--primary-color-h);
}

.nav-buttons .post-btn {
  background-color: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
}

.nav-buttons .post-btn:hover {
  background-color: var(--primary-color);
  color: white;
}

/* Hamburger Menu */
.hamburger {
  display: none;
  cursor: pointer;
  background: transparent;
  border: none;
  padding: 10px;
}

.hamburger .bar {
  display: block;
  width: 25px;
  height: 3px;
  margin: 5px 0;
  background-color: var(--text-color-b);
  transition: all 0.3s ease;
}

/* Mobile Menu Styles */
.navbar-collapse {
  display: flex;
  align-items: center;
  gap: 20px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hamburger {
      display: block;
  }

  .hamburger.active .bar:nth-child(1) {
      transform: rotate(45deg) translate(5px, 5px);
  }

  .hamburger.active .bar:nth-child(2) {
      opacity: 0;
  }

  .hamburger.active .bar:nth-child(3) {
      transform: rotate(-45deg) translate(7px, -6px);
  }

  .navbar-collapse {
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      background-color: var(--background-color);
      flex-direction: column;
      padding: 20px;
      display: none;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }

  .navbar-collapse.active {
      display: flex;
      padding-top: 50px;
  }

  .nav-links {
      flex-direction: column;
      align-items: center;
      width: 100%;
      margin-bottom: 20px;
  }

  .nav-links li {
      margin: 10px 0;
  }

  .nav-buttons {
      flex-direction: column;
      width: 100%;
      max-width: 300px;
  }

  .nav-buttons button {
      width: 100%;
  }
}

@media (max-width: 480px) {
  .navbar .logo img {
      height: 30px;
  }

  .navbar-container {
      padding: 15px;
  }
}
