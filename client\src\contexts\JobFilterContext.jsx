import React, { createContext, useState, useContext, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';

// Create context
const JobFilterContext = createContext();

// Provider component
export const JobFilterProvider = ({ children }) => {
  const [searchParams, setSearchParams] = useSearchParams();
  
  // Job filter states
  const [jobType, setJobType] = useState('');
  const [searchKeyword, setSearchKeyword] = useState('');
  const [minSalary, setMinSalary] = useState('0');
  const [maxSalary, setMaxSalary] = useState('1000000');
  const [salaryFilterApplied, setSalaryFilterApplied] = useState(false);
  
  // Sync state from URL parameters on initial load
  useEffect(() => {
    const search = searchParams.get('search');
    const urlJobType = searchParams.get('jobType');
    const minSal = searchParams.get('minSal');
    const maxSal = searchParams.get('maxSal');
    
    if (search) {
      setSearchKeyword(search);
    }
    
    if (urlJobType) {
      setJobType(urlJobType);
    }
    
    if (minSal) {
      setMinSalary(minSal);
      setSalaryFilterApplied(true);
    }
    
    if (maxSal) {
      setMaxSalary(maxSal);
      setSalaryFilterApplied(true);
    }
  }, [searchParams]);
  
  // Update job type and URL parameters
  const updateJobType = (newJobType) => {
    setJobType(newJobType);
    
    // Update URL parameters
    const newSearchParams = new URLSearchParams(searchParams);
    
    if (newJobType && newJobType !== '') {
      newSearchParams.set('jobType', newJobType);
    } else {
      newSearchParams.delete('jobType');
    }
    
    setSearchParams(newSearchParams);
  };
  
  // Update search keyword and URL parameters
  const updateSearchKeyword = (keyword) => {
    setSearchKeyword(keyword);
    
    // Update URL parameters
    const newSearchParams = new URLSearchParams(searchParams);
    
    if (keyword && keyword.trim() !== '') {
      newSearchParams.set('search', keyword.trim());
    } else {
      newSearchParams.delete('search');
    }
    
    setSearchParams(newSearchParams);
  };
  
  // Update salary range and URL parameters
  const updateSalaryRange = (min, max) => {
    // Handle empty values and NaN
    const parsedMin = min === '' || isNaN(parseInt(min)) ? '0' : min.toString();
    const parsedMax = max === '' || isNaN(parseInt(max)) ? '1000000' : max.toString();
    
    setMinSalary(parsedMin);
    setMaxSalary(parsedMax);
    
    // Mark salary filter as applied if values are different from default
    const isDefault = (parsedMin === '0') && (parsedMax === '1000000');
    setSalaryFilterApplied(!isDefault);
    
    // Update URL parameters
    const newSearchParams = new URLSearchParams(searchParams);
    
    if (parsedMin !== '0') {
      newSearchParams.set('minSal', parsedMin);
    } else {
      newSearchParams.delete('minSal');
    }
    
    if (parsedMax !== '1000000') {
      newSearchParams.set('maxSal', parsedMax);
    } else {
      newSearchParams.delete('maxSal');
    }
    
    setSearchParams(newSearchParams);
  };
  
  // Reset all filters
  const resetFilters = () => {
    setJobType('');
    setSearchKeyword('');
    setMinSalary('0');
    setMaxSalary('1000000');
    setSalaryFilterApplied(false);
    
    // Clear URL parameters
    const newSearchParams = new URLSearchParams();
    setSearchParams(newSearchParams);
  };
  
  return (
    <JobFilterContext.Provider
      value={{
        jobType,
        searchKeyword,
        minSalary,
        maxSalary,
        salaryFilterApplied,
        updateJobType,
        updateSearchKeyword,
        updateSalaryRange,
        resetFilters
      }}
    >
      {children}
    </JobFilterContext.Provider>
  );
};

// Custom hook to use the job filter context
export const useJobFilter = () => {
  const context = useContext(JobFilterContext);
  if (!context) {
    throw new Error('useJobFilter must be used within a JobFilterProvider');
  }
  return context;
};

export default JobFilterContext; 