import React from 'react';
import '../css/SocialMediaFeeds.css';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faInstagram,
  faFacebookF,
  faTiktok,
  faLinkedinIn,
  faYoutube
} from '@fortawesome/free-brands-svg-icons';

// Social media platform data with FontAwesome icons and links
const socialPlatforms = [
  { 
    name: 'Instagram', 
    color: 'linear-gradient(45deg, #e1306c, #fd5949, #f77737, #fccc63, #833ab4)', 
    icon: faInstagram,
    url: 'https://www.instagram.com/jobpagelk/' // Replace with your Instagram URL
  },
  { 
    name: 'Facebook', 
    color: 'linear-gradient(45deg, #0077b5, #00a0dc)', 
    icon: faFacebookF,
    url: 'https://www.facebook.com/profile.php?id=61564913969342' // Replace with your Facebook URL
  },
  { 
    name: 'LinkedIn', 
    color: 'linear-gradient(45deg, #1877f2, #42a5f5)', 
    icon: faLinkedinIn,
    url: 'https://www.linkedin.com/company/jobpage-lk' // Replace with your LinkedIn URL
  },
  { 
    name: 'YouTube', 
    color: 'linear-gradient(45deg, #ff0000, #ff4444)', 
    icon: faYoutube,
    url: 'https://www.youtube.com/@jobpage_rakiyapituwa' // Replace with your YouTube URL
  },
  { 
    name: 'TikTok', 
    color: '#000000', 
    icon: faTiktok,
    url: 'https://www.tiktok.com/@jobpage.lk' // Replace with your TikTok URL
  }
];

const SocialMediaFeeds = () => {
  const handleSocialClick = (url) => {
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  return (
    <div className="social-media-feeds-container">
      {socialPlatforms.map((platform, index) => (
        <div 
          key={platform.name} 
          className="social-media-feed-box" 
          style={{ 
            background: platform.color,
            animationDelay: `${index * 0.2}s`,
            cursor: 'pointer'
          }}
          onClick={() => handleSocialClick(platform.url)}
          role="button"
          tabIndex={0}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              handleSocialClick(platform.url);
            }
          }}
          aria-label={`Visit our ${platform.name} page`}
          title={`Click to visit our ${platform.name} page`}
        >
          <div className="social-media-icon">
            <FontAwesomeIcon icon={platform.icon} size="2x" />
          </div>
          <span className="social-media-name">{platform.name} Feeds</span>
        </div>
      ))}
    </div>
  );
};

export default SocialMediaFeeds;
