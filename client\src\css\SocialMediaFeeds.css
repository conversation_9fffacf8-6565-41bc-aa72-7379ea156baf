.social-media-feeds-container {
    display: flex;
    justify-content: space-between;
    width: 100%;
    overflow-x: auto;
    padding: 0 20px;
    box-sizing: border-box;
    margin: 0 auto;
    flex-wrap: wrap;
    gap: 20px;
  }
  
  .social-media-feed-box {
    flex: 1;
    min-width: 150px;
    height: 200px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
    text-align: center;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    opacity: 0;
    animation: fadeInSlide 0.5s forwards;
    margin-bottom: 10px;
    border-radius: 8px;
  }
  
  .social-media-feed-box:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  }
    .social-media-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    margin-bottom: 15px;
    font-size: 40px;
    transition: transform 0.3s ease;
  }
  
  .social-media-feed-box:hover .social-media-icon {
    transform: scale(1.1);
  }
  
  .social-media-name {
    font-weight: bold;
    font-size: 16px;
    margin-top: 5px;
  }
  
  @keyframes fadeInSlide {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  /* Responsive adjustments */
  @media (max-width: 768px) {
    .social-media-feeds-container {
      flex-direction: row;
      flex-wrap: wrap;
      justify-content: center;
    }
    
    .social-media-feed-box {
      flex: 0 0 calc(50% - 20px);
      margin-bottom: 20px;
    }
  }
  
  @media (max-width: 480px) {
    .social-media-feed-box {
      flex: 0 0 100%;
    }
  }