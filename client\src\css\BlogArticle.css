/* Separator bar for page */
.page-separator {
  position: fixed;
  top: 80px;
  left: 0;
  width: 100%;
  height: 50px;
  background-color: #FFF4E9;
  z-index: 1100;
}

/* Base styles */
.blog-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 150px 20px 20px 20px; /* Increased top padding for navbar + separator */
    display: flex;
    flex-wrap: wrap;
    font-family: Arial, sans-serif;
    color: #333;
    position: relative;
  }
  
  /* Article Header */
  .article-header {
    width: 100%;
    margin-bottom: 30px;
    text-align: center;
  }
  
  .article-header h1 {
    font-size: 32px;
    margin-bottom: 20px;
    color: #222;
    font-weight: 700;
  }
  
  .author-info {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
  }
  
  .author-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
  }
  
  .author-name {
    font-weight: 600;
    color: #555;
  }
  
  .post-date {
    color: #888;
    font-size: 14px;
  }
  
  .post-stats {
    display: flex;
    gap: 10px;
    margin-left: 10px;
  }
  
  /* Main content layout */
  .article-content {
    width: 65%;
    padding-right: 30px;
  }
  
  .sidebar {
    width: 35%;
    padding-left: 20px;
  }
  
  /* Article content styling */
  .main-image {
    width: 100%;
    margin-bottom: 20px;
  }
  
  .main-image img {
    width: 100%;
    height: auto;
    border-radius: 8px;
  }
  
  .intro-text {
    font-size: 16px;
    line-height: 1.6;
    margin-bottom: 30px;
    color: #555;
  }
  
  .lead-paragraph {
    font-size: 18px;
    line-height: 1.7;
    margin-bottom: 20px;
  }
  
  .drop-cap {
    float: left;
    font-size: 60px;
    line-height: 0.8;
    padding-right: 8px;
    padding-top: 4px;
    font-weight: bold;
    color: #333;
  }
  
  .article-text p {
    margin-bottom: 20px;
    line-height: 1.7;
  }
  
  .article-images {
    margin: 30px 0;
  }
  
  .image-row {
    display: flex;
    gap: 20px;
    justify-content: space-between;
  }
  
  .article-image {
    width: calc(50% - 10px);
    border-radius: 8px;
  }
  
  .article-quote {
    border-left: 4px solid #6c5ce7;
    padding: 20px;
    margin: 30px 0;
    background-color: #f8f9fa;
    position: relative;
  }
  
  .article-quote p {
    font-style: italic;
    font-size: 18px;
    line-height: 1.7;
    margin-bottom: 15px;
  }
  
  .quote-source {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #6c5ce7;
    font-weight: 600;
    font-size: 14px;
  }
  
  .article-tags {
    display: flex;
    gap: 10px;
    margin: 30px 0;
  }
  
  .tag {
    background-color: #f0f0f0;
    padding: 5px 12px;
    border-radius: 30px;
    font-size: 14px;
    color: #555;
  }
  
  .article-metrics {
    display: flex;
    gap: 15px;
    margin-bottom: 30px;
    font-size: 14px;
    color: #666;
  }
  
  /* Author bio section */
  .author-bio {
    display: flex;
    gap: 15px;
    background-color: #f9f9f9;
    padding: 20px;
    border-radius: 8px;
    margin: 40px 0;
  }
  
  .author-large-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
  }
  
  .author-details h3 {
    margin-bottom: 8px;
    font-size: 18px;
  }
  
  .author-description {
    font-size: 14px;
    line-height: 1.6;
    color: #555;
  }
  
  /* Related posts */
  .related-posts-section {
    margin: 40px 0;
  }
  
  .section-title {
    margin-bottom: 20px;
    font-size: 24px;
    color: #222;
    font-weight: 600;
  }
  
  .related-posts {
    display: flex;
    gap: 20px;
    justify-content: space-between;
  }
  
  .related-post {
    width: calc(33.33% - 14px);
  }
  
  .related-post-image {
    width: 100%;
    border-radius: 8px;
    margin-bottom: 10px;
  }
  
  .related-post-title {
    font-size: 16px;
    line-height: 1.4;
    font-weight: 600;
  }
  
  /* Comment section */
  .comment-section {
    margin-top: 40px;
  }
  
  .comment-form {
    margin-bottom: 40px;
  }
  
  .form-disclaimer {
    font-size: 14px;
    color: #777;
    margin-bottom: 20px;
  }
  
  .form-inputs {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
  }
  
  .form-group {
    width: calc(50% - 8px);
  }
  
  .form-group.full-width {
    width: 100%;
  }
  
  .form-control {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 15px;
  }
  
  textarea.form-control {
    min-height: 120px;
    resize: vertical;
  }
  
  .post-comment-btn {
    background-color: #6c5ce7;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    margin-top: 10px;
  }
  
  .post-comment-btn:hover {
    background-color: #5d4dcb;
  }
  
  .comments-count {
    margin-bottom: 20px;
    font-size: 20px;
    font-weight: 600;
  }
  
  .comment {
    display: flex;
    gap: 15px;
    margin-bottom: 30px;
  }
  
  .commenter-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
  }
  
  .comment-content h4 {
    margin-bottom: 8px;
    font-size: 16px;
  }
  
  .comment-text {
    font-size: 14px;
    line-height: 1.6;
    color: #444;
    margin-bottom: 8px;
  }
  
  .reply-link {
    font-size: 14px;
    color: #6c5ce7;
    text-decoration: none;
    font-weight: 600;
  }
  
  /* Sidebar styling */
  .search-box {
    margin-bottom: 30px;
  }
  
  .search-input {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 15px;
  }
  
  .sidebar-section {
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 30px;
  }
  
  .sidebar-title {
    margin-bottom: 15px;
    font-size: 18px;
    font-weight: 600;
  }
  
  /* Category list */
  .category-list {
    list-style: none;
    padding: 0;
  }
  
  .category-item {
    display: flex;
    justify-content: space-between;
    padding: 10px 0;
    border-bottom: 1px solid #eee;
  }
  
  .category-item:last-child {
    border-bottom: none;
  }
  
  .category-count {
    background-color: #f0f0f0;
    padding: 2px 8px;
    border-radius: 30px;
    font-size: 12px;
    color: #555;
  }
  
  /* Latest news */
  .news-items {
    display: flex;
    flex-direction: column;
    gap: 15px;
  }
  
  .news-item {
    display: flex;
    gap: 12px;
  }
  
  .news-image {
    width: 70px;
    height: 70px;
    border-radius: 6px;
    object-fit: cover;
  }
  
  .news-content {
    flex: 1;
  }
  
  .news-title {
    font-size: 14px;
    font-weight: 600;
    line-height: 1.4;
    margin-bottom: 8px;
  }
  
  .news-author {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: #777;
  }
  
  .news-author-avatar {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    object-fit: cover;
  }
  
  /* Tags cloud */
  .tags-cloud {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .tags-cloud .tag {
    padding: 4px 10px;
    font-size: 12px;
    background-color: white;
    border: 1px solid #eee;
  }
  
  /* Job planners section */
  .job-planners-section {
    background-color: #f0f4ff;
    position: relative;
    overflow: hidden;
  }
  
  .job-planners-content {
    position: relative;
    z-index: 1;
  }
  
  .featured-title {
    font-size: 14px;
    color: #6c5ce7;
    margin-bottom: 8px;
  }
  
  .job-planners-title {
    font-size: 22px;
    font-weight: 600;
    margin-bottom: 10px;
  }
  
  .job-planners-text {
    font-size: 14px;
    margin-bottom: 15px;
  }
  
  .enroll-button {
    background-color: #ffb100;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 30px;
    font-weight: 600;
    cursor: pointer;
    margin-bottom: 10px;
  }
  
  .job-planners-image {
    position: absolute;
    bottom: 0;
    right: 0;
  }
  
  /* Responsive adjustments */
  @media screen and (max-width: 992px) {
    .article-content {
      width: 100%;
      padding-right: 0;
    }
    
    .sidebar {
      width: 100%;
      padding-left: 0;
      margin-top: 40px;
    }
    
    .related-posts {
      flex-direction: column;
    }
    
    .related-post {
      width: 100%;
      margin-bottom: 20px;
    }
  }
  
  @media screen and (max-width: 768px) {
    .image-row {
      flex-direction: column;
    }
    
    .article-image {
      width: 100%;
      margin-bottom: 15px;
    }
    
    .form-group {
      width: 100%;
    }
  }