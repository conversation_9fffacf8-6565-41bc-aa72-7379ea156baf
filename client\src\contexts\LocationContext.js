import React, { createContext, useContext, useState } from 'react';

// Create the Location Context
const LocationContext = createContext();

// Custom hook to use the Location Context
export const useLocation = () => {
  const context = useContext(LocationContext);
  if (!context) {
    throw new Error('useLocation must be used within a LocationProvider');
  }
  return context;
};

// Location Provider component
export const LocationProvider = ({ children }) => {
  const [selectedLocation, setSelectedLocation] = useState('');

  // Sri Lankan Districts (shared between SearchArea and JobBoard)
  const districts = [
    "Ampara", "Anuradhapura", "Badulla", "Batticaloa", "Colombo",
    "Galle", "Gampaha", "Hambantota", "Jaffna", "Kalutara",
    "Kandy", "Kegalle", "Kilinochchi", "Kurunegala", "Mannar",
    "Matale", "Matara", "Monaragala", "Mullaitivu", "Nuwara Eliya",
    "Polonnaruwa", "<PERSON><PERSON><PERSON>", "Ratnapura", "Trincomalee", "Vav<PERSON><PERSON>"
  ];

  const value = {
    selectedLocation,
    setSelectedLocation,
    districts
  };

  return (
    <LocationContext.Provider value={value}>
      {children}
    </LocationContext.Provider>
  );
};
