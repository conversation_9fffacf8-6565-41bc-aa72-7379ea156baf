.job-board-container {
    display: flex;
    background-color: #ffffff;
    min-height: 100vh;
    width: 100%;
    max-width: none; /* Remove max-width constraint */
    padding: 20px calc(15px + 210px) 20px calc(2% + 210px); /* Add main-content-padded equivalent */
    font-family: 'Inter', sans-serif;
    box-sizing: border-box;
    margin: 0;
    flex-wrap: wrap;
  }
    @media (min-width: 1200px) {
    .job-board-container {
      padding: 20px calc(15px + 210px) 40px calc(2% + 210px);
    }
  }

  /* Responsive adjustments for JobBoard */
  @media (max-width: 1400px) {
    .job-board-container {
      padding: 20px calc(15px + 80px) 20px calc(2% + 80px);
    }
  }

  @media (max-width: 768px) {
    .job-board-container {
      padding: 20px calc(15px + 30px) 20px calc(2% + 30px);
    }
  }

  @media (max-width: 480px) {
    .job-board-container {
      padding: 20px calc(15px + 15px) 20px calc(2% + 15px);
    }
  }
  
  .filters-sidebar {
    width: 250px;
    padding: 20px;
    background-color: white;
    border-radius: 16px;
    margin-right: 20px;
    margin-left: 0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05), 0 2px 8px rgba(0, 0, 0, 0.08);
    border: 1px solid #f5f5f5;
    height: fit-content;
    flex-shrink: 0;
  }
  
  .filter-section {
    margin-bottom: 24px;
  }
  
  .filter-section h3 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 12px;
    color: #333;
    display: flex;
    align-items: center;
  }
  
  .filter-section h3 svg {
    margin-right: 8px;
    color: #8057ff;
  }
  
  .location-search, .category-search {
    margin-bottom: 12px;
  }
  
  .location-input, .category-input {
    width: 100%;
    padding: 10px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    font-size: 14px;
  }

  /* Job Type Dropdown Styles */
  .job-type-dropdown {
    margin-bottom: 12px;
  }

  /* Location Dropdown Styles */
  .dropdown-wrapper {
    position: relative;
    display: inline-block;
    width: 100%;
    margin-bottom: 12px;
  }

  .location-dropdown {
    width: 100%;
    padding: 10px 35px 10px 10px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    font-size: 14px;
    background-color: white;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    cursor: pointer;
    color: #333;
  }

  .location-dropdown:focus {
    outline: none;
    border-color: #8057ff;
    box-shadow: 0 0 0 2px rgba(128, 87, 255, 0.1);
  }

  .dropdown-icon-wrapper {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
    pointer-events: none;
    font-size: 12px;
  }

  .work-location-types {
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid #f0f0f0;
  }

  .select-wrapper {
    position: relative;
    display: inline-block;
    width: 100%;
  }

  .job-type-select {
    width: 100%;
    padding: 10px 35px 10px 10px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    font-size: 14px;
    background-color: white;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    cursor: pointer;
    color: #333;
  }

  .job-type-select:focus {
    outline: none;
    border-color: #8057ff;
    box-shadow: 0 0 0 2px rgba(128, 87, 255, 0.1);
  }

  .dropdown-icon {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
    pointer-events: none;
    font-size: 12px;
  }
  
  .filter-option {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
  }
  
  .filter-option label {
    margin-left: 8px;
    font-size: 14px;
    color: #666;
  }
  
  .salary-inputs {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
  }
  
  .salary-input-group {
    display: flex;
    align-items: center;
  }
  
  .salary-input-group label {
    margin-right: 6px;
    font-size: 14px;
    color: #666;
  }
  
  .salary-input-group input {
    width: 70px;
    padding: 6px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
  }
  
  .salary-input-group span {
    margin-left: 2px;
    color: #666;
  }
  
  .salary-range-slider {
    position: relative;
    height: 30px;
    margin-bottom: 10px;
  }
  
  .salary-range-slider input {
    position: absolute;
    width: 100%;
    height: 6px;
    background: none;
    pointer-events: none;
    
  }
  
  .salary-range-slider input::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #8057ff;
    cursor: pointer;
    pointer-events: auto;
    border: none;
  }
  
  .salary-range-display {
    text-align: center;
    font-size: 14px;
    color: #666;
  }
  
  .filter-button {
    width: 100%;
    padding: 12px;
    background-color: #8057ff;
    color: white;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    margin-bottom: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .filter-button svg {
    margin-right: 8px;
  }
  
  .job-alert-section {
    background-color: #e9e3ff;
    padding: 16px;
    border-radius: 12px;
  }
  
  .job-alert-section h3 {
    color: #333;
    font-size: 16px;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
  }
  
  .job-alert-section h3 svg {
    margin-right: 8px;
    color: #8057ff;
  }
  
  .job-alert-section p {
    color: #666;
    font-size: 14px;
    margin-bottom: 12px;
  }
  
  .set-alert-button {
    width: 100%;
    padding: 10px;
    background-color: #8057ff;
    color: white;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
  }
  
  .job-listings {
    flex: 1;
    min-width: 300px;
    margin-left: 0;
  }
  
  .job-search-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    color: #666;
    font-size: 14px;
  }

  /* Job search header left section with filter button */
  .job-search-left {
    display: flex;
    align-items: center;
    gap: 15px;
  }

  /* Filter toggle button for JobBoard */
  .filter-toggle-btn-jobboard {
    display: none; /* Hidden by default on desktop */
    background-color: #7743DB;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    align-items: center;
    gap: 6px;
    transition: background-color 0.2s;
  }

  .filter-toggle-btn-jobboard:hover {
    background-color: #6234c7;
  }
  
  .sort-select {
    padding: 8px 12px;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    font-size: 14px;
    color: #333;
  }
  
  .job-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
    margin-left: 0;
  }
  
  .ads-section {
    margin-top: 30px;
    margin-left: 0;
    background-color: #475be8;
    border-radius: 16px;
    height: 150px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    position: relative;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05), 0 2px 8px rgba(0, 0, 0, 0.08);
    border: 1px solid #f5f5f5;
  }
  
  .ads-banner {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding-left: 40px;
  }
  
  .ads-banner h2 {
    font-size: 28px;
    margin-bottom: 8px;
  }
  
  .ads-banner p {
    font-size: 18px;
  }
  
  .ads-banner::after {
    content: "Ads";
    position: absolute;
    left: 40px;
    bottom: 20px;
    font-size: 64px;
    opacity: 0.3;
  }

  /* Add these styles to your existing JobBoard.css file */

.salary-range-section {
    background-color: #fef2f6;
    border-radius: 12px;
    padding: 20px;
  }
  
  .salary-slider-container {
    position: relative;
    height: 40px;
    margin: 20px 0;
  }
  
  .salary-slider-track {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 100%;
    height: 6px;
    background-color: #e6e6e6;
    border-radius: 3px;
  }
  
  .salary-slider-progress {
    position: absolute;
    height: 100%;
    background-color: #bf5af2;
    border-radius: 3px;
  }
  
  .salary-range-input {
    position: absolute;
    top: 0;
    width: 100%;
    height: 100%;
    background: none;
    pointer-events: none;
    -webkit-appearance: none;
    z-index: 10;
  }
  
  .salary-range-input::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: white;
    border: 2px solid #bf5af2;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    pointer-events: auto;
    margin-top: -10px;
  }
  
  .salary-range-input::-moz-range-thumb {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: white;
    border: 2px solid #bf5af2;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    pointer-events: auto;
  }
  
  .salary-labels {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 14px;
    color: #444;
  }
  
  .salary-input-fields {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
  }
  
  .salary-text-input {
    width: 43%;
    padding: 12px;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    background-color: transparent;
    font-size: 14px;
    color: #444;
    text-align: center;
  }
  
  .salary-separator {
    font-size: 18px;
    color: #444;
  }
  
  .salary-filter-buttons {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }
  
  .apply-filter-button {
    width: 100%;
    padding: 12px;
    background-color: #bf5af2;
    color: white;
    border: none;
    border-radius: 10px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.2s;
  }
  
  .apply-filter-button:hover {
    background-color: #a346d1;
  }
  
  .reset-filter-button {
    width: 100%;
    padding: 12px;
    background-color: transparent;
    color: #888;
    border: none;
    border-radius: 10px;
    font-weight: 500;
    cursor: pointer;
    transition: color 0.2s;
  }
  
  .reset-filter-button:hover {
    color: #666;
  }

  /* Add this at the appropriate location in your CSS file */
.job-board-top-spacing {
  height: 80px; /* Adjust based on your navbar height */
  width: 100%;
}

/* Mobile Filter Sidebar */
.filter-sidebar-mobile {
  position: fixed;
  top: 0;
  right: -350px;
  width: 350px;
  height: 100vh;
  background-color: white;
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1), -4px 0 16px rgba(0, 0, 0, 0.08);
  border-left: 1px solid #f5f5f5;
  z-index: 1000;
  transition: right 0.3s ease;
  overflow-y: auto;
}

.filter-sidebar-mobile.open {
  right: 0;
}

.filter-sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px 20px 25px; /* Balanced padding with right side padding */
  border-bottom: 1px solid #eaeaea;
  background-color: #f9f5fa;
}

.filter-sidebar-header h3 {
  margin: 0;
  font-size: 18px;
  color: #333;
  display: flex;
  align-items: center;
  gap: 8px;
}

.close-sidebar-btn {
  background: none;
  border: none;
  font-size: 18px;
  color: #666;
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.close-sidebar-btn:hover {
  background-color: rgba(119, 67, 219, 0.1);
  color: #7743DB;
}

.filter-sidebar-content {
  padding: 20px 25px 20px 30px !important; /* Balanced padding with reduced left and added right padding */
}

/* Specific styling for mobile filter sections */
.filter-sidebar-mobile .filter-section {
  margin-left: 5px; /* Reduced margin for mobile filter sections */
  padding-left: 5px; /* Reduced padding for mobile filter sections */
  margin-right: 5px; /* Added right margin for mobile filter sections */
  padding-right: 5px; /* Added right padding for mobile filter sections */
}

.filter-sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
}

/* Mobile responsiveness */
@media (max-width: 992px) {
  .job-board-container {
    flex-direction: column;
    padding: 15px 10px 20px 2%;
  }
  
  .filters-sidebar {
    width: 100%;
    margin-right: 0;
    margin-left: 0;
    margin-bottom: 20px;
    order: 2;
    
  }
  
  .job-listings {
    order: 1;
    margin-bottom: 20px;
    margin-left: 0;
  }
  
  .job-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }

  /* Show filter button on mobile and tablet */
  .filter-toggle-btn-jobboard {
    display: flex !important;
  }
  
  .filters-sidebar {
    display: none; /* Hide desktop filter sidebar on smaller screens */
  }
}

@media (max-width: 576px) {
  .job-board-container {
    padding-left: 10px;
    padding-right: 10px;
  }
  .job-grid {
    grid-template-columns: 1fr;
  }
}

/* Loading overlay styles */
.loading-overlay {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40px 20px;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  margin: 20px 0;
  min-height: 200px;
}

.loading-overlay .spinner {
  font-size: 2rem;
  color: #4a6bff;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
