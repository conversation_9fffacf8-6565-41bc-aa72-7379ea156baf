const express = require('express');
const router = express.Router();
const companyController = require('../controllers/companyController');
const upload = require('../middleware/uploadMiddleware');

// GET - Get dashboard analytics for companies
router.get('/dashboard', companyController.getDashboardAnalytics);

// GET - Get all companies
router.get('/', companyController.getAllCompanies);

// GET - Get company by ID
router.get('/:company_id', companyController.getCompanyById);

// POST - Create a new company
router.post('/', upload.fields([
  { name: 'company_logo', maxCount: 1 }
]), companyController.createCompany);

// PUT - Update a company
router.put('/:company_id', upload.fields([
  { name: 'company_logo', maxCount: 1 }
]), companyController.updateCompany);

// DELETE - Delete a company
router.delete('/:company_id', companyController.deleteCompany);

module.exports = router;